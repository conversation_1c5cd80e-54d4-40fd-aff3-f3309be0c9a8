import * as DadosParticipante from '../exports';

interface IConsentimentoDadosParticipanteProps {
  editando: boolean;
  lgpdControls: DadosParticipante.IControlesLGPD;
  setLgpdControls: React.Dispatch<
    React.SetStateAction<DadosParticipante.IControlesLGPD>
  >;
}

export const ConsentimentoDadosParticipante: React.FC<
  IConsentimentoDadosParticipanteProps
> = ({ editando, lgpdControls, setLgpdControls }) => {
  return (
    <DadosParticipante.ConditionalRenderer condition={editando}>
      <DadosParticipante.S.LGPDContainer>
        <DadosParticipante.S.CheckboxLGPD
          checked={lgpdControls.dadosPessoaisParceiros}
          onChange={checked =>
            setLgpdControls({
              ...lgpdControls,
              dadosPessoaisParceiros:
                DadosParticipante.alterarValorCheckbox(checked),
            })
          }
          rightLabel={DadosParticipante.LABEL_CONSENTIMENTO_DADOS_CAIXA}
        />
        <DadosParticipante.S.CheckboxLGPD
          checked={lgpdControls.dadosPessoaisOutrosProdutos}
          onChange={checked =>
            setLgpdControls({
              ...lgpdControls,
              dadosPessoaisOutrosProdutos:
                DadosParticipante.alterarValorCheckbox(checked),
            })
          }
          rightLabel={DadosParticipante.LABEL_CONSENTIMENTO_DADOS_PARCEIROS}
        />
        <DadosParticipante.Text
          variant="text-standard-400"
          fontColor="content-danger-03"
        >
          {DadosParticipante.DISCLAIMER_CONSENTIMENTO}
        </DadosParticipante.Text>
      </DadosParticipante.S.LGPDContainer>
      <DadosParticipante.Divider />
    </DadosParticipante.ConditionalRenderer>
  );
};
