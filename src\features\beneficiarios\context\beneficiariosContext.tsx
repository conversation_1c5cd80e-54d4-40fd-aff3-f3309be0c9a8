import {
  PropsWithChildren,
  TBeneficiariosActions,
  createContext,
  TBeneficiariosState,
  useReducer,
  beneficiariosInitialState,
  beneficiariosReducer,
  ICertificadoCoberturas,
  criaBeneficiariosEstadoInicial,
} from '../exports';

export interface IBeneficiariosProvider extends PropsWithChildren {
  beneficios: ICertificadoCoberturas;
}

export const BeneficiariosContext = createContext<TBeneficiariosState>(
  beneficiariosInitialState,
);
export const BeneficiariosDispatchContext = createContext<
  React.Dispatch<TBeneficiariosActions>
>({} as React.Dispatch<TBeneficiariosActions>);

export const BeneficiariosProvider: React.FC<IBeneficiariosProvider> = ({
  children,
  beneficios,
}) => {
  const [state, dispatch] = useReducer(
    beneficiariosReducer,
    beneficios,
    criaBeneficiariosEstadoInicial,
  );

  return (
    <BeneficiariosContext.Provider value={state}>
      <BeneficiariosDispatchContext.Provider value={dispatch}>
        {children}
      </BeneficiariosDispatchContext.Provider>
    </BeneficiariosContext.Provider>
  );
};
