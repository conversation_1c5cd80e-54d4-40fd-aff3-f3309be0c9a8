import { TResponseFundosDestino } from '@src/shared/types/ITransferenciaEntreFundoResponse';
import { IResponseFundo } from '@src/corporativo/types/consultaCertificado/Response/IFundosPorCertificadoResponse';

export type TFundoDestinos = Partial<TResponseFundosDestino> & {
  transferenciaValor?: number;
};

export type TEtapasTransferencia = 'origem' | 'destino' | 'validacao';

export enum EEtapasTranferencia {
  DefinirFundosOrigem,
  DefinirFundosDestino,
  ValidarOperacao,
  DefinirAssinatura,
}

export enum EErroTransferencia {
  ValorMin,
  ValorRestante,
  SemFundosDestino,
  FluxoInvalido,
  CodeDestino,
  ComprovanteTransferencia,
}

export enum ETiposTransferencia {
  Parcial = 'P',
  Total = 'T',
}

export enum EDescricaoTiposTransferencia {
  TransferenciaParcial = 'parcial',
  TransferenciaTotal = 'total',
}

export type TReservasOrigem = {
  fundoId: string;
  staTipRetirada?: ETiposTransferencia.Total | ETiposTransferencia.Parcial;
  valorSolicitado: number;
};

export type TTransferenciaFundo = IResponseFundo;

type TTransferenciaDataContext = {
  confirmarTransferencia: boolean;
  fundosDestinos: Record<string, TFundoDestinos>;
  verMais: boolean;
  modalProsseguir: boolean;
  transferenciaRealizada: boolean;
  reservasOrigem: TReservasOrigem[];
  etapa: EEtapasTranferencia;
  assinatura: boolean;
  erro?: EErroTransferencia;
  distribuicaoValores: {
    somaEntreFundosDestino: number;
    distribuicaoRestante: number;
    somaIntencaoDistribuicaoOrigens: number;
  };
};

export type TTransferenciaContext = TTransferenciaDataContext & {
  setFundosDestinos: (fundosDestinos: Record<string, TFundoDestinos>) => void;
  setVerMais: (valor: boolean) => void;
  setModalProsseguir: (modalAberto: boolean) => void;
  setConfirmarTransferencia: (confirmar: boolean) => void;
  setTransferenciaRealizada: (realizada: boolean) => void;
  setReservasOrigem: (reservasOrigem: TReservasOrigem[]) => void;
  setEtapa: (etapa: EEtapasTranferencia) => void;
  setAssinatura: (assinatura: boolean) => void;
  setErro: (err?: EErroTransferencia) => void;
};

export type TTransferenciaProivder = {
  children: React.ReactNode;
};

export interface IPayloadComprovanteTransferencia {
  tipoRequisicao: string;
  idRequisicao: string;
}
