import {
  getTernaryResult,
  IconInfoOutlined,
  S,
  Text,
  ToolTip,
  TTableHeaderPorcentualProps,
  useBeneficiariosForm,
} from '../exports';

export const TableHeaderPorcentual: React.FC<TTableHeaderPorcentualProps> = ({
  coberturaId,
}) => {
  const { somarPorcentagemCobertura } = useBeneficiariosForm();
  return (
    <S.HeaderTooltip>
      Porcentual{' '}
      <ToolTip
        maxWidth="352px"
        text={
          (
            <>
              <Text variant="text-small-600">Porcentual</Text>
              <Text variant="text-small-600">
                Caso não seja informado 100% dos beneficiários, o percentual não
                indicado será direcionado aos herdeiros legais do Segurado.
              </Text>
            </>
          ) as unknown as string
        }
      >
        <IconInfoOutlined
          size="small"
          color={getTernaryResult(
            somarPorcentagemCobertura(coberturaId) !== 100,
            '#D87B00',
            '#005CA9',
          )}
        />
      </ToolTip>
    </S.HeaderTooltip>
  );
};
