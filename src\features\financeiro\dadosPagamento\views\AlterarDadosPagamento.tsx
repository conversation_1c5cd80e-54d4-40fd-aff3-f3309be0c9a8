import {
  SelectFormaPagamento,
  useAlterarDadosPagamento,
  AlertaMensagens,
  SelectContaBancaria,
  SelectOperacao,
  CamposNovaConta,
  SelectDiaDebito,
  TAlterarDadosPagamentoProps,
  Acoes,
  Match,
  SwitchCase,
  Grid,
  LoadingSpinner,
  checkIfAllItemsAreTrue,
} from '@src/features/financeiro/dadosPagamento/exports';

const AlterarDadosPagamento: React.FC<TAlterarDadosPagamentoProps> = ({
  onClose,
}) => {
  const { loading } = useAlterarDadosPagamento();

  return (
    <SwitchCase fallback={<LoadingSpinner />}>
      <Match when={checkIfAllItemsAreTrue([!loading])}>
        <Grid>
          <AlertaMensagens />
          <SelectFormaPagamento />
          <SelectContaBancaria />
        </Grid>

        <Grid>
          <SelectDiaDebito />
          <SelectOperacao />
        </Grid>

        <CamposNovaConta />
        <Acoes onClose={onClose} />
      </Match>
    </SwitchCase>
  );
};

export default AlterarDadosPagamento;
