import '@testing-library/jest-dom';
import { render, screen } from '@src/corporativo/tests/config';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import Beneficiarios from '../pages/Beneficiarios';
import { mockObterCoberturas } from '../mocks/respostaObterCoberturas';

jest.mock('@src/corporativo/components/Assinatura/Assinatura', () => () => (
  <div data-testid="assinatura-mock">Assinatura mock</div>
));

jest.mock('@cvp/componentes-posvenda', () => ({
  ...jest.requireActual('@cvp/componentes-posvenda'),
  useApiGatewayCvpInvoker: jest.fn((val: string) => {
    const defaultReturn = {
      loading: false,
      invocarApiGatewayCvpComToken: jest.fn(),
    };

    switch (val) {
      case PECOS.ModificarBeneficiarios:
        return {
          response: { sucesso: true },
          ...defaultReturn,
          invocarApiGatewayCvpComToken: jest.fn(() => {
            return Promise.resolve((resolve: typeof Promise.resolve) =>
              resolve({
                response: {
                  sucesso: true,
                },
              }),
            );
          }),
        };
      case PECOS.ObterCoberturas:
        return {
          response: {
            sucesso: true,
            entidade: {
              retorno: {
                beneficios: [mockObterCoberturas],
              },
            },
          },
          ...defaultReturn,
        };
      default:
        return {
          response: { sucesso: true },
          ...defaultReturn,
        };
    }
  }),
}));

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useBlocker: jest.fn(() => ({
    state: 'unblocked',
    proceed: jest.fn(),
    reset: jest.fn(),
  })),
}));

describe('Autorização cancelamento', () => {
  it('deve renderizar o componente de beneficiarios', () => {
    render(<Beneficiarios />);

    const form = screen.getByTestId('formulario-beneficiarios');

    expect(form).toBeInTheDocument();
  });
});
