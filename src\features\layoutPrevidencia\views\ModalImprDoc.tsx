import { ModalImprimirDocumento } from '@cvp/componentes-posvenda';
import {
  PRIMEIRO_SELECT_LABEL,
  TERCEIRO_SELECT_LABEL,
} from '@src/features/layoutPrevidencia/constants/imprDocModalTextos';
import { getSelectLabelSecond } from '@src/features/layoutPrevidencia/factory/modalImprDocFactory';
import useModalImprDoc from '@src/features/layoutPrevidencia/hook/useModalImprDoc';
import {
  EModalImprDocActionType,
  IModalImprDocProps,
} from '@src/features/layoutPrevidencia/types/modalImprDocTypes';
import { parseDateISOStringToAAAAMMDDString } from '@src/features/layoutPrevidencia/utils';
import { AlertaModal } from '@src/corporativo/components/AlertaModal';
import { useContext } from 'react';
import { MatrizAcessoContext } from '../exports';

const ModalImprDoc: React.FC<IModalImprDocProps> = ({
  modalImprDocState,
  modalImprDocDispatch,
  navigate,
}) => {
  const {
    disableButton,
    getModalImprDocMessage,
    isRequestLoading,
    onChangeDateFieldDataFim,
    onChangeDateFieldDataInicio,
    onChangeSelectPrimary,
    onChangeSelectSecondary,
    onChangeSelectThird,
    onGenerateDoc,
    showSecondSelect,
    showThirdSelect,
    thirdSelectType,
  } = useModalImprDoc(modalImprDocState, modalImprDocDispatch);
  const { modalImprimirDocumentosPrimeiroSelectOpcoes } =
    useContext(MatrizAcessoContext);

  return (
    <ModalImprimirDocumento
      selectLabel={PRIMEIRO_SELECT_LABEL}
      selectLabelSecond={getSelectLabelSecond(modalImprDocState)}
      selectLabelThird={TERCEIRO_SELECT_LABEL}
      isOpen={modalImprDocState.isModalImprDocOpen}
      message={getModalImprDocMessage()}
      onClose={() =>
        modalImprDocDispatch({ type: EModalImprDocActionType.FECHAR_MODAL })
      }
      options={modalImprimirDocumentosPrimeiroSelectOpcoes}
      optionsSecond={modalImprDocState.optionsSegundaOpcao}
      optionsThird={modalImprDocState.optionsTerceiraOpcao}
      SelectPrimaryValue={[modalImprDocState.selectPrimeiraOpcao.value]}
      SelectSecondaryValue={[modalImprDocState.selectSegundaOpcao.value]}
      selectDateFieldFromValue={parseDateISOStringToAAAAMMDDString(
        modalImprDocState.selectDataInicio.value,
      )}
      selectDateFieldToValue={parseDateISOStringToAAAAMMDDString(
        modalImprDocState.selectDataFim.value,
      )}
      SelectThirdValue={[modalImprDocState.selectTerceiraOpcao.value]}
      onChangeSelectPrimary={onChangeSelectPrimary}
      onChangeSelectSecondary={onChangeSelectSecondary}
      onChangeDateFieldFrom={onChangeDateFieldDataInicio}
      onChangeDateFieldTo={onChangeDateFieldDataFim}
      onChangeSelectThird={onChangeSelectThird}
      showSecondSelect={showSecondSelect}
      showThirdSelect={showThirdSelect}
      thirdSelectType={thirdSelectType}
      disableButton={disableButton}
      onGenerate={() => onGenerateDoc(navigate)}
      showSelectDateFieldFromError={
        !!modalImprDocState.mensagemErroSelectDataInicio
      }
      showSelectDateFieldToError={!!modalImprDocState.mensagemErroSelectDataFim}
      selectDateFieldFromErrorMessage={
        modalImprDocState.mensagemErroSelectDataInicio
      }
      selectDateFieldToErrorMessage={
        modalImprDocState.mensagemErroSelectDataFim
      }
      isLoading={isRequestLoading}
      showErrorAlert={!!modalImprDocState.mensagemErroObterPdf}
      customAlert={
        <AlertaModal
          variant="danger-01"
          mensagemAlerta={modalImprDocState.mensagemErroObterPdf}
        />
      }
    />
  );
};

export default ModalImprDoc;
