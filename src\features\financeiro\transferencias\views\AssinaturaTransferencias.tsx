import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/exports';

const AssinaturaTransferencias: React.FC = () => {
  const {
    dadosAssinatura,
    exibirAssinaturaInvalida,
    realizarAssinaturaCallback,
  } = TransferenciaEntreFundos.useRealizarAssinaturaTransferencia();

  return (
    <TransferenciaEntreFundos.Grid>
      <TransferenciaEntreFundos.GridItem xs="1/2">
        <TransferenciaEntreFundos.Text
          variant="heading-tiny-600"
          fontColor="content-neutral-05"
          marginTop="26,5px"
          marginBottom="26,5px"
        >
          {TransferenciaEntreFundos.ASSINATURA.AUTENTICACAO}
        </TransferenciaEntreFundos.Text>

        <TransferenciaEntreFundos.SwitchCase fallback={undefined}>
          <TransferenciaEntreFundos.Match when={exibirAssinaturaInvalida}>
            {TransferenciaEntreFundos.ASSINATURA.INVALIDA}
          </TransferenciaEntreFundos.Match>
        </TransferenciaEntreFundos.SwitchCase>

        <TransferenciaEntreFundos.Assinatura
          dados={dadosAssinatura}
          callback={realizarAssinaturaCallback}
        />
      </TransferenciaEntreFundos.GridItem>
    </TransferenciaEntreFundos.Grid>
  );
};

export default AssinaturaTransferencias;
