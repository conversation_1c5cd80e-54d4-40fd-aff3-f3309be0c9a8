import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';
import { TUseListarValoresPayload } from '@src/corporativo/types/listarValores/TUseListarValoresPayload';
import { IUseListarValoresRequest } from '@src/corporativo/types/listarValores/IUseListarValoresRequest';
import {
  TUseListarValoresResponse,
  TUseListarValoresResponseReturn,
} from '@src/corporativo/types/listarValores/TUseListarValoresResponse';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

export const useListarValores: TUseListarValoresPayload = payload => {
  const { loading, response } = useApiGatewayCvpInvoker<
    IUseListarValoresRequest,
    TUseListarValoresResponseReturn
  >(PECOS.ListarValores, {
    data: {
      cpf: tryGetValueOrDefault([payload?.cpf], ''),
      codConta: tryGetValueOrDefault([payload?.codConta], ''),
      valor: tryGetValueOrDefault([payload?.valor], ''),
    },
    autoFetch: true,
  });

  return {
    loading,
    response: tryGetValueOrDefault(
      [response?.entidade?.return],
      {} as TUseListarValoresResponse,
    ),
  };
};
