import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { IObterFundosPayload } from '@src/corporativo/types/financeiro/aporte/IObterFundosPayload';
import { IObterFundosResponse } from '@src/corporativo/types/financeiro/aporte/Response/IObterFundosResponse';
import { TUseObterFundosDistribuicao } from '@src/corporativo/types/financeiro/aporte/TUseObterFundosDistribuicao';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { useContext } from 'react';

export const useObterFundosDistribuicao = (): TUseObterFundosDistribuicao => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload = {
    cpfCnpj,
    numeroCertificado: certificadoAtivo?.certificadoNumero,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<Partial<IObterFundosPayload>, IObterFundosResponse>(
      PECOS.ObterFundos,
      {
        data: payload,
        autoFetch: false,
        handleResponse: { throwToastErrorBFF: false },
      },
    );

  return {
    loading,
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as IObterFundosResponse,
    ),
    invocarApiGatewayCvpComToken,
  };
};
