import styled from 'styled-components';
import { Checkbox } from '../exports';

export const Container = styled.div(({ theme }) => ({
  margin: `${theme.spacing.inset.small} 0`,
  padding: `0 ${theme.spacing.inset.smaller}`,
}));

export const LGPDContainer = styled.div`
  margin: 16px 0;
  display: flex;
  gap: 24px;
  flex-direction: column;
`;

export const CheckboxLGPD = styled(Checkbox)`
  align-items: center;
  font-weight: 500;
  font-family: inherit;
  font-size: 1rem;
  > button {
    margin: 0;
  }
`;
