import { CONSTS, useState } from '../exports';

export type TUseAliquotas = {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  handleAliquota: (aliquotaEscolhida: string) => void;
};

const useAliquotas = (
  setOpcaoTributacaoIrrf: (opcaoTributacaoIrrf: string) => void,
): TUseAliquotas => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const handleAliquota = (aliquotaEscolhida: string) => {
    if (aliquotaEscolhida) {
      setIsOpen(true);
      if (aliquotaEscolhida === CONSTS.ALIQUOTAS.REGRESSIVA.id) {
        setOpcaoTributacaoIrrf(CONSTS.ALIQUOTAS.REGRESSIVA.opcaoTributacaoIrrf);
      }
      setOpcaoTributacaoIrrf(CONSTS.ALIQUOTAS.PROGRESSIVA.opcaoTributacaoIrrf);
    }
  };
  return {
    isOpen,
    setIsOpen,
    handleAliquota,
  };
};

export default useAliquotas;
