import { useContext } from 'react';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IConsultarContribuicaoRegularPayload,
  IConsultarContribuicaoRegularResponse,
  IUseConsultarContribuicaoRegularRetorno,
} from '@src/corporativo/types/financeiro/resgate/IConsultarContribuicaoRegular';

export const useConsultarContribuicaoRegular =
  (): IUseConsultarContribuicaoRegularRetorno => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const codigoCertificado = tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    );

    const {
      response: dadosContribuicaoRegular,
      loading: isLoadingConsultaContribuicaoRegular,
      invocarApiGatewayCvpComToken: consultarContribuicaoRegular,
    } = useApiGatewayCvpInvoker<
      Partial<IConsultarContribuicaoRegularPayload>,
      IConsultarContribuicaoRegularResponse
    >(PECOS.ConsultarContribuicaoRegularResgate, {
      data: {
        codigoCertificado,
      },
    });

    return {
      dadosContribuicaoRegular: tryGetValueOrDefault(
        [dadosContribuicaoRegular?.entidade],
        {} as IConsultarContribuicaoRegularResponse,
      ),
      isLoadingConsultaContribuicaoRegular,
      consultarContribuicaoRegular,
    };
  };
