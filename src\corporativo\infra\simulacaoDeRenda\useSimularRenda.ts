import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  ISimularRendaBody,
  IUseSimularRenda,
} from '@src/corporativo/types/consulta/ISimularRenda';
import { ISimulacaoDeRendaResponse } from '@src/features/consulta/consultarSimulacaoDeRenda/types/ISimulacaoDeRenda';
import { useContext } from 'react';

const useSimularRenda = (): IUseSimularRenda => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    Partial<ISimularRendaBody>,
    ISimulacaoDeRendaResponse
  >(PECOS.SimularRenda, {
    data: {
      Cpf: String(getSessionItem('cpfCnpj')),
      NumeroCertificado: certificadoAtivo?.certificadoNumero,
    },
  });

  return { loading, invocarApiGatewayCvpComToken };
};

export default useSimularRenda;
