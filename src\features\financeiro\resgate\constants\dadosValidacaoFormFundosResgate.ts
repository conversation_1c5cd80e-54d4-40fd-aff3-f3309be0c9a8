import { tryGetMonetaryValueOrDefault } from '@src/features/financeiro/resgate/exports';

export const MENSAGENS_VALIDACAO_FORM_FUNDOS_RESGATE = {
  OBRIGATORIO: 'Valor a ser resgatado é obrigatório',
  INVALIDO: 'Valor inválido',
  ABAIXO_MINIMO: (min: number): string =>
    `O valor deve ser maior que ${tryGetMonetaryValueOrDefault(min)}`,
};

export const PATHS_VALIDACAO_FORM_FUNDOS_RESGATE = {
  valorRetirar: (codigoFundo: string): string =>
    `fundosParaResgate.${codigoFundo}.valorRetirar`,
};
