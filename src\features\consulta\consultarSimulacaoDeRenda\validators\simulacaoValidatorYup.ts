import {
  CAMPO_OBRIGATORIO,
  NORMALIZE_STRING,
  PRAZO_TIPO_DE_RENDA,
  REGEX_SIMULACAO_RENDA,
  TIPO_DE_RENDA,
  Yup,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const validarReversivelAoBeneficiarioSchema = Yup.object().shape({
  pctReversao: Yup.string().required(CAMPO_OBRIGATORIO),
  staGeneroConjuge: Yup.string().required(CAMPO_OBRIGATORIO),
  dthNascimentoConjuge: Yup.string().required(CAMPO_OBRIGATORIO),
});

const regexToSimulationRenda = {
  [TIPO_DE_RENDA.temporaria]: REGEX_SIMULACAO_RENDA.PRAZO_TEMPORARIA,
  [TIPO_DE_RENDA.prazo_certo]: REGEX_SIMULACAO_RENDA.PRAZO_PRAZO_CERTA,
  [TIPO_DE_RENDA.prazo_minimo]: REGEX_SIMULACAO_RENDA.PRAZO_MINIMO,
};
export const textSmulationRendaTemporariaPrazoCerto = {
  [TIPO_DE_RENDA.temporaria]: PRAZO_TIPO_DE_RENDA.TEMPORARIA,
  [TIPO_DE_RENDA.prazo_minimo]: PRAZO_TIPO_DE_RENDA.PRAZO_MINIMO,
  [TIPO_DE_RENDA.prazo_certo]: PRAZO_TIPO_DE_RENDA.PRAZO_CERTO,
};

export const validarPrazoSchema = (renda: string): Yup.ObjectSchema<object> =>
  Yup.object().shape({
    qtdPrazoAnos: Yup.string()
      .required(CAMPO_OBRIGATORIO)
      .matches(regexToSimulationRenda[renda]),
  });

export const validateAndNormalizeString = (text: string): string => {
  return text
    .normalize(NORMALIZE_STRING)
    .replace(/[\u0300-\u036f]/g, '')
    .toLocaleLowerCase();
};
