import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IObterBancosResponse {
  codigoBanco: number;
  nomeBanco: string;
}

export interface IUseObterBancosRetorno {
  listaBancos: IObterBancosResponse[];
  isLoadingListaBancos: boolean;
  obterBancos: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<IObterBancosResponse[]> | undefined>;
}
