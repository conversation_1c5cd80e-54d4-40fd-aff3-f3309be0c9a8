import { Utils, IHandleReponseResult } from '../exports';

export const checkSucessoSalvarDadosParticipante = (
  dados: IHandleReponseResult<unknown> | undefined,
): boolean =>
  Utils.checkIfAllItemsAreTrue([!!dados?.sucessoBFF, !!dados?.sucessoGI]);

export const checkErroSalvarDadosParticipante = (
  dados: IHandleReponseResult<unknown> | undefined,
): boolean => {
  return Utils.checkIfSomeItemsAreTrue([
    dados?.sucessoBFF === false,
    dados?.sucessoGI === false,
  ]);
};
