import * as Resgate from '@src/features/financeiro/resgate/exports';

export interface IParametrosPadraoCalculoAliquota {
  formik: Resgate.FormikProps<Resgate.IFormikValuesSimulacaoResgate>;
  resultadoCalculoResgateRestante: Resgate.ICalcularValorResgateRestanteRetorno;
  calcularResgate: (
    dynamicPayload?: Resgate.IObterPayloadCalcularResgateFactoryRetorno,
  ) => Promise<
    Resgate.IHandleReponseResult<Resgate.ICalcularResgateResponse> | undefined
  >;
  consultarResumoAliquota: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | Resgate.IHandleReponseResult<Resgate.IConsultarResumoAliquotaResponse>
    | undefined
  >;
  consultarDetalheCalculo: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | Resgate.IHandleReponseResult<Resgate.IConsultarDetalheCalculoResponse>
    | undefined
  >;
}
