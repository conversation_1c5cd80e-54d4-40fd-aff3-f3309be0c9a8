import { IFormikValuesSimulacaoResgate } from '@src/features/financeiro/resgate/exports';

export const INITIAL_STATE_FORMIK: IFormikValuesSimulacaoResgate = {
  tipoResgate: '',
  fundosParaResgate: [],
  aliquotaParaResgateSelecionada: '',
  motivoResgate: '',
  contaExistente: {
    nomeCanal: '',
    codigoBanco: '',
    tipoCanal: '',
    codigoContaBancaria: '',
    digitoAgencia: '',
    digitoConta: '',
    tipoContaBancaria: '',
    descricaoTipoContaBancaria: '',
    nomeBanco: '',
    numeroAgencia: '',
  },
  isNovaConta: false,
  novaConta: {
    banco: {
      value: '',
      label: '',
    },
    agencia: '',
    conta: '',
    digito: '',
    operacao: '',
  },
  fundosParaContribuicao: [],
};
