import { valuesSelectPrimeiraOpcao } from '@src/features/layoutPrevidencia/factory/modalImprDocFactory';
import { PREV_PERMISSIONS } from '@src/corporativo/factories/matrizAcesso/factoryPerfilPermissoes';
import {
  TConsultasComponentesPermissoes,
  TImprimirDocumentosPermissoes,
} from '@src/corporativo/types/matrizAcesso/IUseMatrizAcesso';

export const ALTERACOES_COMPONENTES_PERMISSOES = {
  alteracao_dia_vencimento: {
    label: 'Dia de Vencimento',
    path: 'alteracao-dia-vencimento',
    permissions: [PREV_PERMISSIONS.DIA_DE_VENCIMENTO_DO_PLANO],
  },
  alterar_forma_dados_pagamento: {
    label: 'Forma de Pagamento',
    path: 'alterar-forma-dados-pagamento',
    permissions: [PREV_PERMISSIONS.ALTERAR_DADOS_DE_PAGAMENTO],
  },
  alteracao_prazo_diferimento: {
    label: 'Prazo Diferimento',
    path: 'alteracao-prazo-diferimento',
    permissions: [PREV_PERMISSIONS.PRAZO_DE_TERMINO_DO_PLANO],
  },
  alterar_valor_contribuicao: {
    label: 'Valor de Contribuição',
    path: 'alterar-valor-contribuicao',
    permissions: [PREV_PERMISSIONS.ALTERAR_VALOR_DA_CONTRIBUICAO],
  },
  alteracao_regime_tributario: {
    label: 'Alterar Regime Tributário',
    path: 'alteracao-regime-tributario',
    permissions: [PREV_PERMISSIONS.ALTERAR_REGIME_TRIBUTARIO],
  },
  alteracao_dados_participante: {
    label: 'Alterar Dados do Segurado',
    path: 'alteracao-dados-participante',
    permissions: [PREV_PERMISSIONS.ENDERECO_DE_CORRESPONDENCIA__EDITAR],
  },
};

export const CONSULTAS_COMPONENTES_PERMISSOES: TConsultasComponentesPermissoes =
  {
    beneficiarios: {
      path: 'beneficiarios',
      permissions: [PREV_PERMISSIONS.BENEFICIARIOS_CADASTRADOS_EDITAR],
    },
    dados_plano: {
      path: 'dados-plano',
      permissions: [PREV_PERMISSIONS.INFORMACOES_DO_PLANO],
    },
    dados_participante: {
      path: 'dados-participante',
      permissions: [PREV_PERMISSIONS.ENDERECO_DE_CORRESPONDENCIA__CONSULTA],
    },
    contribuicoes: {
      path: 'contribuicoes',
      permissions: [
        PREV_PERMISSIONS.CONTRIBUICAO_ADICIONAL,
        PREV_PERMISSIONS.CONTRIBUICOES_EM_ABERTO,
        PREV_PERMISSIONS.CONTRIBUICOES_PAGAS,
      ],
    },
    historico_solicitacoes: {
      path: 'historico-solicitacoes',
      permissions: [PREV_PERMISSIONS.HISTORICO_DE_SOLICITACOES],
    },
    reajuste_anual_do_plano: {
      path: 'reajuste-anual-do-plano',
      permissions: [PREV_PERMISSIONS.REAJUSTE_E_REENQUADRAMENTO],
    },
    simulacao_renda: {
      path: 'simulacao-renda',
      permissions: [PREV_PERMISSIONS.SIMULADOR_DE_RENDA],
    },
  };

export const BOTOES_APOLICES_PERMISSOES = {
  botao_imprimir_documentos: {
    path: 'botao-imprimir-documentos',
    permissions: [],
  },
  botao_cancelar_certificado: {
    path: 'botao-cancelar-certificado',
    permissions: [],
  },
};

export const IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES: TImprimirDocumentosPermissoes =
  {
    [valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO]: {
      value: valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO,
      permissions: [PREV_PERMISSIONS.EXTRATO_DETALHADO],
    },
    [valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS]: {
      value: valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS,
      permissions: [PREV_PERMISSIONS.RENTABILIDADE_E_COTAS],
    },
    [valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL]: {
      value: valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL,
      permissions: [PREV_PERMISSIONS.QUITACAO_ANUAL],
    },
    [valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR]: {
      value: valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR,
      permissions: [],
    },
    [valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS]: {
      value: valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS,
      permissions: [PREV_PERMISSIONS.IMPOSTO_DE_RENDA],
    },
    [valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS]:
      {
        value:
          valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
        permissions: [PREV_PERMISSIONS.INFORMACOES_DOS_BENEFICIARIOS_CONSULTA],
      },
    [valuesSelectPrimeiraOpcao.EMISSAO_CERTIFICADO]: {
      value: valuesSelectPrimeiraOpcao.EMISSAO_CERTIFICADO,
      permissions: [PREV_PERMISSIONS.EMITIR_CERTIFICADO],
    },
  };

export const TIPO_TRANSFERENCIA_OPCOES_PERMISSOES = {
  [PREV_PERMISSIONS.TRANSFERENCIA_DE_FUNDOS]: {
    value: PREV_PERMISSIONS.TRANSFERENCIA_DE_FUNDOS,
    permissions: [PREV_PERMISSIONS.TRANSFERENCIA_DE_FUNDOS],
  },
  [PREV_PERMISSIONS.TRANSFERENCIA_EMISSAO]: {
    value: PREV_PERMISSIONS.TRANSFERENCIA_EMISSAO,
    permissions: [PREV_PERMISSIONS.TRANSFERENCIA_EMISSAO],
  },
  [PREV_PERMISSIONS.TRANSFERENCIA_CERTIFICADO]: {
    value: PREV_PERMISSIONS.TRANSFERENCIA_CERTIFICADO,
    permissions: [PREV_PERMISSIONS.TRANSFERENCIA_CERTIFICADO],
  },
};
