import {
  Grid,
  GridItem,
  styled,
} from '@src/features/financeiro/aporte/exports';

export const Container = styled.div(() => ({
  border: '1px solid rgba(208, 224, 227, 1)',
  borderRadius: '10px',
  height: 'auto',

  boxShadow:
    '0px 1px 3px 1px rgba(0, 0, 0, 0.15), 0px 1px 2px 0px rgba(0, 0, 0, 0.3)',

  '> div': {
    height: 'auto',
  },
}));

export const customStyles = {
  rows: {
    style: {
      minHeight: '80px',
    },
  },
};

export const ButtonContainer = styled('div')`
  width: 91px;
  height: 100%;
  align-items: center;
  justify-content: center;
`;

export const GridColumnContainer = styled(Grid)(() => ({
  flexDirection: 'column',
}));

export const GridItemContainer = styled(GridItem)(() => ({
  borderTop: '2px solid rgba(208, 224, 227, 1)',
}));
