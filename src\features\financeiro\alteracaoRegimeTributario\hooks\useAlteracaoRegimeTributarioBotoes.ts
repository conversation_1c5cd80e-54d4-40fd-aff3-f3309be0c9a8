import {
  CODIGO_REQUISICAO,
  converterBase64,
  getSessionItem,
  INavigateFunction,
  PrevidenciaContext,
  ROUTES,
  TIPO_DOCUMENTO,
  tipoEmailConstants,
  useAlteracaoRegimeTributarioContext,
  useContext,
  useNavigate,
} from '../exports';

type TUseAlteracaoRegimeTributarioBotoes = () => {
  confirmarAlteracaoRegimeTributario: boolean | 'indeterminate';
  navigate: INavigateFunction;
  handleImprimirComprovante: () => void;
};

const useAlteracaoRegimeTributarioBotoes: TUseAlteracaoRegimeTributarioBotoes =
  () => {
    const navigate = useNavigate();

    const { certificadoAtivo, setImpressao, setParametrosScroll } =
      useContext(PrevidenciaContext);

    const { confirmarAlteracaoRegimeTributario } =
      useAlteracaoRegimeTributarioContext();

    const cpfCnpj = String(getSessionItem('cpfCnpj'));

    const imprimirComprovante = (base64: string) => {
      const urlPdf = URL.createObjectURL(converterBase64(base64));
      const rota = window.location.pathname;
      const valorScroll = window.scrollY;

      setImpressao({
        tipoDocumento: TIPO_DOCUMENTO.COMPROVANTE,
        tipoEmail: tipoEmailConstants.COMPROVANTE_RESGATE,
        urlPdf,
        parametrosEnvio: {
          numeroResgate: '',
          codigoRequisicao: CODIGO_REQUISICAO.ALTERACAO_REGIME_TRIBUTARIO,
          cpfCnpj,
          numeroCertificado: certificadoAtivo?.certificadoNumero,
        },
        base64: [base64],
      });

      setParametrosScroll({
        rota,
        valorScroll,
      });

      setParametrosScroll({
        rota,
        valorScroll,
      });

      navigate(ROUTES.IMPRIMIR);
    };

    const handleImprimirComprovante = () => {
      const base64 = '';

      imprimirComprovante(base64);
    };
    return {
      confirmarAlteracaoRegimeTributario,
      navigate,
      handleImprimirComprovante,
    };
  };

export default useAlteracaoRegimeTributarioBotoes;
