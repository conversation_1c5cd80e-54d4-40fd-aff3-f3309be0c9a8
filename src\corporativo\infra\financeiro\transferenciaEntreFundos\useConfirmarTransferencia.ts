import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';
import { IResponseConfirmarTransferencia } from '@src/corporativo/types/consultaCertificado/Response/IConfirmarTransferencia';

type TPayload = {
  cpf: string;
};

type TRequestConfirmarTransferencia = {
  numeroCertificado: string;
  idTransferencia: string;
};

export type TUseConfirmarTransferencia = {
  loading: boolean;
  response: IHandleReponseResult<IResponseConfirmarTransferencia> | undefined;
  fetchData: (
    request: TRequestConfirmarTransferencia,
  ) => Promise<
    IHandleReponseResult<IResponseConfirmarTransferencia> | undefined
  >;
};

function useConfirmarTransferencia(): TUseConfirmarTransferencia {
  const payload = {
    cpf: getSessionItem('cpfCnpj'),
  };

  const { response, invocarApiGatewayCvpComToken, loading } =
    useApiGatewayCvpInvoker<TPayload, IResponseConfirmarTransferencia>(
      'PECO_ConfirmarTransferencia',
      {
        data: { cpf: String(payload.cpf) },
        autoFetch: false,
      },
    );

  async function confirmarTransferencia(
    parametros: TRequestConfirmarTransferencia,
  ) {
    return invocarApiGatewayCvpComToken(parametros);
  }

  return {
    response,
    fetchData: confirmarTransferencia,
    loading,
  };
}

export default useConfirmarTransferencia;
