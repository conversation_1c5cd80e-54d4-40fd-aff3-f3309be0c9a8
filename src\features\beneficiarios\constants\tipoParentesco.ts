export const TIPO_PARENTESCO = {
  1: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  2: '<PERSON><PERSON><PERSON>(a)',
  3: '<PERSON><PERSON>',
  4: '<PERSON><PERSON><PERSON>',
  5: '<PERSON><PERSON><PERSON><PERSON><PERSON> (a)',
  6: '<PERSON><PERSON><PERSON> (ã)',
  7: '<PERSON><PERSON><PERSON><PERSON> (a)',
  8: '<PERSON><PERSON> (a)',
  9: 'Primo (a)',
  10: 'Avô (ó)',
  11: '<PERSON><PERSON>',
  12: 'Entead<PERSON> (a)',
  13: 'Neto (a)',
  14: '<PERSON><PERSON><PERSON><PERSON> (a)',
  15: 'B<PERSON>v<PERSON> (ó)',
  16: '<PERSON><PERSON>',
} as const;

export const TIPOS_PARENTESCO = Object.keys(TIPO_PARENTESCO).map(key => ({
  text: TIPO_PARENTESCO[key as unknown as keyof typeof TIPO_PARENTESCO],
  value: key,
}));
