export type { ITiposConsentimento } from './consultar/IConsultarConsentimentoResponse';
export type { IConsultarDadosParticipanteVidaRequest } from './consultar/IConsultarDadosParticipanteVidaRequest';
export type { IDadosNomeSocialResponse } from './consultar/IDadosNomeSocialResponse';
export type { IDadosParticipanteResponse } from './consultar/IDadosParticipanteResponse';
export type { IFormDadosParticipante } from './consultar/IFormDadosParticipante';
export type { IEnderecoParticipanteResponse } from './consultar/IEnderecoParticipanteResponse';
export type { ITelefoneParticipanteResponse } from './consultar/ITelefoneParticipanteResponse';
export type { TUseConsultarDadosParticipante } from './consultar/TUseConsultarDadosParticipante';
export type { TUseObterNomeSocial } from './consultar/TUseObterNomeSocial';
export type { TUseBuscarEnderecoCep } from './consultar/TUseBuscarEnderecoCep';
export type { IDadosBuscarEnderecoCepResponse } from './consultar/IDadosBuscarEnderecoCepResponse';

export type { IUseAtualizarDadosParticipanteResponse } from './alterar/IUseAtualizarDadosParticipanteResponse';
export type { IUseAtualizarDadosParticipanteRequest } from './alterar/IUseAtualizarDadosParticipanteRequest';
export type { IAtualizarDadosParticipanteResponse } from './alterar/IAtualizarDadosParticipanteResponse';
export type { IAtualizarDadosParticipantePayload } from './alterar/IAtualizarDadosParticipantePayload';

export type { IControlesLGPD } from './IControlesLGPD';
export type { IEndereco } from './IEndereco';
