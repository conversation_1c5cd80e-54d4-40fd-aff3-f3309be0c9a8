import {
  ITooltipTableRow,
  TableColumn,
  Text,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const COLUMNS_TABLE_TOOLTIP: TableColumn<ITooltipTableRow>[] = [
  {
    id: 'prazoDeAcumulacao',
    name: 'Prazo de Acumulacão',
    cell: row => <Text variant="text-small-700">{row.prazoDeAcumulacao}</Text>,
    sortable: false,
    width: '50%',
  },
  {
    id: 'aliquotasRegressivas',
    name: 'Alíquotas Regressivas',
    cell: row => (
      <Text variant="text-small-700">{row.aliquotasRegressivas}</Text>
    ),
    sortable: false,
    width: '50%',
  },
];

export const DATA_TABLE_TOOLTIP = [
  {
    prazoDeAcumulacao: 'Até 2 anos',
    aliquotasRegressivas: '35%',
  },
  {
    prazoDeAcumulacao: 'Até 2 a 4 anos',
    aliquotasRegressivas: '30%',
  },
  {
    prazoDeAcumulacao: 'Até 6 a 8 anos',
    aliquotasRegressivas: '20%',
  },
  {
    prazoDeAcumulacao: 'Até 10 anos',
    aliquotasRegressivas: '15%',
  },
  {
    prazoDeAcumulacao: 'Acima de 10 anos',
    aliquotasRegressivas: '10%',
  },
];
