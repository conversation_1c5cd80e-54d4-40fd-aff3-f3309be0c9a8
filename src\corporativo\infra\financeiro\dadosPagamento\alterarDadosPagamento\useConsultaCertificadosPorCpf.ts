import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IRequestDadosCertificadoPorCpf,
  IResponseDadosCertificadosPorCpf,
} from '@src/features/financeiro/types/DadosCertificado';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';

export const useConsultaCertificadosPorCpf = () => {
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj');

  const {
    response: dadosCertificado,
    loading: loadingDadosCertificado,
    invocarApiGatewayCvpComToken: consultarCertificadoPorCpf,
  } = useApiGatewayCvpInvoker<
    IRequestDadosCertificadoPorCpf,
    IResponseDadosCertificadosPorCpf[]
  >(PECOS.ObterCertificadoPorCpf, {
    data: {
      cpfCnpj: cpfCnpjSession,
    },
    autoFetch: true,
  });

  return {
    dadosCertificado: tryGetValueOrDefault([dadosCertificado?.entidade], []),
    loadingDadosCertificado,
    consultarCertificadoPorCpf,
  };
};
