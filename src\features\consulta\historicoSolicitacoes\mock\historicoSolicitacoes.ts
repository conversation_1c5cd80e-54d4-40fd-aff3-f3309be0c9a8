export const mockedHistoricoSolicitacoes = {
  entidade: [
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '69.67',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Central de Relacionamento',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319674',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319711',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'EM PAGAMENTO',
      idRequisicao: '15319712',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319713',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319714',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319715',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319716',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319717',
      detalhesEvento: '',
    },
    {
      contaId: '9753694',
      codigoRequisicao: 'Contribuição Adicional/Aporte (Operação)',
      tipoRequisicao: 'APORTE',
      valorRequisicao: '190',
      dataRequisicao: '2023-05-18T00:00:00-03:00',
      canalRequisicao: 'Agência',
      statusRequisicao: 'CONCLUIDA',
      idRequisicao: '15319718',
      detalhesEvento: '',
    },
  ],
};
