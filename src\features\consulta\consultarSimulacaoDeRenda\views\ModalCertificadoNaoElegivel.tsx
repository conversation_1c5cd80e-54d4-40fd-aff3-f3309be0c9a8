import {
  Button,
  Dialog,
  IModalCertificadoNaoElegivel,
  Text,
  TEXTS_MODAL_CERTIFICADO_NAO_ELEGIVEL,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const ModalCertificadoNaoElegivel: React.FC<
  IModalCertificadoNaoElegivel
> = ({ open, setOpenModalCertificado, tipoDeRegime }) => {
  return (
    <Dialog open={open} onOpenChange={() => setOpenModalCertificado(!open)}>
      <Dialog.Content>
        <Dialog.Header variant="highlight">
          <Text variant="heading-small-600">
            {TEXTS_MODAL_CERTIFICADO_NAO_ELEGIVEL.title}
          </Text>
        </Dialog.Header>
        <Dialog.Body>
          <Text variant="text-big-400">
            {TEXTS_MODAL_CERTIFICADO_NAO_ELEGIVEL.text} {tipoDeRegime}
          </Text>
        </Dialog.Body>
        <Dialog.Footer>
          <Button
            variant="primary"
            onClick={() => setOpenModalCertificado(!open)}
          >
            Confirmar
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};
