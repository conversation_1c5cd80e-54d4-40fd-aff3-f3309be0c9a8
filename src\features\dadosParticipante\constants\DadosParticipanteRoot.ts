export const DEFAULT_EMPTY_API_PARTICIPANTE = '0';

export const QTD_MAXIMA_DIGITOS_CEP = 8;

export const TEXTO_LOADING = 'Carregando dados do participante...';

export const ACEITAR_SMS = {
  SIM: 'S',
  NAO: 'N',
};

export const TIPO_EMAIL = {
  PESSOAL: 'PS',
  TRABALHO: 'TR',
};

export const LOCAL_TELEFONE = {
  CELULAR: 'O',
  COMERCIAL: 'T',
};

export const TELEFONE_PRINCIPAL = {
  SIM: 'S',
  NAO: 'N',
};

export const EXTENSAO_NUMERO = {
  PADRAO: '0',
};

export const TIPO_TELEFONE = {
  CELULAR: 'C',
  TELEFONE_DIRETO: 'D',
  FAX: 'F',
  OUTRO: 'O',
  CENTRAL: 'R',
  TELEX: 'T',
  TEL_FAX: 'X',
};

export const CODIGO_PAIS = {
  brasil: '105',
};

export const ERRO_ATUALIZAR_DADOS_PARTICIPANTE =
  'Erro na atualização dos dados do participante.';

export enum EDadosParticipanteActionKind {
  EDITAR_DADOS_PARTICIPANTE = 'EDITAR_DADOS_PARTICIPANTE',
  SALVAR_DADOS_PARTICIPANTE = 'SALVAR_DADOS_PARTICIPANTE',
  ALTERAR_ASSINATURA_VALIDA = 'ALTERAR_ASSINATURA_VALIDA',
  ALTERAR_CONSETIMENTOS = 'ALTERAR_CONSENTIMENTOS',
  CANCELAR_EDICAO_DADOS_PARTICIPANTE = 'CANCELAR_EDICAO_DADOS_PARTICIPANTE',
  CONSULTAR_CEP = 'CONSULTAR_CEP',
}
