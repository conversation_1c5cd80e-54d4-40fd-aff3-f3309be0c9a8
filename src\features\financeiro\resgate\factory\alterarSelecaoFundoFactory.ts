import {
  getTernaryResult,
  IListarFundosParaResgateFundosDisponiveis,
  IAlterarSelecaoFundoFactory,
} from '@src/features/financeiro/resgate/exports';

/**
 * Atualiza a seleção de um fundo específico em uma lista de fundos para resgate.
 *
 * Quando o código do fundo bate com o `codigoFundo` informado, ele inverte o valor de `selecionado`
 * e limpa ou mantém o `valorRetirar` com base nesse estado.
 *
 * @param {IAlterarSelecaoFundoFactory} params - Parâmetros contendo a lista de fundos e o código do fundo a alterar.
 * @param {IListarFundosParaResgateFundosDisponiveis[]} params.fundos - Lista atual de fundos disponíveis para resgate.
 * @param {string} params.codigoFundo - Código do fundo que deve ter seu estado de seleção alterado.
 * @returns {IListarFundosParaResgateFundosDisponiveis[]} Uma nova lista de fundos com o fundo selecionado atualizado.
 */
export const alterarSelecaoFundoFactory = ({
  fundos,
  codigoFundo,
}: IAlterarSelecaoFundoFactory): IListarFundosParaResgateFundosDisponiveis[] => {
  return fundos.map(fundo => {
    const isFundoSelecionado: boolean = fundo.codigoFundo === codigoFundo;

    const fundoAtualizado: IListarFundosParaResgateFundosDisponiveis = {
      ...fundo,
      selecionado: !fundo.selecionado,
      tipoResgate: fundo.tipoResgate,
      valorRetirar: getTernaryResult(
        !!fundo.selecionado,
        '',
        fundo.valorRetirar,
      ),
    };

    return getTernaryResult(isFundoSelecionado, fundoAtualizado, fundo);
  });
};
