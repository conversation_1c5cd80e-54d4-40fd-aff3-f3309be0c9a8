import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import {
  IAtualizarDadosParticipantePayload,
  IAtualizarDadosParticipanteResponse,
} from '..';

export interface IUseAtualizarDadosParticipanteResponse {
  response:
    | IHandleReponseResult<IAtualizarDadosParticipanteResponse>
    | undefined;
  loading: boolean;
  invocarApiGatewayCvpComToken: (
    payload: IAtualizarDadosParticipantePayload,
  ) => Promise<IHandleReponseResult<IAtualizarDadosParticipanteResponse>>;
}
