import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useSucessoResgate = (): Resgate.IUseSucessoResgate => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  const statusResgate =
    resgateFeatureData?.dadosRetornoConfirmacaoResgate?.statusResgate;

  const [isOpenModalPendencia, setIsOpenModalPendencia] =
    Resgate.useState<boolean>(false);

  const deveRenderizarMensagemSucesso: boolean =
    Resgate.checkIfSomeItemsAreTrue([
      statusResgate === Resgate.STATUS_RESGATE.SOLICITADO,
      statusResgate === Resgate.STATUS_RESGATE.VALIDADO,
    ]);

  const deveRenderizarMensagemPendente: boolean =
    statusResgate === Resgate.STATUS_RESGATE.PENDENTE;

  const deveRenderizarMensagemAguardandoAssinatura: boolean =
    statusResgate === Resgate.STATUS_RESGATE.AGUARDANDO_ASSINATURA;

  const deveRenderizarTabelaResumo: boolean = Resgate.checkIfSomeItemsAreTrue([
    statusResgate === Resgate.STATUS_RESGATE.SOLICITADO,
    statusResgate === Resgate.STATUS_RESGATE.VALIDADO,
    statusResgate === Resgate.STATUS_RESGATE.AGUARDANDO_ASSINATURA,
  ]);

  const controlarModalPendencia = (isPendencia?: boolean): void => {
    if (isPendencia) {
      setIsOpenModalPendencia(isPendencia);
      return;
    }

    setIsOpenModalPendencia(prevState => !prevState);
  };

  Resgate.useEffect(() => {
    controlarModalPendencia(statusResgate === Resgate.STATUS_RESGATE.PENDENTE);
  }, [resgateFeatureData.dadosRetornoConfirmacaoResgate]);

  return {
    isOpenModalPendencia,
    deveRenderizarMensagemSucesso,
    deveRenderizarMensagemPendente,
    deveRenderizarMensagemAguardandoAssinatura,
    deveRenderizarTabelaResumo,
    controlarModalPendencia,
  };
};
