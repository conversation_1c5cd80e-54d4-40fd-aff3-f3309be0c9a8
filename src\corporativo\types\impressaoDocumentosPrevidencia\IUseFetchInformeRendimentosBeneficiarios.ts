import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IUseFetchInformeRendimentosBeneficiariosPayload {
  Cpf: string;
  AnoBase: string;
  TipoPessoa: string;
  Email: string;
}

export interface IInformeRendimentosBeneficiariosEntity {
  informe: string;
}

export interface IUseFetchInformeRendimentosBeneficiariosReturn {
  isLoadingInformeRendimentosBeneficiarios: boolean;
  fetchInformeRendimentosBeneficiarios: (
    dynamicPayload?: IUseFetchInformeRendimentosBeneficiariosPayload,
  ) => Promise<
    IHandleReponseResult<IInformeRendimentosBeneficiariosEntity> | undefined
  >;
}
