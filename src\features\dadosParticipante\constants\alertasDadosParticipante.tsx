import {
  IAlertaIdentificado,
  IconCheckCircleRound,
  IconWarningRound,
} from '../exports';

export const ALERTAS: Record<string, IAlertaIdentificado> = {
  SUCESSO_ALTERAR_DADOS_PARTICIPANTE: {
    identificador: 'dados-participante',
    message: 'Dados do segurado editados com sucesso!',
    variant: 'success-01',
    icon: <IconCheckCircleRound color="#127527" size="medium" />,
  },
  ERRO_ALTERAR_DADOS_PARTICIPANTE: {
    identificador: 'dados-participante',
    message: 'Erro ao alterar os dados do participante',
    variant: 'danger-01',
    icon: <IconWarningRound color="#900000" size="medium" />,
  },
  DADOS_PARTICIPANTE_SEM_ALTERACAO: {
    identificador: 'dados-participante-envio',
    message:
      'Os dados do participante permanecem iguais, se já estão certos cancele a operação de editar',
    variant: 'warning-01',
    icon: <IconWarningRound color="#977203" size="medium" />,
  },
};
