import { useContext } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import {
  ICalcularResgatePayload,
  ICalcularResgateResponse,
  IUseCalcularResgateRetorno,
} from '@src/corporativo/types/financeiro/resgate/ICalcularResgate';

export const useCalcularResgate = (): IUseCalcularResgateRetorno => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const codigoCertificado = tryGetValueOrDefault(
    [certificadoAtivo?.certificadoNumero],
    '',
  );

  const {
    response: dadosCalculoResgate,
    loading: isLoadingCalculoResgate,
    invocarApiGatewayCvpComToken: calcularResgate,
  } = useApiGatewayCvpInvoker<
    Partial<ICalcularResgatePayload>,
    ICalcularResgateResponse
  >(PECOS.CalcularResgate, {
    data: { codigoCertificado },
  });

  return {
    dadosCalculoResgate: tryGetValueOrDefault(
      [dadosCalculoResgate?.entidade],
      {} as ICalcularResgateResponse,
    ),
    isLoadingCalculoResgate,
    calcularResgate,
  };
};
