import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';
import { CONSENTIMENTO_CONSTANTES } from '@src/corporativo/constants/dadosParticipante/consentimento';
import { IConsultarConsentimentoResponse } from '@src/corporativo/types/dadosParticipante/consultar/IConsultarConsentimentoResponse';

type TPayload = {
  cpf: string | null;
  numeroCertificado: string;
  macroProcesso: number;
  subProcesso: number;
  numConsentimentoNegocio: string;
};

type TUseObterConsentimento = (numeroCertificado: string) => {
  response: IHandleReponseResult<IConsultarConsentimentoResponse> | undefined;
  loading: boolean;
};

export const useObterConsentimento: TUseObterConsentimento =
  numeroCertificado => {
    const payload: TPayload = {
      cpf: getSessionItem('cpfCnpj'),
      macroProcesso: CONSENTIMENTO_CONSTANTES.MACRO_PROCESSO,
      subProcesso: Number(CONSENTIMENTO_CONSTANTES.SUB_PROCESSO_CAIXA),
      numConsentimentoNegocio:
        CONSENTIMENTO_CONSTANTES.NUM_CONSENTIMENTO_NEGOCIO,
      numeroCertificado,
    };

    const { response, loading } = useApiGatewayCvpInvoker<
      TPayload,
      IConsultarConsentimentoResponse
    >('PECO_ConsultarConsentimento', {
      data: payload,
      autoFetch: true,
    });

    return {
      response,
      loading,
    };
  };
