import {
  I<PERSON><PERSON><PERSON>,
  TUseModalNovoBeneficiario,
  useState,
  useContext,
  BeneficiariosDispatchContext,
  getTernaryResult,
  validarCpf,
  ALERTA_CPF_INVALIDO,
  ALERTA_CPF_EXISTENTE,
  EBeneficiariosActionKind,
  useBeneficiariosForm,
} from '../exports';

export const useModalNovoBeneficiario: TUseModalNovoBeneficiario = ({
  coberturaId,
  beneficiarioId,
  adicionarBeneficiario,
}) => {
  const [alertaModal, setAlertaModal] = useState<IAlerta | null>(null);
  const { values, editarCobertura } = useBeneficiariosForm();

  const { beneficiarios } = values[coberturaId];
  const beneficiariosDispatch = useContext(BeneficiariosDispatchContext);

  const beneficarioEditado = beneficiarios.find(
    beneficiario => beneficiario.idBeneficiario === beneficiarioId,
  );

  const [numCpf, setNumCpf] = useState(
    getTernaryResult(
      !!beneficarioEditado,
      beneficarioEditado?.numCpf,
      '',
    ) as string,
  );
  const [sexo, setSexo] = useState(
    getTernaryResult(!!beneficarioEditado, beneficarioEditado?.sexo ?? '', ''),
  );

  const handleSubmit = (e: React.FormEvent) => {
    const isValid = validarCpf(numCpf);
    if (!isValid) {
      setAlertaModal(ALERTA_CPF_INVALIDO);
      e.preventDefault();
      return;
    }
    if (
      !beneficarioEditado &&
      beneficiarios.findIndex(
        beneficiario => beneficiario.numCpf === numCpf,
      ) !== -1
    ) {
      setAlertaModal(ALERTA_CPF_EXISTENTE);
      e.preventDefault();
      return;
    }
    adicionarBeneficiario(numCpf, sexo);
    editarCobertura(coberturaId);
    beneficiariosDispatch({
      type: EBeneficiariosActionKind.EDITAR_BENEFICIARIOS,
    });
    setNumCpf('');
    setSexo('');
  };

  return {
    alertaModal,
    setAlertaModal,
    handleSubmit,
    numCpf,
    setNumCpf,
    sexo,
    setSexo,
  };
};
