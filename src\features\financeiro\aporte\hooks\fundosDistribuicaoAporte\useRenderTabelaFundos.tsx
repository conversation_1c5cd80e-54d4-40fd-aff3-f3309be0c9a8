import {
  checkIfSomeItemsAreTrue,
  getTernaryResult,
  useAporteContext,
  TUseRenderTabelaFundos,
  useDesabilitarFundo,
  useState,
  tryGetValueOrDefault,
  TabelaFundosAporte,
  IDadosTabelaFundos,
  IFundosAporte,
} from '@src/features/financeiro/aporte/exports';

export interface IValorContribuicao {
  valorDistribuido: Record<string, string>;
  valoresMinimos: Record<string, string>;
}

type TUseRenderTabelaFundosPayload = {
  dadosTabelaFundos: IDadosTabelaFundos;
  isChecked?: boolean;
  tipoFundo: string;
};

const useRenderTabelaFundos = ({
  dadosTabelaFundos,
  isChecked,
  tipoFundo,
}: TUseRenderTabelaFundosPayload): TUseRenderTabelaFundos => {
  const [quantidadeFundosSelecionados, setQuantidadeFundosSelecionados] =
    useState<number>(0);

  const { fundoSelecionado, setFundoSelecionado } = useAporteContext();

  const quantidadeMaximaSelecao = tryGetValueOrDefault(
    [dadosTabelaFundos.qntMaxFundoPermitida],
    '',
  );

  const fundosChecked = (checked: boolean | string) => {
    setQuantidadeFundosSelecionados(prev =>
      getTernaryResult(
        checkIfSomeItemsAreTrue([checked === true, checked === 'true']),
        prev + 1,
        prev - 1,
      ),
    );
  };

  const handleFundoSelecionado = (
    checked: boolean | string,
    fundo: IFundosAporte,
  ) => {
    fundosChecked(checked);

    if (checked) {
      setFundoSelecionado(prev => [...prev, fundo]);
    } else {
      setFundoSelecionado(prev =>
        prev.filter(item => item.fundoId !== fundo.fundoId),
      );
    }
  };

  const handleDisableFundos = useDesabilitarFundo()(
    tipoFundo,
    quantidadeFundosSelecionados,
    quantidadeMaximaSelecao,
    fundoSelecionado,
    isChecked,
  );

  const handleDistribuirValor = (value: string, fundoId: string) => {
    setFundoSelecionado(prev =>
      prev.map(i => {
        return getTernaryResult(
          i.fundoId === fundoId,
          {
            ...i,
            valorContribuicao: value,
            tipoFundo,
          },
          i,
        );
      }),
    );
  };

  return dadosTabelaFundos.dadosFundos.map(fundo =>
    TabelaFundosAporte({
      fundo,
      handleFundoSelecionado,
      handleDisableFundos,
      handleDistribuirValor,
    }),
  );
};

export default useRenderTabelaFundos;
