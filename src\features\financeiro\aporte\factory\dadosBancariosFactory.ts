import {
  CONTA_DEBITO,
  DIGITO_AGENCIA_DEFAULT,
  enumNomeBancos,
  enumNumeroBancos,
  ICadastroNovaConta,
  IDadosBancariosAporte,
  IObterDadosBancariosResponse,
  obterNumeroContaSemOperacao,
  obterNumeroOperacao,
  obterOperacao,
  tryGetValueOrDefault,
} from '@src/features/financeiro/aporte/exports';

export const dadosBancariosResponseFactory = (
  dados: IObterDadosBancariosResponse | undefined,
): IDadosBancariosAporte[] =>
  tryGetValueOrDefault(
    [
      dados?.dados.map(item => ({
        canalId: item.canalId,
        conta: `${obterNumeroContaSemOperacao(item.numeroConta)}-${
          item.digitoConta
        }`,
        operacao: obterOperacao(obterNumeroOperacao(item.numeroConta)),
        tipoPagamentoId: item.tipoPagamentoId,
        descricaoPagamento: item.descricaoPagamento,
        dataPagamento: item.dataPagamento,
        metodoPagamento: item.metodoPagamento,
        tipoContaBanco: item.tipoContaBanco,
        numeroBanco: item.numeroBanco,
        nomeBanco: item.nomeBanco,
        numeroAgencia: item.numeroAgencia,
        digitoAgencia: item.digitoAgencia,
        numeroConta: item.numeroConta,
        digitoConta: item.digitoConta,
        banco: `[${item.numeroBanco}] ${item.nomeBanco}`,
      })),
    ],
    [],
  );

export const dadosBancariosAdicionadosFactory = (
  dados: ICadastroNovaConta,
): IDadosBancariosAporte => {
  return {
    canalId: '',
    conta: `${dados.contaBancaria}-${dados.digito}`,
    operacao: obterOperacao(dados.operacao),
    tipoPagamentoId: '',
    descricaoPagamento: '',
    dataPagamento: '',
    metodoPagamento: '',
    tipoContaBanco: '',
    numeroBanco: enumNumeroBancos.CAIXA_ECONOMICA,
    nomeBanco: enumNomeBancos.CAIXA_ECONOMICA,
    numeroAgencia: dados.agencia,
    digitoAgencia: DIGITO_AGENCIA_DEFAULT,
    numeroConta: `${dados.operacao}${dados.contaBancaria}`,
    digitoConta: dados.digito,
    banco: `[${enumNumeroBancos.CAIXA_ECONOMICA}] ${enumNomeBancos.CAIXA_ECONOMICA}`,
  };
};

export const dadosBancariosTextFactory = (dados: IDadosBancariosAporte[]) => [
  {
    id: CONTA_DEBITO.novaConta,
    text: CONTA_DEBITO.novaConta,
  },
  ...dados.map(item => ({
    id: item.canalId,
    text: `${item.numeroAgencia}-${item.numeroConta} - ${item.operacao}`,
  })),
];
