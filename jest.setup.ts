/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
import '@testing-library/jest-dom';

jest.mock('./src/corporativo/infra/config/api/axiosConfig', () => ({
  API_BASE_URL: 'http://mocked.api.url',
}));

// setupTests.ts

declare global {
  interface GlobalThis {
    AppConfig: {
      MFE_ENV?: string;
      [key: string]: unknown;
    };
  }
}

(global as any).AppConfig = {
  MFE_ENV: 'development',
};
