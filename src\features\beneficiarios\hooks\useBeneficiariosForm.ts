import {
  TFormBeneficiarios,
  useFormikContext,
  FormikContextType,
} from '../exports';

type TUseBeneficiariosForm = () => FormikContextType<TFormBeneficiarios> & {
  somarPorcentagemCobertura: (coberturaId: string) => number;
  verificarCoberturaEditando: (coberturaId: string) => boolean;
  editarCobertura: (coberturaId: string, val?: boolean) => void;
  cancelarEdicaoCobertura: (coberturaId: string) => void;
};

export const useBeneficiariosForm: TUseBeneficiariosForm = () => {
  const formik = useFormikContext<TFormBeneficiarios>();

  const somarPorcentagemCobertura = (coberturaId: string) => {
    return formik.values[coberturaId].beneficiarios.reduce(
      (acc, beneficiario) => acc + Number(beneficiario.porcentagem),
      0,
    );
  };

  const verificarCoberturaEditando = (coberturaId: string) => {
    return formik.values[coberturaId].editando;
  };

  const editarCobertura = (coberturaId: string, val = true) => {
    formik.setFieldValue(`${coberturaId}.editando`, val);
  };

  const cancelarEdicaoCobertura = (coberturaId: string) => {
    formik.resetForm({
      values: {
        ...formik.values,
        [coberturaId]: formik.initialValues[coberturaId],
      },
    });
  };

  return {
    ...formik,
    somarPorcentagemCobertura,
    verificarCoberturaEditando,
    editarCobertura,
    cancelarEdicaoCobertura,
  };
};
