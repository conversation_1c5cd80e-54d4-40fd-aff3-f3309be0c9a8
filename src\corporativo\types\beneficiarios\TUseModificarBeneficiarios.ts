import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { TAdicionarBeneficiario, TAlterarBeneficiario } from '.';

export type TUseModificarBeneficiarios = () => {
  loading: boolean;
  invocarApiGatewayCvpComToken: (
    data: TUseModificarBeneficiariosRequest,
  ) => Promise<IHandleReponseResult<unknown> | undefined>;
};

export type TUseModificarBeneficiariosRequest = {
  adicionarBeneficiarios: Array<TAdicionarBeneficiario>;
  alterarBeneficiarios: Array<TAlterarBeneficiario>;
  idBeneficio: string;
  idPlano: string;
  Cpf?: string;
  NumeroCertificado?: string;
};
