import { TNumeroContaECodigoOperacao } from '../types/DadosContaBancaria';

export const obterCodigoOperacaoENumeroConta = (
  numeroConta: string,
): TNumeroContaECodigoOperacao => {
  if (numeroConta.length < 12) {
    return {
      numeroConta: numeroConta.substring(3),
      operacao: numeroConta.substring(0, 3),
    };
  }

  return {
    numeroConta: numeroConta.substring(4),
    operacao: numeroConta.substring(0, 4),
  };
};
