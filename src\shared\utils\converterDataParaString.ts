/**
 * Converte uma data (string ou objeto Date) para o formato 'YYYY-MM-DD'.
 *
 * @param data - Data no formato string ISO, objeto Date, ou nulo/undefined.
 * @returns Uma string no formato 'YYYY-MM-DD' ou uma string vazia se o valor for inválido.
 */
export const converterDataParaString = (
  date?: string | Date | null,
): string => {
  if (typeof date === 'string') return date.split('T')[0];

  if (date && typeof date.getMonth === 'function')
    return date.toISOString().split('T')[0];

  return '';
};
