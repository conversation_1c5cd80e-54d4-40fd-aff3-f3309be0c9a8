export interface IEfetuarAporteResponse {
  mensagemMudancaPerfil: string;
  numeroSolicitacao: string;
  faturaId: string;
  boletoPdf: string;
  trabalhoId: string;
  urlRelatorioPasta: string;
  dadosBoleto: IDadosBoleto;
}

export interface IDadosBoleto {
  linhaDigitavel: string;
  dataVencimento: string;
  cnpjBeneficiario: string;
  agenciaCedente: string;
  nossoNumero: string;
  valorCobrado: string;
}
