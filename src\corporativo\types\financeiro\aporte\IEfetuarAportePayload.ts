import { IPagamento } from '@src/features/financeiro/aporte/exports';

export interface IEfetuarAportePayload {
  numeroCertificado: string | undefined;
  cpfCnpj: string;
  multiFundo: boolean;
  pagamento: IPagamento;
  fundos: IFundosAporteRequest[];
  faturaImpressa: boolean;
  indPep: boolean;
  origem: string;
  descricaoOrigem: string;
  tipoPep: string;
  invoicePrintInd: boolean;
}

export interface IFundosAporteRequest {
  reservaId: string;
  fundoId: string;
  valorContribuicao: string;
}
