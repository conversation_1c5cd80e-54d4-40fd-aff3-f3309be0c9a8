import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { IObterExtratoUnificadoPayload } from '@src/shared/types/IObterExtratoUnificadoPayload';
import { IObterExtratoUnificadoResponse } from '@src/shared/types/ObterExtratoUnificado/Response/IObterExtratoUnificadoResponse';
import { TUseObterExtratoUnificado } from '@src/shared/types/ObterExtratoUnificado/TUseObterExtratoUnificado';

export const useObterExtratoUnificado = (): TUseObterExtratoUnificado => {
  const parseDateStringToDate = (date?: string | Date | null) => {
    if (typeof date === 'string') return date.split('T')[0];

    if (date && typeof date.getMonth === 'function')
      return date.toISOString().split('T')[0];

    return '';
  };
  const payload = {
    cpf: String(getSessionItem('cpfCnpj')),
    dataFinal: parseDateStringToDate(new Date()),
    dataInicial: parseDateStringToDate(new Date()),
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IObterExtratoUnificadoPayload,
      IObterExtratoUnificadoResponse
    >(PECOS.ObterExtratoUnificado, {
      data: {
        cpf: payload.cpf,
        dataFinal: payload.dataFinal,
        dataInicial: payload.dataInicial,
      },
      autoFetch: false,
      cache: true,
      cacheKey: `${payload.cpf}`,
    });

  return {
    loading,
    invocarApiGatewayCvpComToken,
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as IObterExtratoUnificadoResponse,
    ),
  };
};
