import { Svg } from '@cvp/design-system-caixa';
import { ISvgProps } from '@cvp/design-system-caixa/dist/atoms/Svg';

const IconSave: React.FC<ISvgProps> = ({ size }) => {
  return (
    <Svg height="16px" width="16px" viewBox="0 0 16 16" fill="none" size={size}>
      <path
        d="M11.6667 0.5H1.66667C0.741667 0.5 0 1.25 0 2.16667V13.8333C0 14.75 0.741667 15.5 1.66667 15.5H13.3333C14.25 15.5 15 14.75 15 13.8333V3.83333L11.6667 0.5ZM13.3333 13.8333H1.66667V2.16667H10.975L13.3333 4.525V13.8333ZM7.5 8C6.11667 8 5 9.11667 5 10.5C5 11.8833 6.11667 13 7.5 13C8.88333 13 10 11.8833 10 10.5C10 9.11667 8.88333 8 7.5 8ZM2.5 3H10V6.33333H2.5V3Z"
        fill="white"
      />
    </Svg>
  );
};

export default IconSave;
