import { IEndereco, Utils } from '../exports';

export const montarDadosEndereco = (
  objetoOrigem: Record<string, string>,
): IEndereco => ({
  bairro: objetoOrigem.bairro,
  cep: objetoOrigem.cep,
  cidade: objetoOrigem.cidade,
  complemento: objetoOrigem.complemento,
  endereco: objetoOrigem.endereco,
  estado: objetoOrigem.uf,
  numero: objetoOrigem.numero,
  idEndereco: Utils.tryGetValueOrDefault([objetoOrigem.idEndereco], ''),
  idCidade: Utils.tryGetValueOrDefault([objetoOrigem.idCidade], ''),
  tipoRua: Utils.tryGetValueOrDefault([objetoOrigem.tipoRua], ''),
  tipoEndereco: Utils.tryGetValueOrDefault([objetoOrigem.tipoEndereco], ''),
  enderecoPadrao: Utils.tryGetValueOrDefault([objetoOrigem.enderecoPadrao], ''),
});
