import {
  Container,
  checkIfSomeItemsAreTrue,
  ComprovanteAporte,
  ContaDebitoAporte,
  EEtapasAporte,
  FiltrosTabelaAporte,
  FundosDistribuicaoAporte,
  Match,
  ModalConfirmarAporte,
  ModalVerificarCamposAporte,
  NovaContaDebitoAporte,
  OrigemRecursosAporte,
  useFluxoTelasAporte,
  SwitchCase,
} from '@src/features/financeiro/aporte/exports';

const FluxoTelasAporte: React.FC = () => {
  const { etapa, loadingFundos, loadingExtratoUnificado } =
    useFluxoTelasAporte();

  return (
    <SwitchCase>
      <Match
        when={checkIfSomeItemsAreTrue([
          etapa === EEtapasAporte.FiltrosTabelaAporte,
          etapa === EEtapasAporte.FundosDistribuicaoAporte,
          etapa === EEtapasAporte.OrigemRecursosAporte,
          etapa === EEtapasAporte.ModalVerificarCamposAporte,
          etapa === EEtapasAporte.ModalConfirmarAporte,
        ])}
      >
        <Container>
          <FiltrosTabelaAporte />

          <FundosDistribuicaoAporte
            loadingFundos={loadingFundos}
            loadingExtratoUnificado={loadingExtratoUnificado}
          />
        </Container>

        <OrigemRecursosAporte />

        <ModalVerificarCamposAporte />

        <Match when={etapa === EEtapasAporte.ModalConfirmarAporte}>
          <ModalConfirmarAporte />
        </Match>
      </Match>

      <Match when={etapa === EEtapasAporte.ContaDebitoAporte}>
        <ContaDebitoAporte />
      </Match>

      <Match when={etapa === EEtapasAporte.NovaContaDebitoAporte}>
        <NovaContaDebitoAporte />
      </Match>

      <Match when={etapa === EEtapasAporte.ComprovanteAporte}>
        <ComprovanteAporte />
      </Match>
    </SwitchCase>
  );
};

export default FluxoTelasAporte;
