export type TResponseContribuicoes = {
  numCobranca: string;
  codStatusCobranca: string;
  descStatusCobranca: string;
  codMeioPagamento: string;
  dataVencimento: string;
  dataBaixa: string;
  valorCobrado: number;
  valorBaixado: number;
  permiteEmissaoBoleto: string;
  permiteReprocessar: string;
  permiteImpressao: string;
  linhaDigitavel: string;
  dataDentroLimite: boolean;
};

export type TPayloadContribuicoes = {
  numeroCertificado: string;
  Cpf: string;
  DataVencimentoInicial: string;
  DataVencimentoFinal: string;
};

export type TResponseSegundaViaBoleto = {
  return: string;
};

export type TPayloadSegundaViaBoleto = {
  cpf: string;
  numeroCobranca?: string;
};

export type TDadosPagamento = {
  tipoPagamentoId: string;
  tipoContaBanco: string;
  numeroBanco: string;
  numeroAgencia: string;
  digitoAgencia?: string;
  numeroConta: string;
  digitoConta: string;
  canalId?: string;
};

export interface IResponseResponsavelFinanceiro {
  cpf: string;
  pessoaNome: string;
  idPessoa: string;
  indPessoaFisica: string;
  idPessoaFisica: string;
}
export type TCanalDados = {
  canal: string;
  isDisabled: boolean;
};

export type TNovaConta = {
  diaVencimento: string;
  formaPagamento: string;
  numeroAgencia: string;
  numeroConta: string;
  digitoConta: string;
  operacao: string;
};
