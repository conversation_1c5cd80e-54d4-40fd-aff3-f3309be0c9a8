import { IListarFundosParaResgateSaldo } from '@src/corporativo/types/financeiro/resgate/IListarFundosParaResgate';
import { IMapearDadosSelecaoAliquotaFactoryRetorno } from '@src/corporativo/types/financeiro/resgate/IMapearDadosSelecaoAliquotaFactory';

export interface IMontarResumoAliquotaFactory {
  tipoAliquota: string;
  dadosSelecaoAliquota: IMapearDadosSelecaoAliquotaFactoryRetorno | undefined;
  saldo: IListarFundosParaResgateSaldo;
}

export interface IMontarResumoAliquotaFactoryRetorno {
  label: string;
  value: string | undefined;
  mask: string;
}
