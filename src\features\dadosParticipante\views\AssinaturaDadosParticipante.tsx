import * as DadosParticipante from '../exports';

export const AssinaturaDadosParticipante: React.FC = () => {
  const { certificadoAtivo } = DadosParticipante.React.useContext(
    DadosParticipante.PrevidenciaContext,
  );
  const dispatch = DadosParticipante.React.useContext(
    DadosParticipante.DadosParticipanteDispatchContext,
  );
  const { assinaturaValida, validarAssinatura } =
    DadosParticipante.useValidarAssinatura();
  const { invocarApiGatewayCvpComToken } =
    DadosParticipante.useRegistrarTokenAssinaturaCaixa();

  const assinaturaCallback = (
    response: DadosParticipante.IAssinaturaResponse,
  ) => {
    DadosParticipante.Utils.setSessionItem(
      'assinaturaResponse',
      JSON.stringify(response),
    );
    invocarApiGatewayCvpComToken(response);
    const responseAssinaturaValida = validarAssinatura(response);
    dispatch({
      type: DadosParticipante.EDadosParticipanteActionKind
        .ALTERAR_ASSINATURA_VALIDA,
      assinaturaValida: responseAssinaturaValida,
    });
  };

  return (
    <>
      <DadosParticipante.SwitchCase fallback={null}>
        <DadosParticipante.Match
          when={
            !assinaturaValida &&
            DadosParticipante.Utils.checkIfAllItemsAreTrue([
              !!certificadoAtivo?.numeroApolice,
            ])
          }
        >
          Não foi possível carregar a assinatura.
        </DadosParticipante.Match>
      </DadosParticipante.SwitchCase>
      <DadosParticipante.Assinatura
        dados={{
          cpfCnpj: '',
          numeroCertificado: DadosParticipante.Utils.tryGetValueOrDefault(
            [certificadoAtivo?.certificadoNumero],
            '',
          ),
        }}
        callback={assinaturaCallback}
      />
    </>
  );
};
