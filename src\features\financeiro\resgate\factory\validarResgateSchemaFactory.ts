import {
  Yup,
  IValidarResgateSchemaFactory,
} from '@src/features/financeiro/resgate/exports';

/**
 * Criar o schema de validação para o resgate
 *
 * Esta função cria e retorna um schema de validação Yup para os dados de resgate,
 * definindo regras como campos obrigatórios e formatos esperados.
 *
 * @returns {Yup.ObjectSchema<IValidarResgateSchemaFactory>} Schema de validação para o formulário de resgate
 */
export const validarResgateSchemaFactory =
  (): Yup.ObjectSchema<IValidarResgateSchemaFactory> => {
    return Yup.object().shape({
      tipoResgate: Yup.string().required('Campo obrigatório'),
    });
  };
