import {
  IMapearSelectParams,
  tryGetValueOrDefault,
  SelectItem,
} from '@src/features/financeiro/resgate/exports';

/**
 * Mapear uma lista de itens para o formato de Select
 *
 * @template T - Tipo dos itens da lista de origem
 * @param {Object} params - Parâmetros para mapear itens para SelectItem
 * @param {Array<T>} params.lista - Lista de itens a serem mapeados
 * @param {Function} params.getText - Função para extrair o texto (label) de cada item
 * @param {Function} params.getValue - Função para extrair o valor (value) de cada item
 * @returns {SelectItem[]} Array de itens formatados para uso em componentes Select
 */
export const mapearSelectFormFactory = <T>({
  lista,
  getText,
  getValue,
}: IMapearSelectParams<T>): SelectItem[] => {
  const valorSelect = lista?.map(item => ({
    text: getText(item),
    value: getValue(item),
  }));

  return tryGetValueOrDefault([valorSelect], []);
};
