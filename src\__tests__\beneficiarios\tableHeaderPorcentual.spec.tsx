import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@cvp/design-system-caixa';
import * as Beneficiarios from '@src/features/beneficiarios/exports';

jest.mock('@src/corporativo/infra/config/api/axiosConfig', () => ({
  API_BASE_URL: 'http://mocked.api.url',
}));

jest.mock('@cvp/design-system-caixa', () => ({
  ...jest.requireActual('@cvp/design-system-caixa'),
  ToolTip: jest.fn(({ children }) => <div>{children}</div>),
  IconInfoOutlined: jest.fn(({ color, size }) => (
    <div data-testid="icon" style={{ color, fontSize: size }} />
  )),
}));

describe('TableHeaderPorcentual', () => {
  const renderWithTheme = (component: React.ReactNode) =>
    render(<ThemeProvider>{component}</ThemeProvider>);

  it('deve renderizar o componente com o texto "Porcentual"', () => {
    jest.spyOn(Beneficiarios, 'useBeneficiariosForm').mockReturnValue({
      somarPorcentagemCobertura: jest.fn().mockReturnValue(100),
    } as unknown as ReturnType<typeof Beneficiarios.useBeneficiariosForm>);
    renderWithTheme(<Beneficiarios.TableHeaderPorcentual coberturaId="1" />);

    expect(screen.getByText('Porcentual')).toBeInTheDocument();
  });
});
