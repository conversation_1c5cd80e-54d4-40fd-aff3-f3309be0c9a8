export interface ICalcularResgateResponse {
  dadosEncargo: ICalcularResgateDadosEncargo;
}

export interface ICalcularResgateDadosEncargo {
  codigoEmpresa: string;
  numeroResgate: string;
  codigoOperacao: string;
  valorTotalSolicitacao: string;
  valorCorrecao: string;
  percentualCorrecao: string;
  valorSaida: string;
  percentualSaida: string;
  valorIrSolicitacao: string;
  percentualIrSolicitacao: string;
  valorTaxaSaida: string;
  percentualTaxaSaida: string;
  valorBaseIr: string;
  valorSaldoPrincipal: string;
  valorSaldo: string;
  valorLiquidoSolicitacao: string;
  valorReserva: string;
  nomeParticipante: string;
  codigoCertificado: string;
  valorExcedenteFinanceiro: string;
  aliquotaResgate: string;
}
