import { useMemo, useState } from 'react';
import { TransferenciaContext } from '@src/corporativo/context/financeiro/transferencias/TransferenciaContext';
import {
  calculaSomaDistribuicaoEntreFundos,
  calculaValorRestante,
  calculaSomaEntreOrigens,
} from '@src/corporativo/utils/calculaValores';
import {
  TFundoDestinos,
  TTransferenciaContext,
  TTransferenciaProivder,
  TReservasOrigem,
  EErroTransferencia,
  EEtapasTranferencia,
} from '@src/corporativo/types/transferencias';

export const TransferenciaProvider = ({ children }: TTransferenciaProivder) => {
  const [erro, setErro] = useState<EErroTransferencia>();
  const [etapa, setEtapa] = useState<EEtapasTranferencia>(
    EEtapasTranferencia.DefinirFundosOrigem,
  );
  const [reservasOrigem, setReservasOrigem] = useState<TReservasOrigem[]>([]);
  const [verMais, setVerMais] = useState(false);
  const [modalProsseguir, setModalProsseguir] = useState(false);
  const [confirmarTransferencia, setConfirmarTransferencia] = useState(false);
  const [transferenciaRealizada, setTransferenciaRealizada] = useState(false);
  const [assinatura, setAssinatura] = useState(false);
  const [fundosDestinos, setFundosDestinos] = useState<
    Record<string, TFundoDestinos>
  >({} as Record<string, TFundoDestinos>);

  const distribuicaoValores = useMemo(() => {
    const somaEntreFundosDestino =
      calculaSomaDistribuicaoEntreFundos(fundosDestinos);
    return {
      somaEntreFundosDestino,
      somaIntencaoDistribuicaoOrigens: calculaSomaEntreOrigens(reservasOrigem),
      distribuicaoRestante: calculaValorRestante(
        somaEntreFundosDestino,
        reservasOrigem,
      ),
    };
  }, [fundosDestinos, reservasOrigem]);

  const memoizedValues = useMemo<TTransferenciaContext>(
    () => ({
      reservasOrigem,
      setReservasOrigem,
      fundosDestinos,
      setFundosDestinos,
      verMais,
      setVerMais,
      modalProsseguir,
      setModalProsseguir,
      confirmarTransferencia,
      setConfirmarTransferencia,
      transferenciaRealizada,
      setTransferenciaRealizada,
      etapa,
      setEtapa,
      assinatura,
      setAssinatura,
      erro,
      setErro,
      distribuicaoValores,
    }),
    [
      fundosDestinos,
      verMais,
      modalProsseguir,
      confirmarTransferencia,
      transferenciaRealizada,
      reservasOrigem,
      etapa,
      assinatura,
      erro,
    ],
  );

  return (
    <TransferenciaContext.Provider value={memoizedValues}>
      {children}
    </TransferenciaContext.Provider>
  );
};
