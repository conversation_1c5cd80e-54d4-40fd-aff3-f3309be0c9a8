import {
  MultiAccordion,
  TextField as OriginalTextField,
  Table,
} from '@cvp/design-system-caixa';
import styled from 'styled-components';

export const AccordionItem = styled(MultiAccordion.Item)`
  min-height: 106px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0px 2px 8px 1px #00000026;
  margin: 0 0.3rem;
  &:nth-child(4) {
    margin-bottom: 0.5rem;
  }
`;

export const Label = styled(OriginalTextField)`
  border: none;
  outline: none;
  pointer-events: none;
`;
export const TrasferenciaEntreFundosContainer = styled.div`
  padding: 0.688rem;
  border-radius: 10px;
  border: 1px solid #d0e0e3;
  box-shadow: 0px 2px 8px 1px #00000026;
  margin: 0.1rem 0.2rem 0.3rem;
`;

export const TableTranferenciaEntreFundos = styled(Table)`
  border-radius: 0;

  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: #ebf1f2;
      border: none;

      .rdt_TableCol_Sortable {
        font-weight: 400;
        color: #64747a;
      }
    }
  }

  .rdt_TableRow {
    min-height: 90px;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: #d0e0e3;
    margin-bottom: 0;
  }

  .rdt_TableCell > div {
    color: #404b52;
    text-overflow: initial;
    white-space: normal;
    overflow: initial;
  }
`;
