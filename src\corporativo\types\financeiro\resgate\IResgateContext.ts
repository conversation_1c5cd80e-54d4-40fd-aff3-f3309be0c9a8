import { IMapearContasExistentesFactoryReturn } from '@src/corporativo/types/financeiro/resgate/IMapearContasExistentesFactory';
import { IConsultarContribuicaoRegularResponse } from '@src/corporativo/types/financeiro/resgate/IConsultarContribuicaoRegular';
import { IDadosRetornoConfirmacaoResgate } from '@src/corporativo/types/financeiro/resgate/IDadosRetornoConfirmacaoResgate';
import { IConsultarTiposPagamentoResponse } from '@src/corporativo/types/financeiro/resgate/IConsultarTiposPagamento';
import { IListarMotivosResgateResponse } from '@src/corporativo/types/financeiro/resgate/IListarMotivosResgate';
import { IObterResumoAliquotaSelecionadaRetorno } from '@src/corporativo/types/financeiro/resgate/IObterResumoAliquotaSelecionada';
import { IMapearDadosSelecaoAliquotaFactoryRetorno } from '@src/corporativo/types/financeiro/resgate/IMapearDadosSelecaoAliquotaFactory';
import { IObterBancosCorporativoXPrevFactoryRetorno } from '@src/shared/types/IObterBancosCorporativoXPrevFactory';

export interface IResgateProvider {
  children: React.ReactNode;
}

export interface IResgateContext {
  etapa: EEtapasResgate;
  handleEtapa: (etapaAtual: EEtapasResgate) => void;
  resgateFeatureData: IResgateContextData;
  handleResgateFeatureData: (resgateData: IResgateContextData) => void;
}

export enum EEtapasResgate {
  SIMULACAO = 'simulacaoResgate',
  SOLICITACAO = 'solicitacaoResgate',
  SUCESSO = 'sucessoResgate',
}

export interface IResgateContextData {
  dadosSelecaoAliquota?: IMapearDadosSelecaoAliquotaFactoryRetorno;
  hasDadosSimulacao?: boolean;
  dadosBancarios?: IObterBancosCorporativoXPrevFactoryRetorno[];
  listaMotivosResgate?: IListarMotivosResgateResponse[];
  tiposPagamento?: IConsultarTiposPagamentoResponse;
  contribuicaoRegular?: IConsultarContribuicaoRegularResponse;
  numeroResgateConsolidado?: string;
  resumoAliquotaSelecionada?: IObterResumoAliquotaSelecionadaRetorno;
  listaContasExistentes?: IMapearContasExistentesFactoryReturn[];
  dadosRetornoConfirmacaoResgate?: IDadosRetornoConfirmacaoResgate;
}
