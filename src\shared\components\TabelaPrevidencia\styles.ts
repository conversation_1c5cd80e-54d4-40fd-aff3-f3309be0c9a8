import { Table } from '@cvp/design-system-caixa';
import styled from 'styled-components';

export const DataTable = styled(Table)`
  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: #edf4f6;
      border: none;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;

      .rdt_TableCol_Sortable {
        font-weight: 600;
      }
    }
  }

  .rdt_TableBody {
    .rdt_TableRow:nth-child(2n) {
      background-color: #edf4f6;
    }
  }
`;
