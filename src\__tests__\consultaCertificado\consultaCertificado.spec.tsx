import { render, screen } from '@src/corporativo/tests/config';
import { ICertificadoPrevidenciaResponse } from '@src/shared/types/ICertificadoPrevidenciaResponse';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import { IPagamentoRegularPrevidencia } from '@src/features/financeiro/dadosPagamento/exports';
import { IPessoaPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IPessoaPrevidencia';
import * as HookNomeSocial from '@src/corporativo/infra/dadosParticipante/UseObterNomeSocial';
import { IProdutoCertificadoPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IProdutoCertificadoPrevidencia';
import ListaCertificados from '../../features/layoutPrevidencia/views/ListaCertificados';

jest.mock('@src/corporativo/infra/config/api/axiosConfig', () => ({
  API_BASE_URL: 'http://mocked.api.url',
}));

describe('ListaCertificados', () => {
  jest.spyOn(HookNomeSocial, 'useObterNomeSocial').mockReturnValue({
    invocarApiGatewayCvpComToken: jest.fn().mockResolvedValue({ entidade: [] }),
    loading: false,
  });

  const certificadosMock: ICertificadoPrevidenciaResponse[] = [
    {
      empresaId: '1',
      certificadoNumero: '123456',
      certificadoTipo: 'Tipo A',
      situacao: 'Ativo',
      abertura: '2024-01-01',
      emissao: '2024-01-10',
      aposentadoria: '2040-01-01',
      apoliceId: 'A1',
      certificadoBeneficios: [
        {
          planoId: '1',
          beneficioId: '1',
          beneficioTipo: 'Tipo 1',
          beneficioValor: '1000',
          situacao: 'Ativo',
          valorEsperado: '500',
          rendaTipo: 'Tipo A',
          rendaDescricao: 'Descrição',
          contratacao: '2024-01-01',
        },
      ],
      pagamentoRegular: {} as IPagamentoRegularPrevidencia,
      pessoa: {} as IPessoaPrevidencia,
      produto: {} as IProdutoCertificadoPrevidencia,
      opcaoImpostoTipo: 'teste',
      regimeTributario: 'regime',
    },
  ];

  test('deve mostrar loading quando loading é true', () => {
    render(
      <MemoryRouter>
        <ListaCertificados
          loading
          filteredResponse={certificadosMock}
          setCertificadoAtivo={jest.fn()}
        />
      </MemoryRouter>,
    );
    expect(screen.getByText(/carregando/i)).toBeInTheDocument();
  });

  test('deve mostrar o numero certificado', () => {
    render(
      <MemoryRouter>
        <ListaCertificados
          loading={false}
          filteredResponse={certificadosMock}
          setCertificadoAtivo={jest.fn()}
        />
      </MemoryRouter>,
    );

    expect(screen.getByText(/123456/i)).toBeInTheDocument();
  });
  test('deve mostrar o status Certificado', () => {
    render(
      <MemoryRouter>
        <ListaCertificados
          loading={false}
          filteredResponse={certificadosMock}
          setCertificadoAtivo={jest.fn()}
        />
      </MemoryRouter>,
    );

    expect(screen.getByText(/Ativo/i)).toBeInTheDocument();
  });
});
