import * as Resgate from '@src/features/financeiro/resgate/exports';

export const usePrepararSolicitacaoResgate = (
  selecaoAliquota: Resgate.IUseSelecaoAliquotaRetorno,
): Resgate.IUsePrepararSolicitacaoResgateReturn => {
  const { resgateFeatureData, handleResgateFeatureData, handleEtapa } =
    Resgate.useResgateContext();

  const [isOpenModalObservacoes, setIsOpenModalObservacoes] =
    Resgate.useState<boolean>(false);

  const toggleModalObservacoes = (): void => {
    setIsOpenModalObservacoes(prevState => !prevState);
  };

  const { isLoadingDadosBancarios, recuperarBancos } =
    Resgate.useRecuperarBancos();

  const { isLoadingListaMotivosResgate, listarMotivosResgate } =
    Resgate.useListarMotivosResgate();

  const { isLoadingTiposPagamento, consultarTiposPagamento } =
    Resgate.useConsultarTiposPagamento();

  const { isLoadingConsultaContribuicaoRegular, consultarContribuicaoRegular } =
    Resgate.useConsultarContribuicaoRegular();

  const isLoadingPrepararSolicitacao: boolean = Resgate.checkIfSomeItemsAreTrue(
    [
      isLoadingDadosBancarios,
      isLoadingListaMotivosResgate,
      isLoadingTiposPagamento,
      isLoadingConsultaContribuicaoRegular,
    ],
  );

  const prepararSolicitacaoResgate = async (): Promise<void> => {
    const numeroResgateConsolidado = Resgate.obterNumeroResgateConsolidado(
      resgateFeatureData?.dadosSelecaoAliquota,
      resgateFeatureData?.resumoAliquotaSelecionada?.tipoAliquota,
    );

    const dadosBancarios = await recuperarBancos();

    const listaBancosConsolidada: Resgate.IObterBancosCorporativoXPrevFactoryRetorno[] =
      Resgate.obterBancosCorporativoXPrevFactory(dadosBancarios?.entidade);

    const listaMotivosResgate = await listarMotivosResgate();

    const tiposPagamento = await consultarTiposPagamento({
      numeroResgate: numeroResgateConsolidado,
    });

    const contribuicaoRegular = await consultarContribuicaoRegular({
      numeroResgate: numeroResgateConsolidado,
    });

    const contasBancariasValidas: Resgate.IConsultarTiposPagamentoTipo[] =
      Resgate.filtrarContasValidasParaResgate(
        tiposPagamento?.entidade?.tipoPagamentos,
      );

    const listaContasExistentes: Resgate.IMapearContasExistentesFactoryReturn[] =
      Resgate.mapearContasExistentesFactory(contasBancariasValidas);

    handleResgateFeatureData({
      dadosBancarios: listaBancosConsolidada,
      listaMotivosResgate: listaMotivosResgate?.entidade,
      tiposPagamento: tiposPagamento?.entidade,
      contribuicaoRegular: contribuicaoRegular?.entidade,
      numeroResgateConsolidado,
      resumoAliquotaSelecionada: selecaoAliquota.dadosResumoAliquotaSelecionada,
      listaContasExistentes,
    });

    handleEtapa(Resgate.EEtapasResgate.SOLICITACAO);
  };

  return {
    isOpenModalObservacoes,
    toggleModalObservacoes,
    isLoadingPrepararSolicitacao,
    prepararSolicitacaoResgate,
  };
};
