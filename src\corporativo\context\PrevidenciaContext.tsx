import React, {
  createContext,
  useState,
  useMemo,
  ReactNode,
  useCallback,
} from 'react';
import { getTernaryResult, setSessionItem } from '@cvp/utils';
import { TProfileFilters } from '@src/shared/types/TProfileFilters';
import { ICertificadoPrevidenciaResponse } from '@src/features/layoutPrevidencia/exports';
import { TImpressao } from '@src/corporativo/types/impressaoDocumentosPrevidencia/TImpressao';
import { TParametrosScroll } from '@src/corporativo/types/impressaoDocumentosPrevidencia/TParametrosScroll';
import { useObterNomeSocial } from '@src/corporativo/infra/dadosParticipante/UseObterNomeSocial';
import { IPrevidenciaContext } from '@src/corporativo/types/previdenciaContext';

type TPrevidenciaContextProviderProps = { children: ReactNode };
export const PrevidenciaContext = createContext<IPrevidenciaContext>(
  {} as IPrevidenciaContext,
);
PrevidenciaContext.displayName = 'PrevidenciaContext';
const PrevidenciaContextProvider: React.FC<
  TPrevidenciaContextProviderProps
> = ({ children = null }) => {
  const { invocarApiGatewayCvpComToken: atualizarNomeSocial } =
    useObterNomeSocial();
  const [statusContratoFilter, setStatusContratoFilter] =
    useState<TProfileFilters>({} as TProfileFilters);
  const [nomeSocial, setNomeSocial] = useState('');

  const [certificadoAtivo, setCertificadoAtivo] =
    useState<ICertificadoPrevidenciaResponse>(
      {} as ICertificadoPrevidenciaResponse,
    );

  const [impressao, setImpressao] = useState<TImpressao>({} as TImpressao);
  const [parametrosScroll, setParametrosScroll] = useState<TParametrosScroll>(
    {} as TParametrosScroll,
  );

  const [isClientePep, setIsClientePep] = useState<boolean>({} as boolean);

  const setCertificadoAtivoInContextAndSession = async (
    data: ICertificadoPrevidenciaResponse,
  ) => {
    if (data.certificadoNumero === certificadoAtivo.certificadoNumero) return;

    const response = await atualizarNomeSocial({
      situacao: 'ATIVO',
    });

    setNomeSocial(
      getTernaryResult(
        !!response?.entidade,
        response?.entidade?.[0]?.nomeSocial,
        '',
      ) as string,
    );

    setSessionItem('clientePrevidencia', data);
    setCertificadoAtivo(data);
  };

  const resetarCliente = useCallback(() => {
    setCertificadoAtivoInContextAndSession(
      {} as ICertificadoPrevidenciaResponse,
    );
  }, []);

  const contextValue = useMemo(
    () => ({
      setCertificadoAtivo: setCertificadoAtivoInContextAndSession,
      resetarCliente,
      statusContratoFilter,
      setStatusContratoFilter,
      certificadoAtivo,
      impressao,
      nomeSocial,
      setImpressao,
      isClientePep,
      setIsClientePep,
      parametrosScroll,
      setParametrosScroll,
      setNomeSocial,
    }),
    [
      resetarCliente,
      statusContratoFilter,
      certificadoAtivo,
      impressao,
      isClientePep,
      nomeSocial,
      parametrosScroll,
    ],
  );
  return (
    <PrevidenciaContext.Provider value={contextValue}>
      {children}
    </PrevidenciaContext.Provider>
  );
};
export default PrevidenciaContextProvider;
