import { render, screen, fireEvent } from '@testing-library/react';

import {
  BOTOES,
  ModalNaoInformarRecursoAporte,
  ORIGEM_RECURSOS_DECLARACAO,
  ThemeProvider,
} from '@src/features/financeiro/aporte/exports';

describe('ModalNaoInformarRecursoAporte', () => {
  const toggleMock = jest.fn();

  const renderModal = (open: boolean) => {
    render(
      <ThemeProvider>
        <ModalNaoInformarRecursoAporte open={open} toggle={toggleMock}>
          <button type="button">Open Modal</button>
        </ModalNaoInformarRecursoAporte>
      </ThemeProvider>,
    );
  };

  it('deve renderizar o modal quando "open" for true', () => {
    renderModal(true);

    expect(
      screen.getByText(ORIGEM_RECURSOS_DECLARACAO.declaracaoTitulo),
    ).toBeInTheDocument();
    expect(
      screen.getByText(ORIGEM_RECURSOS_DECLARACAO.declaracaoNaoInforma),
    ).toBeInTheDocument();
    expect(screen.getByText(BOTOES.cancelar)).toBeInTheDocument();
    expect(screen.getByText(BOTOES.confirmar)).toBeInTheDocument();
  });

  it('não deve renderizar o modal quando "open" for false', () => {
    renderModal(false);

    expect(
      screen.queryByText(ORIGEM_RECURSOS_DECLARACAO.declaracaoTitulo),
    ).not.toBeInTheDocument();
  });

  it('deve chamar toggle com o valor correto quando um botão for clicado', () => {
    renderModal(true);

    const cancelarButton = screen.getByText(BOTOES.cancelar);
    const confirmarButton = screen.getByText(BOTOES.confirmar);

    fireEvent.click(cancelarButton);
    expect(toggleMock).toHaveBeenCalledWith(false);

    fireEvent.click(confirmarButton);
    expect(toggleMock).toHaveBeenCalledWith(false);
  });
});
