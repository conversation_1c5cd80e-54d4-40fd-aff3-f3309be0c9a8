import {
  CONSTANTES,
  Ds,
  Styles,
  Types,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

export const FiltroPeriodo: React.FC<Types.TFiltroPeriodoProps> = ({
  selecionarFiltro,
  ano,
  atualizacoes,
}) => {
  return (
    <>
      <Ds.Text variant="text-standard-600">
        {CONSTANTES.TEXTOS.HISTORICO_TITULO}
      </Ds.Text>
      <Ds.Text variant="text-standard-400">
        {CONSTANTES.TEXTOS.CONSULTA_A_PARTIR}
        {ano}
      </Ds.Text>

      <Styles.ContainerSelect>
        <Ds.Text variant="text-standard-600">
          {CONSTANTES.TEXTOS.PERIODO_EXIBIDO}
        </Ds.Text>
        <Ds.Select
          options={atualizacoes.map(item => ({
            text: item.numAno,
            value: item.numAno,
          }))}
          selectedValues={[ano]}
          variant="box-classic"
          size="standard"
          sizeWidth="standard"
          placeholder="Todos"
          onChange={([option]) => selecionarFiltro(option.value)}
        />
      </Styles.ContainerSelect>
    </>
  );
};
