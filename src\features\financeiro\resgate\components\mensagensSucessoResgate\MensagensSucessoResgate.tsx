import * as Resgate from '@src/features/financeiro/resgate/exports';

export const MensagensSucessoResgate = ({
  deveRenderizarMensagemSucesso,
  deveRenderizarMensagemPendente,
  deveRenderizarMensagemAguardandoAssinatura,
}: Resgate.IMensagensSucessoResgateProps): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  return (
    <>
      <Resgate.ConditionalRenderer condition={deveRenderizarMensagemSucesso}>
        <Resgate.Alerta tipo="sucesso">
          <Resgate.Text variant="text-standard-400">
            {Resgate.MENSAGENS_RESGATE.SUCESSO.PARAGRAFO_01}
          </Resgate.Text>
        </Resgate.Alerta>

        <Resgate.Text variant="text-standard-400">
          {Resgate.MENSAGENS_RESGATE.SUCESSO.PARAGRAFO_02}
        </Resgate.Text>
      </Resgate.ConditionalRenderer>

      <Resgate.ConditionalRenderer condition={deveRenderizarMensagemPendente}>
        <Resgate.Text variant="text-standard-400">
          {Resgate.MENSAGENS_RESGATE.PENDENTE.PARAGRAFO_01}
        </Resgate.Text>
        <Resgate.Text variant="text-standard-400">
          {
            resgateFeatureData.dadosRetornoConfirmacaoResgate
              ?.motivoPendenciaResgate
          }
        </Resgate.Text>
      </Resgate.ConditionalRenderer>

      <Resgate.ConditionalRenderer
        condition={deveRenderizarMensagemAguardandoAssinatura}
      >
        <Resgate.Text variant="text-standard-400">
          {Resgate.MENSAGENS_RESGATE.SUCESSO_AGUARDANDO_ASSINATURA.PARAGRAFO_01}
        </Resgate.Text>

        <Resgate.Text variant="text-standard-400">
          {Resgate.MENSAGENS_RESGATE.SUCESSO_AGUARDANDO_ASSINATURA.PARAGRAFO_02}
        </Resgate.Text>
      </Resgate.ConditionalRenderer>
    </>
  );
};
