import { PREV_PERMISSIONS } from '@src/corporativo/factories/matrizAcesso/factoryPerfilPermissoes';

export const OpcoesPrimeiroSelect = [
  {
    text: 'Transferência entre fundos',
    value: PREV_PERMISSIONS.TRANSFERENCIA_DE_FUNDOS,
  },
  {
    text: 'Transferência interna com Emissão de Conjugado',
    value: PREV_PERMISSIONS.TRANSFERENCIA_EMISSAO,
  },
  {
    text: 'Transferência entre certificados',
    value: PREV_PERMISSIONS.TRANSFERENCIA_CERTIFICADO,
  },
];

export const FILTRO_TRANSFERENCIA = {
  selectTipoTransferencia: 'Selecione qual serviço deseja:',
};

export const ASSINATURA = {
  AUTENTICACAO: 'Autenticação',
  INVALIDA: 'Não foi possível carregar a assinatura.',
};

export const TIPO_DOCUMENTO = 'Comprovante';

export const CODIGO_REQUISICAO = 'TRANSFUND';

// Constants copied to avoid circular dependencies

// From DadosTransferencia
export const TRANSFERENCIA_TOTAL = 'total';
export const TRANSFERENCIA_PARCIAL = 'parcial';

export const TRANSFERENCIA_TOTAL_PARCIAL = [
  {
    text: 'Total',
    value: TRANSFERENCIA_TOTAL,
  },
  {
    text: 'Parcial',
    value: TRANSFERENCIA_PARCIAL,
  },
];

export const TRANSFERENCIA_APENAS_TOTAL = [
  {
    text: 'Total',
    value: TRANSFERENCIA_TOTAL,
  },
];

// From FluxoDeTransferencia
export const ALERTAS = {
  BOTAO_SIM: 'Sim',
  BOTAO_VOLTAR: 'Voltar',
  MENSAGEM_DESTAQUE_ATENCAO: 'Atenção!',
  MENSAGEM_ERRO_DISTRIBUICAO: {
    DEVE_SER: 'A soma de valores dos fundos devem ser iguais a ',
    VOCE_POSSUI: ' e você possui ',
  },
  MENSAGEM_ATENCAO:
    'Essa ação não é uma simulação e após confirmada não poderá ser cancelada.',
  MENSAGEM_PROSSEGUIR_ATENCAO_MODAL:
    'Essa ação não é uma simulação, e após confirmada não poderá ser cancelada',
  MENSAGEM_PROSSEGUIR_MODAL:
    'Deseja continuar com a transferência entre fundos de investimento?',
  MENSAGEM_TRANSFERENCIA_SUCESSO:
    'A transferência entre fundos de investimentos foi efetuada com suceso.',
  MENSAGEM_ERRO_VALOR_MINIMO: 'O valor mÍnimo de transferência deve ser: ',
  MENSAGEM_ERRO_SEM_FUNDOS_DESTINO:
    'Não foram identificados fundos para poder transferir com o valor informado!',
  MENSAGEM_ERRO_COD_DESTINO: 'O campo Cod Conta Destino deve ser preenchido',
  MENSAGEM_ERRO_COMPROVANTE_TRANSFERENCIA:
    'Não foi possivel gerar o comprovante da transferência',
};

export const VALOR_MINIMO_PERMANENCIA = (
  vlrMinPermancenciaFundo: string,
): string => {
  // Import tryGetMonetaryValueOrDefault directly to avoid circular dependency
  const { tryGetMonetaryValueOrDefault } = require('@cvp/utils');
  return `Caso a opção seja parcial, um valor mínimo de ${tryGetMonetaryValueOrDefault(
    vlrMinPermancenciaFundo,
  )} deverá permanecer no fundo.`;
};

export const VALOR_MINIMO_TRANSFERENCIA = (
  vlrMinTransferenciaFundo: string,
): string => {
  // Import tryGetMonetaryValueOrDefault directly to avoid circular dependency
  const { tryGetMonetaryValueOrDefault } = require('@cvp/utils');
  return `O valor mínimo de transferência para novo fundo deverá ser de ${tryGetMonetaryValueOrDefault(
    vlrMinTransferenciaFundo,
  )}`;
};

export const MENSAGEM_TRANSFERENCIA_SUCESSO =
  'A transferência entre fundos de investimentos foi efetuada com suceso.';

export const MENSAGEM_TRANSFERENCIA_ERRO =
  'Erro ao fazer transferência entre fundos de investimentos';

export const MENSAGEM_FUNDO_INDISPONIVEL =
  'Não foi possível carregar os fundos para transferência';

export const BOTOES_SUCESSO = {
  COMPROVANTE: 'Gerar comprovante',
  FINALIZAR: 'Finalizar atendimento',
};

export const SESSOES = {
  ORIGEM_TITULO: 'Origem',
  DESTINO_TITULO: 'Destino',
  FOOTER_RESTANTE: 'Restante:',
  FOOTER_TOTAL: 'Total:',
  FOOTER_VER_MAIS: 'Ver mais',
  FOOTER_VER_MENOS: 'Ver menos',
  FOOTER_BOTAO_PROSSEGUIR: 'Prosseguir',
};

export const CARREGANDO = {
  CONSULTAS: 'Consultando...',
  DEFINIR_DESTINOS: 'Preparando transferência...',
  REVALIDAR_TRANSFERENCIA: 'Revalidando transferência...',
  CONFIRMAR_TRANSFERENCIA: 'Realizando transferência...',
};
