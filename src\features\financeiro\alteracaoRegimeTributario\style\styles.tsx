import { Dialog } from '@cvp/design-system-caixa';
import { GridItem, RadioGroup, styled, Table } from '../exports';

export const RadioGroupConteiner = styled(RadioGroup)`
  > div {
    width: 100%;
    margin: 21px 16px 26px 16px;
  }
`;

export const TableContent = styled(Table)`
  .sc-dQkurY {
    background-color: #edf4f6;
  }
`;

export const GridItemContent = styled(GridItem)`
  > div {
    border-radius: 10px;
  }
`;

export const Modal: React.JSX.Element = styled(Dialog)`
  max-height: 249px;
  border-radius: 0;
`;

export const HeaderDialog = styled(Dialog.Header)`
  background-color: #005ca9 !important;
  color: #ffffff;
  padding: 5px;
  padding-left: 20px;
  height: 62px;
  border-radius: 0;
  box-shadow: none;
  border-bottom: none;

  button {
    background-color: #005ca9;
    padding: 5px;
    padding-left: 20px;
    svg {
      width: 21px;
      color: #ffffff;
    }
  }
`;
