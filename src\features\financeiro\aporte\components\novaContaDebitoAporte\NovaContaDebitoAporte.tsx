import {
  BOTOES,
  <PERSON><PERSON>,
  <PERSON>tonContainer,
  checkIfSomeItemsAreTrue,
  EEtapasAporte,
  Formik,
  FormularioNovaContaBancariaAporte,
  getTernaryResult,
  Grid,
  GridItem,
  LoadingSpinner,
  NOVA_CONTA_BANCARIA_INITIAL_STATE,
  NOVA_CONTA_DEBITO,
  Text,
  useNovaContaAporte,
  validacaoCadastrarNovaContaBancaria,
} from '@src/features/financeiro/aporte/exports';

const NovaContaDebitoAporte: React.FC = () => {
  const { validarContaLoading, handleValidarConta, setEtapa } =
    useNovaContaAporte();
  return (
    <>
      <Text
        variant="text-big-400"
        fontColor="content-neutral-06"
        marginBottom="30px"
        marginTop="24px"
      >
        {NOVA_CONTA_DEBITO.titulo}
      </Text>
      <Formik
        initialValues={NOVA_CONTA_BANCARIA_INITIAL_STATE}
        onSubmit={values => {
          handleValidarConta(values);
        }}
        validationSchema={validacaoCadastrarNovaContaBancaria}
      >
        {({ handleSubmit, handleChange, values, setFieldValue, isValid }) => (
          <form onSubmit={handleSubmit}>
            <FormularioNovaContaBancariaAporte
              handleChange={handleChange}
              values={values}
              setFieldValue={setFieldValue}
            />

            <Grid justify="flex-end">
              <GridItem>
                <Button
                  onClick={() => setEtapa(EEtapasAporte.ContaDebitoAporte)}
                  disabled={validarContaLoading}
                  size="standard"
                  variant="secondary-outlined"
                >
                  {BOTOES.cancelar}
                </Button>
              </GridItem>
              <GridItem>
                <Button
                  type="submit"
                  disabled={checkIfSomeItemsAreTrue([
                    !isValid,
                    validarContaLoading,
                  ])}
                  size="standard"
                  variant="primary"
                >
                  <ButtonContainer>
                    {getTernaryResult(
                      validarContaLoading,
                      <LoadingSpinner />,
                      <p>{BOTOES.avancar}</p>,
                    )}
                  </ButtonContainer>
                </Button>
              </GridItem>
            </Grid>
          </form>
        )}
      </Formik>
    </>
  );
};

export default NovaContaDebitoAporte;
