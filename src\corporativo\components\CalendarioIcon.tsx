import { Svg } from '@cvp/design-system-caixa';
import { ISvgProps } from '@cvp/design-system-caixa/dist/atoms/Svg';

const IconCalendario: React.FC<ISvgProps> = ({ size }) => {
  return (
    <Svg width="16" height="18" viewBox="0 0 16 18" fill="none" size={size}>
      <path
        d="M13.8333 2.33341H13V0.666748H11.3333V2.33341H4.66667V0.666748H3V2.33341H2.16667C1.24167 2.33341 0.5 3.08341 0.5 4.00008V15.6667C0.5 16.5834 1.24167 17.3334 2.16667 17.3334H13.8333C14.75 17.3334 15.5 16.5834 15.5 15.6667V4.00008C15.5 3.08341 14.75 2.33341 13.8333 2.33341ZM13.8333 15.6667H2.16667V7.33342H13.8333V15.6667ZM13.8333 5.66675H2.16667V4.00008H13.8333V5.66675ZM3.83333 9.00008H8V13.1667H3.83333V9.00008Z"
        fill="#9EB2B8"
      />
    </Svg>
  );
};

export default IconCalendario;
