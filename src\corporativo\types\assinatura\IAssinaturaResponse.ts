import { EMetodoAssinatura } from '@src/corporativo/constants/assinatura/params';

export interface IMessage {
  status: string;
  codigo: string;
  descricao: string;
}

export interface IBackendError {
  httpStatus: string;
  messages: IMessage[];
}

export interface IDadosValidacao {
  success: boolean;
  token?: string;
  error?: IBackendError;
}
export interface ISenhaResponse extends IDadosValidacao {}

export interface ITokenResponse extends IDadosValidacao {
  telefoneValidacao?: string;
}

export interface IAssinaturaResponse {
  metodo: EMetodoAssinatura;
  resposta: ISenhaResponse | ITokenResponse;
}
