interface IErrorPattern {
  pattern: RegExp;
  description: string;
  recovery: () => void;
}

const ERROR_PATTERNS: IErrorPattern[] = [
  {
    pattern: /Cannot read properties of undefined \(reading '600'\)/,
    description: 'Erro de tema/estilo no design system',
    recovery: () => {
      console.log(' Tentando recuperar erro de tema...');
      const event = new CustomEvent('theme-error-recovery');
      window.dispatchEvent(event);

      setTimeout(() => {
        window.location.reload();
      }, 2000);
    },
  },
  {
    pattern: /Cannot read properties of undefined \(reading/,
    description: 'Erro de propriedade undefined genérico',
    recovery: () => {
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    },
  },
  {
    pattern: /TypeError.*is not a function/,
    description: 'Erro de função não encontrada',
    recovery: () => {
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    },
  },
];

let errorRecoveryAttempts = 0;
const MAX_RECOVERY_ATTEMPTS = 2;

function attemptErrorRecovery(error: Error | string): boolean {
  const errorMessage = typeof error === 'string' ? error : error.message;

  if (errorRecoveryAttempts >= MAX_RECOVERY_ATTEMPTS) {
    console.warn(' Máximo de tentativas de recuperação atingido');
    return false;
  }

  for (const pattern of ERROR_PATTERNS) {
    if (pattern.pattern.test(errorMessage)) {
      errorRecoveryAttempts++;

      try {
        pattern.recovery();
        return true;
      } catch (recoveryError) {}
    }
  }

  return false;
}

export function setupRuntimeErrorRecovery() {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const originalConsoleError = console.error;
  console.error = (...args) => {
    const errorMessage = args.join(' ');

    if (attemptErrorRecovery(errorMessage)) {
      console.log(' Tentativa de recuperação iniciada');
    }

    originalConsoleError.apply(console, args);
  };

  const originalErrorHandler = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    if (typeof message === 'string') {
      attemptErrorRecovery(message);
    }

    if (originalErrorHandler) {
      return originalErrorHandler(message, source, lineno, colno, error);
    }
    return false;
  };

  if (typeof module !== 'undefined' && module.hot) {
    module.hot.accept();
    module.hot.dispose(() => {
      errorRecoveryAttempts = 0;
    });
  }

  window.addEventListener('keydown', event => {
    if (event.ctrlKey && event.shiftKey && event.key === 'R') {
      event.preventDefault();
      errorRecoveryAttempts = 0;
      window.location.reload();
    }
  });
}

export function clearVisualErrors() {
  try {
    const errorOverlays = document.querySelectorAll(
      '[data-react-error-overlay], [data-webpack-overlay], .react-error-overlay',
    );

    errorOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
        console.log('🧹 Overlay de erro removido');
      }
    });

    const reactErrors = document.querySelectorAll(
      '[data-reactroot] [style*="error"]',
    );
    reactErrors.forEach(error => {
      if (error.parentNode) {
        (error as HTMLElement).style.display = 'none';
      }
    });

    return true;
  } catch (error) {
    console.warn('⚠️ Erro ao limpar erros visuais:', error);
    return false;
  }
}

// Auto-inicialização
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setupRuntimeErrorRecovery();
}
