/**
 * Sistema de recuperação de runtime errors específicos
 * Detecta e tenta recuperar de erros comuns durante desenvolvimento
 */

interface ErrorPattern {
  pattern: RegExp;
  description: string;
  recovery: () => void;
}

// Padrões de erro conhecidos e suas recuperações
const ERROR_PATTERNS: ErrorPattern[] = [
  {
    pattern: /Cannot read properties of undefined \(reading '600'\)/,
    description: 'Erro de tema/estilo no design system',
    recovery: () => {
      console.log('🔧 Tentando recuperar erro de tema...');
      // Força re-render dos componentes com tema
      const event = new CustomEvent('theme-error-recovery');
      window.dispatchEvent(event);

      // Se não funcionar, recarrega após 2 segundos
      setTimeout(() => {
        console.log('🔄 Recuperação de tema falhou, recarregando...');
        window.location.reload();
      }, 2000);
    },
  },
  {
    pattern: /Cannot read properties of undefined \(reading/,
    description: 'Erro de propriedade undefined genérico',
    recovery: () => {
      console.log('🔧 Tentando recuperar erro de propriedade undefined...');
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    },
  },
  {
    pattern: /TypeError.*is not a function/,
    description: 'Erro de função não encontrada',
    recovery: () => {
      console.log('🔧 Tentando recuperar erro de função...');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    },
  },
];

let errorRecoveryAttempts = 0;
const MAX_RECOVERY_ATTEMPTS = 2;

/**
 * Analisa o erro e tenta recuperação específica
 */
function attemptErrorRecovery(error: Error | string): boolean {
  const errorMessage = typeof error === 'string' ? error : error.message;

  // Evita loops infinitos de recuperação
  if (errorRecoveryAttempts >= MAX_RECOVERY_ATTEMPTS) {
    console.warn('🚫 Máximo de tentativas de recuperação atingido');
    return false;
  }

  // Procura padrão correspondente
  for (const pattern of ERROR_PATTERNS) {
    if (pattern.pattern.test(errorMessage)) {
      console.log(`🎯 Erro identificado: ${pattern.description}`);
      errorRecoveryAttempts++;

      try {
        pattern.recovery();
        return true;
      } catch (recoveryError) {
        console.error('❌ Falha na recuperação:', recoveryError);
      }
    }
  }

  return false;
}

/**
 * Configura sistema de recuperação de runtime errors
 */
export function setupRuntimeErrorRecovery() {
  if (process.env.NODE_ENV !== 'development') {
    return; // Só ativo em desenvolvimento
  }

  console.log('🛡️ Sistema de recuperação de runtime errors ativo');

  // Intercepta erros do React Error Boundary
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const errorMessage = args.join(' ');

    // Detecta erros específicos e tenta recuperação
    if (attemptErrorRecovery(errorMessage)) {
      console.log('🔄 Tentativa de recuperação iniciada');
    }

    // Chama console.error original
    originalConsoleError.apply(console, args);
  };

  // Intercepta erros globais
  const originalErrorHandler = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    if (typeof message === 'string') {
      attemptErrorRecovery(message);
    }

    // Chama handler original
    if (originalErrorHandler) {
      return originalErrorHandler(message, source, lineno, colno, error);
    }
    return false;
  };

  // Reset contador quando HMR é bem-sucedido
  if (typeof module !== 'undefined' && module.hot) {
    module.hot.accept();
    module.hot.dispose(() => {
      errorRecoveryAttempts = 0;
      console.log('✅ Contador de recuperação resetado');
    });
  }

  // Listener para recuperação manual
  window.addEventListener('keydown', event => {
    // Ctrl + Shift + R para forçar recuperação
    if (event.ctrlKey && event.shiftKey && event.key === 'R') {
      event.preventDefault();
      console.log('🔄 Recuperação manual acionada');
      errorRecoveryAttempts = 0;
      window.location.reload();
    }
  });

  console.log('💡 Dica: Use Ctrl+Shift+R para forçar recuperação manual');
}

/**
 * Força limpeza de erros visuais
 */
export function clearVisualErrors() {
  try {
    // Remove overlays de erro
    const errorOverlays = document.querySelectorAll(
      '[data-react-error-overlay], [data-webpack-overlay], .react-error-overlay',
    );

    errorOverlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
        console.log('🧹 Overlay de erro removido');
      }
    });

    // Remove elementos de erro do React
    const reactErrors = document.querySelectorAll(
      '[data-reactroot] [style*="error"]',
    );
    reactErrors.forEach(error => {
      if (error.parentNode) {
        (error as HTMLElement).style.display = 'none';
        console.log('🧹 Elemento de erro React ocultado');
      }
    });

    return true;
  } catch (error) {
    console.warn('⚠️ Erro ao limpar erros visuais:', error);
    return false;
  }
}

// Auto-inicialização
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setupRuntimeErrorRecovery();
}
