import {
  columnsTabelaFundosDistribuicaoFactory,
  customStyles,
  FUNDOS,
  Grid,
  GridItem,
  IDadosTabelaFundos,
  Match,
  Table,
  Text,
  TIPO_FUNDO,
  tryGetValueOrDefault,
  useRenderTabelaFundos,
} from '@src/features/financeiro/aporte/exports';

interface IComprovanteAporteFundos {
  fundosExistentes: IDadosTabelaFundos;
  fundosNovos: IDadosTabelaFundos;
}

const ComprovanteAporteFundos: React.FC<IComprovanteAporteFundos> = ({
  fundosExistentes,
  fundosNovos,
}) => {
  return (
    <Grid>
      <Match when={fundosExistentes.dadosFundos.length > 0}>
        <GridItem xs="1" margin="0px 18px">
          <Text variant="text-large-600" fontColor="content-neutral-04">
            {FUNDOS.existentes}
          </Text>
          <Table
            responsive
            striped
            highlightOnHover
            themeTable="cvp-05"
            data={tryGetValueOrDefault(
              [
                useRenderTabelaFundos({
                  dadosTabelaFundos: fundosExistentes,
                  isChecked: true,
                  tipoFundo: TIPO_FUNDO.existente,
                }),
              ],
              [],
            )}
            customStyles={customStyles}
            columns={columnsTabelaFundosDistribuicaoFactory}
            noDataComponent={FUNDOS.fundosExistentesIndisponiveis}
          />
        </GridItem>
      </Match>
      <Match when={fundosNovos.dadosFundos.length > 0}>
        <GridItem xs="1" margin="0px 18px">
          <Text variant="text-large-600" fontColor="content-neutral-04">
            {FUNDOS.fundosNovos}
          </Text>
          <Table
            responsive
            striped
            highlightOnHover
            themeTable="cvp-05"
            data={tryGetValueOrDefault(
              [
                useRenderTabelaFundos({
                  dadosTabelaFundos: fundosNovos,
                  isChecked: true,
                  tipoFundo: TIPO_FUNDO.novo,
                }),
              ],
              [],
            )}
            customStyles={customStyles}
            columns={columnsTabelaFundosDistribuicaoFactory}
            noDataComponent={FUNDOS.fundosNovosIndisponiveis}
          />
        </GridItem>
      </Match>
    </Grid>
  );
};

export default ComprovanteAporteFundos;
