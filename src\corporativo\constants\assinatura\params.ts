export enum ETipoConta {
  NSGD = '1',
  SIDEC = '2',
}
export enum EProduto {
  CONTA_CORRENTE_PF = '1',
  CONTA_SIMPLES_PF = '2',
  POUPANCA_CAIXA = '13',
  CONTA_CAIXA_FACIL = '23',
  CONTA_DEPOSITO_FIANCA = '19',
  POUPANCA_CREDITO_IMOBILIARIO = '28',
  CONTA_SALARIO = '37',
  POUPANCA_PF_CAIXA = '1288',
  CONTA_SALARIO_CAIXA = '3700',
  CONTA_CORRENTE_PF_CAIXA = '3701',
  CONTA_CAUCAO_VINCULADA_008 = '8',
  CONTA_CAUCAO_VINCULADA_010 = '10',
  CONSIGNACAO_EM_PAGAMENTO_NSGD = '2773',
}

export enum EMetodoAssinatura {
  SENHA = 'senha',
  TOKEN = 'token',
  FACIAL = 'facial',
}

export enum ETelfoneOrigem {
  SICLI = 'SICLI',
  SIGMS = 'SIGMS',
  AMBOS = 'AMBOS',
}

export enum EOrigem {
  CLI = 'CLI',
}

export const MENSAGENS = {
  CONFIRMACAO: 'Deseja confirmar a operação?',
};

export const TOKEN_CONFIG = {
  tempoExpiracao: '2',
  tempoExpiracaoNovoToken: '2',
};

export const ID_ANGULAR_COMPONENT = 'angularComponent';
