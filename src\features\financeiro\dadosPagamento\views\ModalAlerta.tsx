import { useEffect, useState } from 'react';
import { Button, DialogInfo } from '@cvp/design-system-caixa';

type TAlertaGerarBoletoProps = {
  open: boolean;
};

const AlertaGerarBoleto: React.FC<TAlertaGerarBoletoProps> = ({ open }) => {
  const [mostraAlerta, setMostraAlerta] = useState<boolean>(false);

  const handleFecharAlerta = () => {
    setMostraAlerta(false);
  };

  useEffect(() => {
    if (open) setMostraAlerta(open);
  }, [open]);

  return (
    <DialogInfo
      open={mostraAlerta}
      title="Gerar boleto"
      trigger={<div />}
      body={`Para gerar e imprimir o boleto, selecione uma parcela e clique em "Gerar boleto".`}
      titleVariant="highlight"
      maxWidth="500px"
      footer={
        <Button variant="secondary" onClick={handleFecharAlerta}>
          Ok
        </Button>
      }
      notCloseOnOutsideClick
      onOpenChange={handleFecharAlerta}
    />
  );
};

export default AlertaGerarBoleto;
