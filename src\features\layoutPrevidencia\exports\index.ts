export {
  For,
  SwitchCase,
  Match,
  LayoutPlataforma,
  useFilter,
} from '@cvp/componentes-posvenda';
export { useNavigate } from 'react-router-dom';
export { useContext } from 'react';
export { Grid, IconPVPlusCircle } from '@cvp/design-system-caixa';
export { default as useCertificadosPrevidencia } from '@src/corporativo/infra/consultaCertificado/useCertificadosPrevidencia';
export { REDIRECT_BUTTONS } from '@src/shared/factory/botoesRedirect';
export { default as useTabsFromRouter } from '../hook/useTabsFromRouter';
export { default as tabsFromRouterFactory } from '../factory/TabsFromRouterFactory';
export { default as LayoutConsulta } from '../views/LayoutPrevidencia';
export { default as ListaCertificados } from '../views/ListaCertificados';
export { default as previdencia } from '../../../corporativo/constants/PrevidenciaConstants';
export { default as CertificadoInfo } from '../components/CertificadoInfo';
export { default as StatusCertificadoMapper } from '../views/StatusPrevidenciaMapper';
export { default as ContainerListaCertificados } from '../views/ContainerListaCertificados';
export { ModalImprimirDocumentosContext } from '@src/corporativo/context/ModalImprimirDocumentosContext';

export { DescricaoStatusPrevidencia } from '../constants/DescricaoStatusPrevidencia';
export { default as ApoliceIcon } from '../../../corporativo/components/ApoliceIcon';
export { default as CalendarioIcon } from '../../../corporativo/components/CalendarioIcon';
export { default as DocumentIcon } from '../../../corporativo/components/DocumentIcon';
export { default as IconPDF } from '../../../corporativo/components/PDFIcon';
export { default as BotoesApolice } from '../components/BotoesApolice';
export { Badge, CertificadoPrevidenciaWrapper } from '../styles/style';

export type { IDadosTabelaProdutosPrevidencia } from '@src/corporativo/types/consultaCertificado/IDadosTabelaProdutosPrevidencia';
export type { ICertificadoPrevidenciaResponse } from '@src/shared/types/ICertificadoPrevidenciaResponse';
export type { TProfileFilters } from '../../../shared/types/TProfileFilters';
export type { TStatusContratoFilter } from '../../../shared/types/TProfileFilters';
export { default as atualizarSituacoes } from '../factory/FiltroPropostaFactroy';
export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
export { MatrizAcessoContext } from '@src/corporativo/context/MatrizAcessoContext';
export { default as useListaCertificados } from '../hook/useListaCertificados ';
export { default as useObterBeneficiarios } from '@src/corporativo/infra/beneficiarios/useObterBeneficiarios';

export * from '../utils';
export * from '../constants/imprDocModalTextos';
export { default as ImprimirDocumento } from '../pages/ImprimirDocumento';
export * from '../factory/modalImprDocFactory';
export { default as useModalImprDoc } from '../hook/useModalImprDoc';
export * from '../types/modalImprDocTypes';
export { default as ModalImprDoc } from '../views/ModalImprDoc';

export { LOADING, LOADING_INSURANCE } from '../../../shared/constants/api';

export type { IButtonRedirectProps } from '@cvp/componentes-posvenda/types/types/components/ILayoutPlataforma';
