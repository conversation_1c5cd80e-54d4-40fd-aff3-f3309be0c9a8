export interface IAndamento {
  data: string;
  codigoDocumento: string;
  status: string;
  descricaoStatus: string;
  textoAuxiliarStatus: string;
}

export interface IDadosSinistroResponse {
  dados: {
    andamentosCertificados: Array<IDadosSinistroEntidade>;
    cpfTitular: string;
    nomeTitular: string;
  };
  sucesso: boolean;
  mensagem: string;
}

export interface IDadosSinistroEntidade {
  codigoCertificado: string;
  dataAvisoSinistro: string;
  dataSinistro: string;
  numeroAvisoSinistro: number;
  descricaoTipoSinitro: string;
  descricaoMotivoSinistro: string;
  statusAvisoSinitro: string;
  andamentos: IAndamento[];
}
