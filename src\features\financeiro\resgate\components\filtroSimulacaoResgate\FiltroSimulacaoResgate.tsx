import * as Resgate from '@src/features/financeiro/resgate/exports';

export const FiltroSimulacaoResgate = ({
  saldoTotal,
  isTipoResgateParcial,
  listaFundosParaResgate,
  selecionarTipoResgate,
}: Resgate.IFiltroSimulacaoResgateProps): React.ReactElement => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <>
      <Resgate.GridItem xs="1" lg="1 / 4">
        <Resgate.Text
          variant="text-standard-600"
          fontColor="content-neutral-05"
        >
          Saldo em 06/03/2025
        </Resgate.Text>
        <Resgate.InputText
          name="valorContribuicao"
          type="text"
          variant="box-classic"
          size="standard"
          placeholder="R$ 0,00"
          arialabel="Saldo atual"
          disabled
          value={Resgate.valoresMonetarios.mask(saldoTotal?.toString())}
        />
      </Resgate.GridItem>
      <Resgate.GridItem xs="1" lg="1 / 4" style={{ display: 'flex' }}>
        <Resgate.ContainerEscolhaTipoResgate>
          <Resgate.Text
            variant="text-standard-600"
            fontColor="content-neutral-05"
          >
            Tipo de resgate
          </Resgate.Text>
          <Resgate.RadioGroup
            defaultValue="primeiro"
            direction="row"
            name="groupExemple"
            value={formik.values.tipoResgate}
            onValueChange={selecionarTipoResgate}
          >
            {Resgate.LISTA_TIPOS_RESGATE.map(item => (
              <Resgate.RadioItem
                variant="highlight"
                id={item.id}
                value={item.id}
                rightLabel={
                  <Resgate.RadioLabel htmlFor={item.id} size="standard">
                    {item.value}
                  </Resgate.RadioLabel>
                }
              />
            ))}
          </Resgate.RadioGroup>
        </Resgate.ContainerEscolhaTipoResgate>
      </Resgate.GridItem>

      <Resgate.ConditionalRenderer condition={isTipoResgateParcial}>
        <Resgate.GridItem xs="1">
          <Resgate.Alerta tipo="atencao">
            <Resgate.Text variant="text-standard-400">
              Atenção! O plano deverá permanecer com reserva mínima de{' '}
              {Resgate.tryGetMonetaryValueOrDefault(
                listaFundosParaResgate?.limitesCertificado
                  ?.valorMinimoPermanencia,
              )}
              .
            </Resgate.Text>
          </Resgate.Alerta>
        </Resgate.GridItem>
      </Resgate.ConditionalRenderer>
    </>
  );
};
