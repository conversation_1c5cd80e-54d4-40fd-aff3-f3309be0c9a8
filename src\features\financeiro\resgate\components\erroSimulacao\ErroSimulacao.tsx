import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ErroSimulacao = ({
  erroSimulacao,
}: Resgate.IErroSimulacaoProps): React.ReactElement => {
  return (
    <Resgate.ConditionalRenderer condition={!!erroSimulacao}>
      <Resgate.GridItem xs="1">
        <Resgate.Alerta tipo="erro">
          <Resgate.Text variant="heading-standard-600">Erro</Resgate.Text>

          <div>
            <Resgate.Text variant="text-standard-400">
              {erroSimulacao}
            </Resgate.Text>
          </div>
        </Resgate.Alerta>
      </Resgate.GridItem>
    </Resgate.ConditionalRenderer>
  );
};
