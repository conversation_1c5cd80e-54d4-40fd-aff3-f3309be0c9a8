import {
  getSessionItem,
  IObterDadosBancariosPayload,
  IObterDadosBancariosResponse,
  PECOS,
  PrevidenciaContext,
  tryGetValueOrDefault,
  TUseObterDadosBancarios,
  useApiGatewayCvpInvoker,
  useContext,
} from '@src/features/financeiro/aporte/exports';

export const useObterDadosBancarios = (): TUseObterDadosBancarios => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload = {
    cpfCnpj,
    numeroCertificado: certificadoAtivo?.certificadoNumero,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IObterDadosBancariosPayload,
      IObterDadosBancariosResponse
    >(PECOS.ObterDadosBancarios, {
      data: payload,
      autoFetch: false,
    });

  return {
    loading,
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as IObterDadosBancariosResponse,
    ),
    invocarApiGatewayCvpComToken,
  };
};
