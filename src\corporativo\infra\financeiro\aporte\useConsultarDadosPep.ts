import {
  getSessionItem,
  IObterDadosPepPayload,
  IObterDadosPepResponse,
  PECOS,
  tryGetValueOrDefault,
  TUseConsultarDadosPep,
  useApiGatewayCvpInvoker,
} from '@src/features/financeiro/aporte/exports';

export const useConsultarDadosPep = (): TUseConsultarDadosPep => {
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload = {
    cpfCnpj,
  };

  const { response, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    IObterDadosPepPayload,
    IObterDadosPepResponse
  >(PECOS.ConsultarDadosPep, {
    data: payload,
    autoFetch: false,
    cache: true,
    cacheKey: cpfCnpj,
  });

  return {
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as IObterDadosPepResponse,
    ),
    invocarApiGatewayCvpComToken,
  };
};
