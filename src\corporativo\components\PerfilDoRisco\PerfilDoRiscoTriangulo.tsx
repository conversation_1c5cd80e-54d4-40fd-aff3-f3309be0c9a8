import React from 'react';

type TTPerfilDoRiscoTrianguloProps = {
  color: string;
};
export const PerfilDoRiscoTriangulo: React.FC<
  TTPerfilDoRiscoTrianguloProps
> = ({ color }) => (
  <svg
    width="139"
    height="116"
    viewBox="0 0 139 116"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M69.5 0L0 116H139L69.5 0Z" fill="white" />
    <path d="M69 36L29 102H109L69 36Z" fill={color} />
  </svg>
);
