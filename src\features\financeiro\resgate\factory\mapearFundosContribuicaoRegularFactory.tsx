import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Mapear dados de fundos de contribuição regular para o formato adequado à exibição na interface
 *
 * Esta função transforma os dados brutos dos fundos de contribuição regular em componentes visuais interativos,
 * incluindo checkboxes para seleção, componentes de perfil de risco, formatação de valores percentuais de rentabilidade
 * e campos de entrada para exibição dos valores de contribuição.
 *
 * @param {Object} params - Parâmetros para o mapeamento dos fundos
 * @param {Array<Object>} params.fundos - Lista de fundos de contribuição regular disponíveis
 * @param {(codigoFundo: string) => void} params.selecionarFundoParaContribuicao - Função para manipular a seleção de um fundo para contribuição
 * @returns {IMapearFundosContribuicaoRegularFactoryReturn[]} Lista de fundos formatados com componentes React para exibição na interface
 *
 * @example
 * // Exemplo de uso:
 * const fundosFormatados = mapearFundosContribuicaoRegularFactory({
 *   fundos: [
 *     {
 *       codigoFundo: '123',
 *       selecionado: false,
 *       perfilFundo: 'MODERADO',
 *       rentabilidade: 5.67,
 *       valorContribuicao: '1000.00'
 *     }
 *   ],
 *   selecionarFundoParaContribuicao: (codigoFundo) => console.log(`Fundo ${codigoFundo} selecionado`)
 * });
 * // Retorna um array com os dados originais do fundo, mais os componentes React
 * // para selecionar, exibir o perfil, rentabilidade formatada e valor formatado
 */
export const mapearFundosContribuicaoRegularFactory = ({
  fundos,
  selecionarFundoParaContribuicao,
}: Resgate.IMapearFundosContribuicaoRegularFactory): Resgate.IMapearFundosContribuicaoRegularFactoryReturn[] => {
  const fundosContribuicaoRegular = Resgate.tryGetValueOrDefault([fundos], []);

  return fundosContribuicaoRegular.map(fundo => {
    return {
      ...fundo,
      selecionar: (
        <Resgate.Checkbox
          name="selecionar"
          variant="outlineBlack"
          key={`checkbox-${fundo.codigoFundo}`}
          checked={fundo.selecionado}
          disabled={fundo.selecionado}
          onChange={() => selecionarFundoParaContribuicao(fundo.codigoFundo)}
        />
      ),
      perfilFundo: (
        <Resgate.PerfilDoRisco
          key={`perfil-${fundo.codigoFundo}`}
          perfil={fundo.perfilFundo}
        />
      ),
      rentabilidade: Resgate.porcentagem.mask(
        Resgate.tryGetValueOrDefault([fundo.rentabilidade.toString()], ''),
      ),
      valorResgatado: (
        <Resgate.InputText
          key={`input-${fundo.codigoFundo}`}
          type="text"
          disabled
          value={Resgate.valoresMonetarios.mask(fundo.valorContribuicao)}
          arialabel="Adicione um valor para contribuir regularmente"
          placeholder="R$ 0,00"
          variant="box-classic"
          size="small"
        />
      ),
    };
  });
};
