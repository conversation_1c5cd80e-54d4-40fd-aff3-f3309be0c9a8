import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  CONSTS,
  TUseSolicitarAlteracaoRegimeTributario,
  useAlteracaoRegimeTributarioContext,
  useAssinaturaAlteracaoRegimeTributario,
  useConsultarRegimeTributario,
  useSolicitarAlteracaoPerfilTributario,
} from '../exports';

export const useSolicitarAlteracaoRegimeTributario: TUseSolicitarAlteracaoRegimeTributario =
  () => {
    const {
      opcaoTributacaoIrrf,
      podeExibirAssinatura,
      setPodeExibirAssinatura,
    } = useAlteracaoRegimeTributarioContext();

    const {
      loading: isLoadingConsultarRegimeTributario,
      podeAlterarRegimeTributario,
    } = useConsultarRegimeTributario();

    const {
      assinaturaValida,
      obterAssinatura,
      confirmarAssinaturaAlteracaoRegimeTributario,
      loading: isLoadingConfirmarAssinatura,
    } = useAssinaturaAlteracaoRegimeTributario();

    const {
      isLoadingSolicitarAlteracaoPerfilTributario,
      solicitarAlteracaoPerfilTributarioResponse,
      solicitarAlteracaoPerfilTributario,
    } = useSolicitarAlteracaoPerfilTributario();

    const handleSolicitarAlteracaoPerfilTributario = async () => {
      if (!podeExibirAssinatura) {
        setPodeExibirAssinatura(
          checkIfAllItemsAreTrue([
            podeAlterarRegimeTributario ===
              CONSTS.REGIME_TRIBUTARIO.PODE_ALTERAR,
            opcaoTributacaoIrrf !== '',
          ]),
        );

        return;
      }

      if (assinaturaValida) {
        const resultado = await solicitarAlteracaoPerfilTributario({
          opcaoTributacaoIrrf,
        });

        confirmarAssinaturaAlteracaoRegimeTributario(
          resultado?.entidade?.assinaturas[0]?.codigo,
        );
      }
    };

    return {
      loading: checkIfSomeItemsAreTrue([
        isLoadingConsultarRegimeTributario,
        isLoadingConfirmarAssinatura,
      ]),
      urlAssinatura:
        solicitarAlteracaoPerfilTributarioResponse?.assinaturas?.[0]
          .urlAssinatura,
      assinaturaValida,
      isLoadingSolicitarAlteracaoPerfilTributario,
      obterAssinatura,
      handleSolicitarAlteracaoPerfilTributario,
    };
  };
