import {
  CONTA_DEBITO,
  dadosBancariosResponseFactory,
  dadosBancariosTextFactory,
  EEtapasAporte,
  enumNumeroBancos,
  enumTipoContaBancaria,
  enumValidacaoContaBancaria,
  FILTRO_TABELA_INITIAL_STATE,
  IDadosBancariosAporte,
  ITextoDadosBancarios,
  obterNumeroContaSemOperacao,
  obterNumeroOperacao,
  SelectItem,
  TEXTO_DADOS_BANCARIOS_INITIAL_STATE,
  useAporteContext,
  useAporteServiceContext,
  useEffect,
  useState,
} from '@src/features/financeiro/aporte/exports';

type TUseContaDebitoAporte = () => {
  textoDadosBancarios: ITextoDadosBancarios[];
  validarContaLoading: boolean;
  obterDadosBancariosLoading: boolean;
  contaBancariaSelecionada: IDadosBancariosAporte;
  handleSelecionarContaBancaria: (option: SelectItem[]) => void;
  handleVerificarConta: () => Promise<void>;
  handleEtapaAnterior: () => void;
};

export const useContaDebitoAporte: TUseContaDebitoAporte = () => {
  const {
    formikFiltrosTabelaAporte,
    formikValorDistribuido,
    contasBancarias,
    contaBancariaSelecionada,
    setFiltroTabelaAporte,
    setContasBancarias,
    setContaBancariaSelecionada,
    setTipoContaBancaria,
    setEtapa,
  } = useAporteContext();

  const {
    obterDadosBancarios,
    validarConta: { validarConta, isLoadingValidacaoConta },
  } = useAporteServiceContext();

  const [textoDadosBancarios, setTextoDadosBancarios] = useState<
    ITextoDadosBancarios[]
  >([TEXTO_DADOS_BANCARIOS_INITIAL_STATE]);

  const handleSelecionarContaBancaria = (option: SelectItem[]) => {
    const opcaoSelecionada = option[0].value;

    if (opcaoSelecionada === CONTA_DEBITO.novaConta) {
      setEtapa(EEtapasAporte.NovaContaDebitoAporte);
    } else {
      const contaBancariaEscolhida = contasBancarias.find(conta =>
        opcaoSelecionada.includes(conta.canalId),
      );

      if (contaBancariaEscolhida) {
        setContaBancariaSelecionada(contaBancariaEscolhida);
      }
    }
  };

  const obterContaBancaria = async () => {
    const response = await obterDadosBancarios.invocarApiGatewayCvpComToken();

    const dadosBancarios = dadosBancariosResponseFactory(response?.entidade);

    setContasBancarias(dadosBancarios);

    const texto = dadosBancariosTextFactory(dadosBancarios);

    setTextoDadosBancarios(texto);
  };

  const handleVerificarConta = async () => {
    const response = await validarConta({
      codigoAgencia: contaBancariaSelecionada?.numeroAgencia,
      codigoOperacao: obterNumeroOperacao(
        contaBancariaSelecionada?.numeroConta,
      ),
      digitoVerificador: contaBancariaSelecionada?.digitoConta,
      numeroBanco: enumNumeroBancos.CAIXA_ECONOMICA,
      numeroConta: obterNumeroContaSemOperacao(
        contaBancariaSelecionada?.numeroConta,
      ),
    });

    if (
      response?.entidade?.codigoRetorno ===
      enumValidacaoContaBancaria.CONTA_BANCARIA_CADASTRADA_VALIDADA
    ) {
      setEtapa(EEtapasAporte.ModalConfirmarAporte);
    }
  };

  const handleEtapaAnterior = () => {
    setFiltroTabelaAporte(FILTRO_TABELA_INITIAL_STATE);
    formikFiltrosTabelaAporte.resetForm();
    formikValorDistribuido.resetForm();
    setEtapa(EEtapasAporte.FiltrosTabelaAporte);
  };

  useEffect(() => {
    setTipoContaBancaria(enumTipoContaBancaria.CONTA_BANCARIA_EXISTENTE);
    obterContaBancaria();
  }, []);

  return {
    textoDadosBancarios,
    validarContaLoading: isLoadingValidacaoConta,
    obterDadosBancariosLoading: obterDadosBancarios.loading,
    contaBancariaSelecionada,
    handleSelecionarContaBancaria,
    handleVerificarConta,
    handleEtapaAnterior,
  };
};
