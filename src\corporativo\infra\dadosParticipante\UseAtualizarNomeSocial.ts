import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { checkIfAllItemsAreTrue, getSessionItem } from '@cvp/utils';
import {
  INomeSocialPayload,
  INomeSocialPecoPayload,
  IUseNomeSocial,
} from '@src/corporativo/types/dadosParticipante/consultar/IUseAtualizarNomeSocial';
import { PECOS } from '../config/api/endpoints';

export const useAtualizarNomeSocial = (): IUseNomeSocial => {
  const { response, loading, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<INomeSocialPecoPayload, unknown>(
      PECOS.ManterNomeSocial,
      {
        autoFetch: false,
      },
    );

  const editarNomeSocial = async (payload: INomeSocialPayload) => {
    const resultado = await invocarApiGatewayCvpComToken({
      cpf: getSessionItem('cpfCnpj'),
      ...payload,
    });

    return checkIfAllItemsAreTrue([
      !!resultado?.sucessoBFF,
      !!resultado?.sucessoGI,
    ]);
  };

  return {
    response,
    loading,
    editarNomeSocial,
  };
};
