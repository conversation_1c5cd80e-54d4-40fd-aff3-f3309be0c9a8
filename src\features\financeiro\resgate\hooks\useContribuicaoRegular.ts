import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useContribuicaoRegular = (
  hasContaPreenchida: boolean,
): Resgate.IUseContribuicaoRegular => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const selecionarFundoParaContribuicao = (codigoFundo: string): void => {
    formik.setValues({
      ...formik.values,
      fundosParaContribuicao: Resgate.alterarSelecaoFundoContribuicaoFactory({
        fundos: resgateFeatureData?.contribuicaoRegular?.fundos,
        codigoFundo,
        valorContribuicaoRegularlAtual:
          resgateFeatureData?.contribuicaoRegular
            ?.valorContribuicaoRegularlAtual,
      }),
    });
  };

  const fundosContribuicaoRegular: Resgate.IMapearFundosContribuicaoRegularFactoryReturn[] =
    Resgate.mapearFundosContribuicaoRegularFactory({
      fundos: resgateFeatureData?.contribuicaoRegular?.fundos,
      selecionarFundoParaContribuicao,
    });

  const deveRenderizarTabelaContribuicao: boolean =
    Resgate.checkIfAllItemsAreTrue([
      !!formik.values.motivoResgate,
      hasContaPreenchida,
      !!resgateFeatureData?.contribuicaoRegular?.permiteDefinirContribRegular,
    ]);

  return {
    fundosContribuicaoRegular,
    deveRenderizarTabelaContribuicao,
  };
};
