import {
  IFundosParaResgateFactory,
  TableColumn,
} from '@src/features/financeiro/resgate/exports';

export const COLUNAS_SIMULACAO_RESGATE: TableColumn<IFundosParaResgateFactory>[] =
  [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      cell: (row: IFundosParaResgateFactory) => row.selecionar,
      center: true,
      minWidth: '50px',
    },
    {
      name: 'Fundo',
      selector: (row: IFundosParaResgateFactory) => row.descricaoFundo,
      center: true,
      wrap: true,
      minWidth: '180px',
    },
    {
      name: 'Perfil do risco',
      cell: (row: IFundosParaResgateFactory) => row.perfilFundo,
      center: true,
      minWidth: '120px',
    },
    {
      name: 'Rentabilidade (útimos 12 meses)',
      selector: (row: IFundosParaResgateFactory) =>
        row.valorRentabilidadeDozeMeses,
      center: true,
      minWidth: '150px',
    },
    {
      name: 'Taxa adm a.a',
      selector: (row: IFundosParaResgateFactory) => row.taxaAdm,
      center: true,
      minWidth: '90px',
    },
    {
      name: 'Saldo da reserva',
      selector: (row: IFundosParaResgateFactory) => row.saldoDisponivel,
      center: true,
      minWidth: '150px',
    },
    {
      name: 'Valor resgatado',
      center: true,
      cell: (row: IFundosParaResgateFactory) => row.valorResgatado,
      minWidth: '160px',
    },
  ];
