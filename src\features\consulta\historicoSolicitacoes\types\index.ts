import {
  IResponseHistoricoSolicitacoes,
  TableColumn,
  TSelectItem,
} from '@src/features/consulta/historicoSolicitacoes/exports';

export type TTipoSolicitacaoFormatada = {
  text: string;
  value: string;
}[];

export type TDefinirPeriodo = {
  dataInicial: string;
  dataFinal: string;
};

export type TConsultaFiltros = TDefinirPeriodo & {
  tipoSolicitadoValor: string;
};

export type TFiltros = {
  periodoValor: TSelectItem[number];
  tipoSolicitacao: TSelectItem[number];
  filtroAtivo: TConsultaFiltros;
};

export type TUseFiltroHistoricoSolicitacoes = {
  filtros: TFiltros;
  periodoPersonalizado: {
    inicio: string;
    fim: string;
  };
  erroPeriodo?: string;
  selecionarFiltro: (chave: keyof TFiltros, item: TSelectItem) => void;
  selecionarPeriodoPersonalizado: (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => void;

  onClickConsultar: (
    onSubmitCallback: (periodo: TConsultaFiltros) => void,
  ) => void;
};

export type TColunasHistoricoSolicitacoes = (
  obterComprovante: (row: IResponseHistoricoSolicitacoes) => void,
  assinarMovimentacao: (row: IResponseHistoricoSolicitacoes) => void,
  carregandoMovimentacoesIds: string[],
) => TableColumn<IResponseHistoricoSolicitacoes>[];
