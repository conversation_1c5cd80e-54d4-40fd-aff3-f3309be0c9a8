import {
  ALIQUOTA,
  TooltipAliquotaProgressiva,
  TooltipAliquotaRegressiva,
} from '@src/features/financeiro/resgate/exports';

/**
 * Retorna o conteúdo do tooltip baseado no tipo de resgate selecionado.
 *
 * @param {string} tipoResgate - O tipo de regime de alíquota (progressivo ou regressivo).
 * @returns {React.ReactElement} Componente React que representa o conteúdo do tooltip.
 */
export const obterConteudoTooltip = (
  tipoResgate: string,
): React.ReactElement => {
  if (tipoResgate === ALIQUOTA.TIPO_REGIME_PROGRESSIVO)
    return <TooltipAliquotaProgressiva />;
  return <TooltipAliquotaRegressiva />;
};
