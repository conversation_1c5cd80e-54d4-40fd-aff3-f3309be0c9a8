import { tryGetValueOrDefault } from '@cvp/utils';

import { IRecuperarBancosResponse } from '@src/corporativo/types/financeiro/resgate/IRecuperarBancos';
import { IObterBancosCorporativoXPrevFactoryRetorno } from '@src/shared/types/IObterBancosCorporativoXPrevFactory';
import { DADOS_BANCO_CONTA } from '@src/shared/constants/dadosContaBanco';
import LISTA_BANCOS_BASE_PREV from '@src/shared/static/ListaBancosBasePrev.json';

/**
 * Obter e consolidar a lista de bancos do serviço corporativo e da lista base de previdência
 *
 * Esta função unifica os bancos provenientes do serviço corporativo com os bancos da lista base de previdência,
 * padronizando os códigos dos bancos, removendo duplicidades e ordenando pelo código.
 *
 * @param {IRecuperarBancosResponse[] | undefined} dadosRecuperarBancos - Lista de bancos recuperados do serviço corporativo
 * @returns {IObterBancosCorporativoXPrevFactoryRetorno[]} Lista consolidada e ordenada de bancos sem duplicidades
 */
export const obterBancosCorporativoXPrevFactory = (
  dadosRecuperarBancos: IRecuperarBancosResponse[] | undefined,
): IObterBancosCorporativoXPrevFactoryRetorno[] => {
  const listaBancosServicoCorporativo = tryGetValueOrDefault(
    [
      dadosRecuperarBancos?.map(banco => ({
        ...banco,
        codigoBanco: String(banco.codigoBanco).padStart(
          DADOS_BANCO_CONTA.CODIGO_BANCO.QTD_CARACTERES_DEFAULT,
          DADOS_BANCO_CONTA.CODIGO_BANCO.CARACTERE_DEFAULT_ADICIONADO,
        ),
      })),
    ],
    [],
  );

  const listaBancosCorporativoEPrev = listaBancosServicoCorporativo?.filter(
    bancoListaCorporativa =>
      LISTA_BANCOS_BASE_PREV.some(
        bancoListaPrev =>
          bancoListaCorporativa.codigoBanco === bancoListaPrev.codigoBanco,
      ),
  );

  const listaDiferencaBancosEntreOrigens = LISTA_BANCOS_BASE_PREV.filter(
    bancoListaPrev =>
      !listaBancosServicoCorporativo.some(
        bancoListaCorporativa =>
          bancoListaCorporativa.codigoBanco === bancoListaPrev.codigoBanco,
      ),
  );

  const listaBancosConsolidada = [
    ...listaBancosCorporativoEPrev,
    ...listaDiferencaBancosEntreOrigens,
  ];

  return listaBancosConsolidada.sort((a, b) => {
    return a.codigoBanco.localeCompare(b.codigoBanco);
  });
};
