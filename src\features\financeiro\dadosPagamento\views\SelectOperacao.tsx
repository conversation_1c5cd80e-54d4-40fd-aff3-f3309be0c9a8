import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const SelectOperacao: React.FC = () => {
  const { adicionarNovaConta, handleNovaContaChange } =
    DadosPagamento.useAlterarDadosPagamento();
  return (
    <DadosPagamento.GridItem xs="1 / 2">
      <DadosPagamento.ConditionalRenderer condition={adicionarNovaConta}>
        <DadosPagamento.Text variant="text-large-700">
          Operação
        </DadosPagamento.Text>
        <DadosPagamento.Select
          options={DadosPagamento.TIPOS_OPERACAO}
          placeholder="Escolha a operação da conta"
          size="standard"
          variant="box-classic"
          sizeWidth={undefined}
          onChange={([item]) =>
            handleNovaContaChange(
              DadosPagamento.changeEventBuilder('operacao', item.value),
            )
          }
        />
      </DadosPagamento.ConditionalRenderer>
    </DadosPagamento.GridItem>
  );
};

export default SelectOperacao;
