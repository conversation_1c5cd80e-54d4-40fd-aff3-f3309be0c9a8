import {
  obterPayloadCalcularResgateFactory,
  ICalcularResgateDadosEncargo,
  IConsultarDetalhesDaAliquotaFactory,
  IConsultarDetalhesDaAliquotaFactoryRetorno,
} from '@src/features/financeiro/resgate/exports';

/**
 * Consultar os detalhes de alíquota para resgate de previdência.
 *
 * O fluxo de execução:
 * 1. Calcula o resgate com a alíquota informada
 * 2. Consulta o resumo da alíquota com base no número do resgate
 * 3. Consulta os detalhes do cálculo com base no número do resgate
 * 4. Retorna os dados consolidados do cálculo, resumo e detalhes
 *
 * @param {IConsultarDetalhesDaAliquotaFactory} params - Objeto com os parâmetros necessários
 * @param {FormikProps<IFormikValuesSimulacaoResgate>} params.formik - Formulário com os dados do resgate
 * @param {string} params.aliquotaAtual - Tipo de alíquota (progressiva ou regressiva)
 * @param {ICalcularValorResgateRestanteRetorno} params.resultadoCalculoResgateRestante - Resultado do cálculo de resgate restante
 * @param {Function} params.calcularResgate - Função para calcular o resgate
 * @param {Function} params.consultarResumoAliquota - Função para consultar o resumo da alíquota
 * @param {Function} params.consultarDetalheCalculo - Função para consultar detalhes do cálculo
 *
 * @returns {Promise<IConsultarDetalhesDaAliquotaFactoryRetorno>} Retorna os dados do cálculo, resumo e detalhes da alíquota
 */
export const consultarDetalhesDaAliquotaFactory = async ({
  formik,
  aliquotaAtual,
  resultadoCalculoResgateRestante,
  calcularResgate,
  consultarResumoAliquota,
  consultarDetalheCalculo,
}: IConsultarDetalhesDaAliquotaFactory): Promise<IConsultarDetalhesDaAliquotaFactoryRetorno> => {
  const retornoCalculo = await calcularResgate(
    obterPayloadCalcularResgateFactory({
      formik,
      aliquota: aliquotaAtual,
      resultadoCalculoResgateRestante,
    }),
  );

  const dadosEncargo: ICalcularResgateDadosEncargo | undefined =
    retornoCalculo?.entidade?.dadosEncargo;

  const retornoResumo = await consultarResumoAliquota({
    numeroResgate: dadosEncargo?.numeroResgate,
  });

  const retornoDetalhes = await consultarDetalheCalculo({
    numeroResgate: dadosEncargo?.numeroResgate,
  });

  return {
    calculo: dadosEncargo,
    resumo: retornoResumo?.entidade,
    detalhado: retornoDetalhes?.entidade,
  };
};
