import {
  TableColumn,
  formatarValorPadraoBrasileiro,
  porcentagem,
  TTransferenciaFundo,
} from '../exports';

type TColumnFundo = TTransferenciaFundo & {
  selecionar?: string;
  transferenciaValor?: string;
};

type TColumn = TableColumn<TColumnFundo>;

export const trasferenciaEntreFundosColunasDestino: TColumn[] = [
  {
    name: 'Selecionar',
    selector: ({ selecionar }) => selecionar ?? '',
    width: '100px',
    center: true,
  },
  {
    name: 'Fundo',
    selector: ({ desFundo }) => desFundo,
    center: true,
  },
  {
    name: 'Perfil do risco',
    selector: ({ desPerfilFundo }) => desPerfilFundo,
    center: true,
  },
  {
    name: 'Rentabilidade(útimos 12 meses)',
    selector: ({ pctRentabUltimoAno }) =>
      porcentagem.mask(pctRentabUltimoAno ?? ''),
    center: true,
  },
  {
    name: 'Taxa adm',
    selector: () => formatarValorPadraoBrasileiro('0'),
    center: true,
  },
  {
    name: 'Valor transferido',
    selector: ({ transferenciaValor }) => {
      if (typeof transferenciaValor === 'string')
        return formatarValorPadraoBrasileiro(transferenciaValor);
      return transferenciaValor ?? '';
    },
    center: true,
  },
];
