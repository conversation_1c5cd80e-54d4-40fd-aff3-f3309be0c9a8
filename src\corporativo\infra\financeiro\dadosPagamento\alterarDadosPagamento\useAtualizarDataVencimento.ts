import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IRequestAtualizarDiaVencimento,
  IResponseAlteracaoDiaVencimento,
} from '@src/features/financeiro/types/AlteracaoDiaVencimento';
import { useContext } from 'react';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { getSessionItem } from '@cvp/utils';

interface IAtualizarDataVencimentoProps {
  dataVencimento?: string;
}
const useAtualizarDataVencimento = ({
  dataVencimento = '',
}: IAtualizarDataVencimentoProps) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj') ?? '';

  const {
    loading: loadingAtualizaDiaVencimento,
    response: responseAtualizaDiaVencimento,
    invocarApiGatewayCvpComToken: atualizarDiaVencimento,
    setResponse,
  } = useApiGatewayCvpInvoker<
    IRequestAtualizarDiaVencimento,
    IResponseAlteracaoDiaVencimento
  >(PECOS.AtualizarDataVencimento, {
    data: {
      numeroCertificado: certificadoAtivo?.certificadoNumero,
      cpfCnpj: cpfCnpjSession,
      dataVencimento,
    },
    autoFetch: false,
  });

  return {
    loadingAtualizaDiaVencimento,
    responseAtualizaDiaVencimento,
    atualizarDiaVencimento,
    setResponse,
  };
};

export default useAtualizarDataVencimento;
