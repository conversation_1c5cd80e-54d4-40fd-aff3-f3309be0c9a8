import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Obter o payload necessário para calcular um resgate
 *
 * Esta função constrói o objeto payload com os dados essenciais para realizar
 * o cálculo do resgate junto à API, incluindo regime tributário, tipo de resgate,
 * detalhes dos fundos e valor total.
 *
 * @param {Object} params - Parâmetros para construção do payload
 * @param {Object} params.formik - Objeto formik contendo os valores do formulário de simulação
 * @param {string} params.aliquota - Regime tributário selecionado (progressivo ou regressivo)
 * @param {Object} params.resultadoCalculoResgateRestante - Objeto com os resultados do cálculo de resgate restante
 * @returns {IObterPayloadCalcularResgateFactoryRetorno} Payload formatado para envio à API de cálculo de resgate
 */
export const obterPayloadCalcularResgateFactory = ({
  formik,
  aliquota,
  resultadoCalculoResgateRestante,
}: Resgate.IObterPayloadCalcularResgateFactory): Resgate.IObterPayloadCalcularResgateFactoryRetorno => ({
  tipoRegimeTributario: aliquota,
  tipoResgate: Resgate.getTernaryResult(
    formik.values.tipoResgate === Resgate.TIPOS_RESGATE.PARCIAL.id,
    Resgate.TIPOS_RESGATE.PARCIAL.id,
    Resgate.TIPOS_RESGATE.TOTAL.id,
  ),
  detalhesFundos: Resgate.tryGetValueOrDefault(
    [Resgate.obterDetalhesFundosFactory(formik.values.fundosParaResgate)],
    [],
  ),
  valorResgateTotal: resultadoCalculoResgateRestante.calculoTotal,
});
