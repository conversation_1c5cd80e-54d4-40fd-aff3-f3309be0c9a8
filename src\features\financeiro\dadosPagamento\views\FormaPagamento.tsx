import {
  converterFormaPagamento,
  tryGetValueOrDefault,
  useConsultaCertificadosPorCpf,
  getTernaryResult,
  obterCodigoOperacaoENumeroConta,
  Grid,
  GridItem,
  Text,
  IResponseDadosCertificadosPorCpf,
} from '@src/features/financeiro/dadosPagamento/exports';

const FormaPagamento: React.FC<{
  pagamentoRegular?: IResponseDadosCertificadosPorCpf;
}> = ({ pagamentoRegular }) => {
  const { dadosCertificado } = useConsultaCertificadosPorCpf();

  const operacao: string = tryGetValueOrDefault(
    [
      obterCodigoOperacaoENumeroConta(
        tryGetValueOrDefault([dadosCertificado?.[0]?.numeroConta], ''),
      ).operacao,
    ],
    '-',
  );

  const conta: string = tryGetValueOrDefault(
    [
      obterCodigoOperacaoENumeroConta(
        tryGetValueOrDefault([dadosCertificado?.[0]?.numeroConta], ''),
      ).numeroConta,
    ],
    '-',
  );

  return (
    <Grid margin="30px 0px" style={{ justifyContent: 'space-between' }}>
      <GridItem md="1/1" xs="1/2" xl="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          Forma de pagamento
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-05">
          {converterFormaPagamento(pagamentoRegular?.metodoPagamento)}
        </Text>
      </GridItem>
      <GridItem md="1/1" xs="1/2" xl="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          Operação
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-05">
          {operacao}
        </Text>
      </GridItem>
      <GridItem md="1/1" xs="1/2" xl="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          Agência e conta
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-05">
          {getTernaryResult(
            !!pagamentoRegular?.numeroAgencia,
            `${pagamentoRegular?.numeroAgencia}
            - ${conta}
            - ${pagamentoRegular?.digitoConta}`,
            '-',
          )}
        </Text>
      </GridItem>
      <GridItem md="1/1" xs="1/2" xl="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          Dia do débito
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-05">
          {dadosCertificado?.[0]?.diaPagamento}
        </Text>
      </GridItem>
    </Grid>
  );
};

export default FormaPagamento;
