import { getTernaryResult } from '@cvp/utils';
import { IconPVPlusCircle } from '@cvp/design-system-caixa';
import type { IButtonRedirectProps } from '@cvp/componentes-posvenda/types/types/components/ILayoutPlataforma';
import { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';

export const REDIRECT_BUTTONS = (
  route?: string,
): Array<IButtonRedirectProps> => [
  {
    title: 'Contratar',
    urlOrAction: ROUTES.VENDA,
    iconPosition: 'leftIcon',
    icon: <IconPVPlusCircle size="nano" />,
  },
  ...getTernaryResult(
    route === 'sinistros',
    [],
    [
      {
        title: 'Consultar sinistros',
        urlOrAction: ROUTES.SINISTROS,
        variant: 'secondary-outlined',
      } as const,
    ],
  ),
];
