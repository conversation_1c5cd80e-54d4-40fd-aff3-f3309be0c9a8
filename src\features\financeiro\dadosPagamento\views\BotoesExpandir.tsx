import * as Boto<PERSON> from '@src/features/financeiro/dadosPagamento/exports';

const BotoesExpandir: React.FC<Botoes.TAlterarDadosPagamentoProps> = ({
  open,
  onClose,
  onEdit,
}) => {
  const { permissoesMatrizAcesso } = Botoes.useContext(
    Botoes.MatrizAcessoContext,
  );
  const theme = Botoes.useTheme();
  return (
    <Botoes.FlexBox $alignItems="center" $justifyContent="space-between">
      <Botoes.ExpandButton type="button" onClick={onClose}>
        {open ? (
          <Botoes.IconExpandLessSharp
            size="small"
            color={theme.color.palette.primary[90]}
            style={{ margin: '0 4px' }}
          />
        ) : (
          <Botoes.IconExpandMoreSharp
            size="small"
            color={theme.color.palette.primary[90]}
            style={{ margin: '0 4px' }}
          />
        )}
        <Botoes.Text
          variant="text-standard-600"
          fontColor="content-highlight-01"
        >
          {Botoes.BOTAO_HISTORICO}
          <Botoes.ToolTip
            maxWidth="352px"
            text={
              (
                <Botoes.Text variant="text-small-600">
                  {Botoes.MSG_HISTORICO.MARQUE} <br />{' '}
                  {Botoes.MSG_HISTORICO.PENDENTE}
                </Botoes.Text>
              ) as unknown as string
            }
          >
            <Botoes.IconInfoOutlined
              size="small"
              color={theme.color.palette.primary[90]}
            />
          </Botoes.ToolTip>
        </Botoes.Text>
      </Botoes.ExpandButton>
      <Botoes.MatrizAcessoRenderizadorAlgumaPermissao
        matrizAcesso={permissoesMatrizAcesso}
        permissoesComponente={[
          ...Botoes.ALTERACOES_COMPONENTES_PERMISSOES.alteracao_dia_vencimento
            .permissions,
          ...Botoes.ALTERACOES_COMPONENTES_PERMISSOES
            .alterar_forma_dados_pagamento.permissions,
        ]}
      >
        <Botoes.SwitchCase fallback={undefined}>
          <Botoes.Match when={AppConfig?.MFE_ENV === 'development'}>
            <Botoes.Button
              variant="secondary-outlined"
              leftIcon={<Botoes.IconEditOutlinedSharp size="small" />}
              size="small"
              onClick={onEdit}
            >
              Editar
            </Botoes.Button>
          </Botoes.Match>
        </Botoes.SwitchCase>
      </Botoes.MatrizAcessoRenderizadorAlgumaPermissao>
    </Botoes.FlexBox>
  );
};

export default BotoesExpandir;
