import {
  CampoTexto,
  useAlterarDadosPagamento,
  ConditionalRenderer,
  Grid,
} from '@src/features/financeiro/dadosPagamento/exports';

const CamposNovaConta: React.FC = () => {
  const { adicionarNovaConta, novaConta } = useAlterarDadosPagamento();

  return (
    <ConditionalRenderer condition={adicionarNovaConta}>
      <Grid>
        <CampoTexto
          label="Agência"
          name="numeroAgencia"
          value={novaConta.numeroAgencia}
          placeholder="Número da agência"
          maxNumber={4}
        />
        <CampoTexto
          label="Número da Conta"
          name="numeroConta"
          value={novaConta.numeroConta}
          placeholder="Número da conta"
          maxNumber={10}
        />
        <CampoTexto
          label="Dígito da Conta"
          name="digitoConta"
          value={novaConta.digitoConta}
          placeholder="Dígito da conta"
          maxNumber={1}
        />
      </Grid>
    </ConditionalRenderer>
  );
};

export default CamposNovaConta;
