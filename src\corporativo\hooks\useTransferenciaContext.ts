import { useContext } from 'react';

import { TransferenciaContext } from '@src/corporativo/context/financeiro/transferencias/TransferenciaContext';
import { TTransferenciaContext } from '@src/corporativo/types/transferencias';

export const useTransferenciaContext = (): TTransferenciaContext => {
  const context = useContext(TransferenciaContext);
  if (!context) {
    throw new Error(
      'useTransferenciaContext deve ser usado dentro do TransferenciaProvider',
    );
  }
  return context;
};
