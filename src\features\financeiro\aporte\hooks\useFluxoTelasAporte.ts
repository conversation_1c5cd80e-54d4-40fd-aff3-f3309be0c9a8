import {
  checkIfAllItemsAreTrue,
  EEtapasAporte,
  FORMA_PAGAMENTO,
  getTernaryResult,
  PrevidenciaContext,
  tryGetValueOrDefault,
  useAporteContext,
  useAporteServiceContext,
  useContext,
  useEffect,
} from '@src/features/financeiro/aporte/exports';

type TUseFluxoTelasAporte = {
  etapa: EEtapasAporte;
  loadingFundos: boolean;
  loadingExtratoUnificado: boolean;
};

export const useFluxoTelasAporte = (): TUseFluxoTelasAporte => {
  const { setIsClientePep } = useContext(PrevidenciaContext);

  const { etapa, filtroTabelaFundos, setEtapa, setFundos, setItensExtrato } =
    useAporteContext();

  const { clientePep, fundosDistribuicao, extratoUnificado } =
    useAporteServiceContext();

  const loadingFundos = fundosDistribuicao.loading;

  const loadingExtratoUnificado = extratoUnificado.loading;

  const perfilIsEmpty = filtroTabelaFundos.perfilInvestidor.length !== 1;

  const formaPagamentoIsEmpty = filtroTabelaFundos.formaPagamento === '';

  const dataDebitoIsEmpty = filtroTabelaFundos.dataDebito === '';

  const filtroPreenchido = getTernaryResult(
    filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.boleto,
    checkIfAllItemsAreTrue([!perfilIsEmpty, !formaPagamentoIsEmpty]),

    checkIfAllItemsAreTrue([
      !perfilIsEmpty,
      !formaPagamentoIsEmpty,
      !dataDebitoIsEmpty,
    ]),
  );

  const obterFundos = async () => {
    const response = await fundosDistribuicao.invocarApiGatewayCvpComToken({
      valorContribuicao: filtroTabelaFundos.valorContribuicao,
      distribuicaoPersonalizada: 'true',
    });

    setFundos(response?.entidade);
  };

  const obterClientePep = async () => {
    const response = await clientePep.invocarApiGatewayCvpComToken();
    setIsClientePep(tryGetValueOrDefault([response?.entidade?.pep], false));
  };

  const obterExtratoUnificado = async () => {
    const response = await extratoUnificado.invocarApiGatewayCvpComToken();

    if (response?.entidade?.itensExtrato) {
      setItensExtrato(response?.entidade?.itensExtrato);
    }
  };

  useEffect(() => {
    obterClientePep();
  }, []);

  useEffect(() => {
    if (filtroPreenchido) {
      obterFundos();
      obterExtratoUnificado();
      setEtapa(EEtapasAporte.FundosDistribuicaoAporte);
    }
  }, [filtroTabelaFundos.perfilInvestidor]);

  return { etapa, loadingFundos, loadingExtratoUnificado };
};
