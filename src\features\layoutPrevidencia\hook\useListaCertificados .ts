import { useContext, useEffect } from 'react';
import {
  atualizarSituacoes,
  PrevidenciaContext,
  useCertificadosPrevidencia,
  useFilter,
} from '../exports';

const useListaCertificados = () => {
  const { setStatusContratoFilter, setCertificadoAtivo } =
    useContext(PrevidenciaContext);
  const { response, loading } = useCertificadosPrevidencia();

  const { filterResponse } = useFilter();
  const filteredResponse = filterResponse(response);

  useEffect(() => {
    const status = atualizarSituacoes(response);
    setStatusContratoFilter(status);
  }, [response, setStatusContratoFilter]);

  return { filteredResponse, setCertificadoAtivo, loading };
};

export default useListaCertificados;
