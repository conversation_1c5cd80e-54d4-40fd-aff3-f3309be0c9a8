import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IAnoHistorico,
  IObterHistoricoPorAnoPayload,
  IResponseDetalhesHistoricoAno,
  IUseObterHistoricoAtualizacoesPorAno,
} from '@src/corporativo/types/consulta/IReajusteAnualPlano';
import { useContext } from 'react';

const DEFAULT_RETURN: IAnoHistorico[] = [];

export const useObterHistoricoPorAno =
  (): IUseObterHistoricoAtualizacoesPorAno => {
    const cpf = String(getSessionItem('cpfCnpj'));
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const { loading, response, invocarApiGatewayCvpComToken } =
      useApiGatewayCvpInvoker<
        IObterHistoricoPorAnoPayload,
        IResponseDetalhesHistoricoAno
      >(PECOS.ObterAnoHistorico, {
        autoFetch: false,
        data: {
          cpf,
          numeroCertificado: certificadoAtivo.certificadoNumero,
        },
      });

    const obterHistoricoPorAno = async (ano: string) => {
      await invocarApiGatewayCvpComToken({ ano });
    };

    return {
      response: tryGetValueOrDefault(
        [response?.entidade?.retornoAnoHistoricoProxy?.anosHistorico],
        DEFAULT_RETURN,
      ),
      loading,
      obterHistoricoPorAno,
    };
  };
