import {
  IDadosParticipanteEntidade,
  IDadosParticipanteFormEntidade,
  IDadosParticipanteState,
  Utils,
} from '../exports';

export type TTipoTelefone = {
  tipoUm: string;
  tipoDois: string;
};

export const dadosParticipanteInitialState: IDadosParticipanteState = {
  editando: false,
  salvando: false,
  buscandoCep: false,
  assinaturaValida: false,
  idCidade: '',
  dadosParticipante: {} as IDadosParticipanteFormEntidade,
  controlesLGPD: {
    dadosPessoaisOutrosProdutos: false,
    dadosPessoaisParceiros: false,
  },
  consetimentoOriginal: {
    dadosPessoaisOutrosProdutos: false,
    dadosPessoaisParceiros: false,
  },
};

const procurarPorTipoTelefone = (
  dados: IDadosParticipanteEntidade,
  tipoTelefone: TTipoTelefone,
) => {
  return dados?.telefones.find(
    e =>
      e.tipoTelefone === tipoTelefone.tipoUm ||
      e.tipoTelefone === tipoTelefone.tipoDois,
  );
};

export const criarStateInicialDadosParticipante = (
  dadosParticipante: IDadosParticipanteEntidade,
): IDadosParticipanteState => {
  const telefoneComercial = procurarPorTipoTelefone(dadosParticipante, {
    tipoUm: 'C',
    tipoDois: 'O',
  });
  const telefoneResidencial = procurarPorTipoTelefone(dadosParticipante, {
    tipoUm: 'D',
    tipoDois: 'T',
  });
  return {
    ...dadosParticipanteInitialState,
    dadosParticipante: {
      ...dadosParticipante,
      nomeSocial: Utils.tryGetValueOrDefault(
        [dadosParticipante.nomeSocial],
        '-',
      ),
      cep: Utils.cep.mask(
        Utils.tryGetValueOrDefault([dadosParticipante.cep], '-'),
      ),
      endereco: Utils.tryGetValueOrDefault([dadosParticipante.endereco], '-'),
      numero: Utils.tryGetValueOrDefault([dadosParticipante.numero], '-'),
      complemento: Utils.tryGetValueOrDefault(
        [dadosParticipante.complemento],
        '-',
      ),
      bairro: Utils.tryGetValueOrDefault([dadosParticipante.bairro], '-'),
      cidade: Utils.tryGetValueOrDefault([dadosParticipante.cidade], '-'),
      uf: Utils.tryGetValueOrDefault([dadosParticipante.uf], '-'),
      email: Utils.tryGetValueOrDefault([dadosParticipante.email], '-'),
      nome: Utils.tryGetValueOrDefault([dadosParticipante.nome], '-'),
      dataNascimento: Utils.tryGetValueOrDefault(
        [Utils.formatarDataHoraAmigavel(dadosParticipante.dataNascimento)],
        '-',
      ),

      celular: Utils.telefoneUtils.mask(
        Utils.tryGetValueOrDefault(
          [
            `${telefoneComercial?.codigoArea}${telefoneComercial?.numeroTelefone}`,
          ],
          '-',
        ),
        { type: 'Residencial' },
      ),
      telefone: Utils.telefoneUtils.mask(
        Utils.tryGetValueOrDefault(
          [
            `${telefoneResidencial?.codigoArea}${telefoneResidencial?.numeroTelefone}`,
          ],
          '-',
        ),
        { type: 'ResidencialSemPrefix' },
      ),
    },
  };
};
