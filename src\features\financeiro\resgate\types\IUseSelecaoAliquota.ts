import * as Resgate from '@src/features/financeiro/resgate/exports';

export interface IUseSelecaoAliquotaRetorno {
  confirmarSimulacao: () => Promise<void>;
  reiniciarSimulacao: () => void;
  isLoadingConfirmacaoSimulacao: boolean;
  toggleModalDetalhamento: () => void;
  isOpen: boolean;
  toggleModalConfirmacaoAliquota: () => void;
  cancelarEscolhaAliquota: () => void;
  confirmarEscolhaAliquota: () => void;
  isOpenModalDetalhamento: boolean;
  obterDadosPorAliquota: (
    tipoAliquota?: string,
  ) => Resgate.IObterDadosPorAliquotaRetorno;
  selecionarOpcaoAliquota: (opcaoSelecionada: string) => void;
  validarAliquotaDesabilitada: (codigoAliquota: string) => boolean;
  mensagens: Record<string, string>;
  dadosResumoAliquotaSelecionada: Resgate.IObterResumoAliquotaSelecionadaRetorno;
  objetoEmail: Resgate.IObjetoEmailSimulacaoResgate;
  customizarEstiloDataTableDetalhesAliquota: (
    style: Resgate.IConditionalRowStyleDetalhesAliquota,
  ) => Resgate.IConditionalRowStyleDetalhesAliquota;
}
