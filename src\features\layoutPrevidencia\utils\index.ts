import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  converterBase64,
} from '@cvp/utils';
import {
  ERRO_MENSAGEM_DATA_FUTURA,
  ERRO_MENSAGEM_DATA_INICIAL_MAIOR_QUE_DATA_FINAL,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF,
} from '../constants/imprDocModalTextos';
import { TGetPdfDocumentoSelecionadoResponse } from '../types/modalImprDocTypes';

interface IRetornaPeriodoDateISOString {
  dataInicio: string;
  dataFim: string;
}

export const diaAtual = new Date().getDate();
export const mesAtual = new Date().getMonth() + 1;
export const indexMesAtual = new Date().getMonth();
export const anoAtual = new Date().getFullYear();

export const retornaPrimeiroDiaDoAno = (ano: number): Date =>
  new Date(ano, 0, 1);

export const retornaUltimoDiaDoAno = (ano: number): Date =>
  new Date(ano, 11, 31);

export const retornaDateUltimoDiaDoMes = (mes: number): Date =>
  new Date(anoAtual, mes + 1, 0);

export const retornaDatePrimeiroDiaDoMes = (mes: number): Date =>
  new Date(anoAtual, mes, 1);

export const retornaDateDiaAtual = (): Date => new Date();

export const retornaPeriodoDateISOString = (
  mesOuAno: string,
): IRetornaPeriodoDateISOString => {
  if (mesOuAno.length === 4) {
    const ano = Number(mesOuAno);

    const primeiroDiaDoAnoISOString =
      retornaPrimeiroDiaDoAno(ano).toISOString();
    const ultimoDiaDoAnoISOString = retornaUltimoDiaDoAno(ano).toISOString();

    if (ano === anoAtual) {
      const dataAtualISOString = retornaDateDiaAtual().toISOString();

      return {
        dataInicio: primeiroDiaDoAnoISOString,
        dataFim: dataAtualISOString,
      };
    }

    return {
      dataInicio: primeiroDiaDoAnoISOString,
      dataFim: ultimoDiaDoAnoISOString,
    };
  }

  const mes = Number(mesOuAno);
  const primeiroDiaDoMes = retornaDatePrimeiroDiaDoMes(mes);
  const ultimoDiaDoMes = retornaDateUltimoDiaDoMes(mes);
  const dataInicioISOString = primeiroDiaDoMes.toISOString();
  const dataFimISOString = ultimoDiaDoMes.toISOString();

  if (mes === indexMesAtual) {
    const dataAtualISOString = retornaDateDiaAtual().toISOString();

    return {
      dataInicio: dataInicioISOString,
      dataFim: dataAtualISOString,
    };
  }

  return {
    dataInicio: dataInicioISOString,
    dataFim: dataFimISOString,
  };
};

export const retornaArrayUltimos5Anos = (): string[] => {
  return Array.from({ length: 5 }, (_, i) => String(anoAtual - i - 1));
};

export const formatarBase64ToUrl = (base64Pdf?: string): string => {
  if (!base64Pdf) return '';
  const blobUrl = converterBase64(base64Pdf);
  return URL.createObjectURL(blobUrl);
};

export const parseAAAAMMDDStringToDateISOString = (date: string): string => {
  const [year, month, day] = date.split('-').map(Number);
  return new Date(year, month - 1, day).toISOString();
};

export const parseDateISOStringToAAAAMMDDString = (
  ISOString: string,
): string => {
  if (!ISOString) return '';
  const date = new Date(ISOString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const isValidDateRange = (
  startDate: string,
  endDate: string,
  errorCallback?: (err: string) => void,
): boolean => {
  if (
    checkIfSomeItemsAreTrue([
      startDate >= new Date().toISOString(),
      endDate >= new Date().toISOString(),
    ])
  ) {
    if (errorCallback) errorCallback(ERRO_MENSAGEM_DATA_FUTURA);
    return false;
  }

  if (!checkIfAllItemsAreTrue([!!startDate, !!endDate])) return true;

  const isDataInicialMenorQueDataFinal = startDate <= endDate;

  if (!isDataInicialMenorQueDataFinal) {
    if (errorCallback)
      errorCallback(ERRO_MENSAGEM_DATA_INICIAL_MAIOR_QUE_DATA_FINAL);
  }
  return isDataInicialMenorQueDataFinal;
};

export const selecionarChavesObj = <T extends object>(
  obj: T,
  chaves: (keyof T)[],
): Partial<T> => {
  return chaves.reduce((acc, chave) => {
    if (chave in obj) {
      acc[chave] = obj[chave];
    }
    return acc;
  }, {} as Partial<T>);
};

export const getPECOErrorMessageOrDefault = (
  response: TGetPdfDocumentoSelecionadoResponse,
): string => {
  if (!response || typeof response === 'string')
    return MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF;
  return response.mensagens?.length && response.mensagens[0].descricao
    ? response.mensagens[0].descricao
    : MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF;
};

export const retornaDateISOStringPeriodoUltimosDias = (
  ultimosDias: string,
): IRetornaPeriodoDateISOString => {
  const dateMiliSeconds = retornaDateDiaAtual().getTime();
  const daysToMiliSeconds = Number(ultimosDias) * 1000 * 60 * 60 * 24;
  const dataInicio = new Date(
    dateMiliSeconds - daysToMiliSeconds,
  ).toISOString();
  const dataFim = new Date(dateMiliSeconds).toISOString();

  return {
    dataInicio,
    dataFim,
  };
};
