import { TTiposDePerfis } from '@src/shared/types/TTiposDePerfis';

export interface IResponseFundosPorCertificado {
  retornoCertificado: {
    certificadoMensagens: TResponseMensagens;
    certificadoOrigem: TResponseOrigem;
    certificadoDestino: [];
    certificadosIncompativeis: [];
    fundosDestino: IResponseFundo[];
    indSimulacaoValida: string;
    qtdMaximaFundos: string;
    vlrMinNovoFundo: string;
    vlrMinPermancenciaFundo: string;
    vlrMinTransferencia: string;
  };
  sucesso: boolean;
  msgErroExcessao?: string;
}

export type TResponseOrigem = {
  codConta: string;
  dthAbertura: string;
  dthAposentadoria: string;
  dthEmissao: string;
  fundos: IResponseFundo[];
  pessoa: TResponsePessoa;
  produto: TResponseProduto;
  status: string;
  valorTotalBeneficios: string;
};

export type TResponseProduto = {
  codProduto: string;
  desProduto: string;
  desSubProdutoCategoria: string;
  estaAgregado: string;
};

export type TResponsePessoa = {
  codPessoa: string;
  nomPessoa: string;
  pessoaFisica: TResponsePessoaFisica;
};

export type TResponsePessoaFisica = {
  nomePessoa: string;
  cpf: string;
};

export type TResponseMensagens = {
  codMensagem: string;
  desMensagem: string;
};

export interface IResponseFundo {
  codFundo: string;
  codPerfilFundo: string;
  desFundo: string;
  desPerfilFundo: TTiposDePerfis;
  numCnpj: string;
  pctDistribuicao: number;
  pctRentabUltimoAno: number;
  vlrSaldo: number;
}
