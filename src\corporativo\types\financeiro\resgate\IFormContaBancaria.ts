export interface IContaExistente {
  nomeCanal: string;
  codigoBanco: string;
  tipoCanal: string;
  codigoContaBancaria: string;
  digitoAgencia: string;
  digitoConta: string;
  tipoContaBancaria: string;
  descricaoTipoContaBancaria: string;
  nomeBanco: string;
  numeroAgencia: string;
}

export interface INovaConta {
  banco: IBanco;
  agencia: string;
  conta: string;
  digito: string;
  operacao: string;
}

export interface IBanco {
  value: string;
  label: string;
}

export interface ITipoConta {
  codigo: string;
  descricao: string;
}
