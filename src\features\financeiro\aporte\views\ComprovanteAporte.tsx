import {
  Alert,
  ALERTS,
  BOTOES,
  Button,
  Text,
  ComprovanteAporteDadosAporte,
  ComprovanteAporteFundos,
  Container,
  FORMA_PAGAMENTO,
  Grid,
  GridItem,
  IconInfoRound,
  Match,
  SwitchCase,
  useComprovanteAporte,
  FILTRO_APORTE,
} from '@src/features/financeiro/aporte/exports';

const ComprovanteAporte: React.FC = () => {
  const {
    filtroTabelaFundos,
    fundosExistentesSelecionados,
    fundosNovosSelecionados,
    handleImprimirBoleto,
    handleImprimirComprovanteAporte,
    handleFinalizarAtendimento,
  } = useComprovanteAporte();

  return (
    <Container>
      <Grid margin="18" container>
        <GridItem xs="1">
          <Text variant="text-big-400" fontColor="content-neutral-06">
            {FILTRO_APORTE.contribuicoes}
          </Text>
          <SwitchCase>
            <Match
              when={
                filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.boleto
              }
            >
              <Alert
                variant="success-01"
                icon={<IconInfoRound size="large" color="#127527" />}
              >
                <Text variant="text-standard-400">
                  {ALERTS.sucesso_aporte_boleto}
                </Text>
              </Alert>
            </Match>

            <Match
              when={
                filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito
              }
            >
              <Alert
                variant="success-01"
                icon={<IconInfoRound size="medium" color="#127527" />}
              >
                <Text variant="text-standard-400">
                  {ALERTS.sucesso_aporte_debito}
                </Text>
              </Alert>
            </Match>
          </SwitchCase>
        </GridItem>

        <ComprovanteAporteDadosAporte
          dadosAporte={filtroTabelaFundos}
          fundosExistentes={fundosExistentesSelecionados}
          fundosNovos={fundosNovosSelecionados}
        />
      </Grid>
      <ComprovanteAporteFundos
        fundosExistentes={fundosExistentesSelecionados}
        fundosNovos={fundosNovosSelecionados}
      />
      <Grid justify="flex-end" margin="0px 7px">
        <GridItem>
          <Button onClick={handleImprimirComprovanteAporte} variant="secondary">
            {BOTOES.comprovante}
          </Button>
        </GridItem>
        <GridItem>
          <Button onClick={handleFinalizarAtendimento} variant="primary">
            {BOTOES.finalizar}
          </Button>
        </GridItem>

        <Match
          when={filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.boleto}
        >
          <GridItem>
            <Button onClick={handleImprimirBoleto} variant="secondary">
              {BOTOES.boleto}
            </Button>
          </GridItem>
        </Match>
      </Grid>
    </Container>
  );
};

export default ComprovanteAporte;
