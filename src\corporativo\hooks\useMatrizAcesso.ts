import {
  ICertificadoPrevidenciaResponse,
  OpcoesPrimeiroSelect,
  tabsFromRouterFactory,
} from '@src/features/layoutPrevidencia/exports';
import { getSessionItem } from '@cvp/utils';
import { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
import { TTabItem } from '@src/features/layoutPrevidencia/types/tabsTypes';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IMatrizAcessoReturn,
  ITokenOperadorMatrizAcessoCliente,
  TMatrizAcessoGIResponse,
} from '@src/corporativo/types/matrizAcesso/IUseMatrizAcesso';
import { PECO_MATRIZ } from '@src/corporativo/infra/config/api/endpoints';
import { IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES } from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';

const useMatrizAcesso = (): IMatrizAcessoReturn => {
  const {
    loading: isMatrizAcessoLoading,
    invocarApiGatewayCvpComToken: fetchMatrizAcesso,
  } = useApiGatewayCvpInvoker<
    { codCliente: string | null },
    ITokenOperadorMatrizAcessoCliente
  >(PECO_MATRIZ.ConsultarMatrizAcessoPrevidencia, {
    autoFetch: false,
    data: {
      codCliente: getSessionItem('cpfCnpj'),
    },
  });

  const verificaSeComponentePossuiPermissoesMatrizAcesso = (
    matrizAcessoPermissoes: string[],
    permissoesComponente: string[],
  ): boolean => {
    return permissoesComponente.every(permissao =>
      matrizAcessoPermissoes.includes(permissao),
    );
  };

  const selectModalImprimirDocumentosPrimeirasOpcoesPermissaoCertificado = (
    userPermissions: string[],
  ) => {
    return OpcoesPrimeiroSelect.filter(opcaoSelect =>
      verificaSeComponentePossuiPermissoesMatrizAcesso(
        userPermissions,
        IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES[opcaoSelect.value].permissions,
      ),
    );
  };

  const setPermissoesUsuarioContexto = (
    matrizAcesso: TMatrizAcessoGIResponse,
    certificadoAtivo: ICertificadoPrevidenciaResponse,
    setMatrizAcessoPermissions: (value: React.SetStateAction<string[]>) => void,
    setModalImprimirDocumentosPrimeiroSelectOpcoes: (
      value: React.SetStateAction<SelectItem[]>,
    ) => void,
    setCertificadoTabs: React.Dispatch<React.SetStateAction<TTabItem[]>>,
  ): void => {
    if (matrizAcesso && matrizAcesso.sucessoBFF) {
      const userPermissionsByCertificadoAtivo =
        matrizAcesso.entidade?.matrizAcessoCliente?.lstWebSession.filter(
          webSession =>
            webSession.certificado === certificadoAtivo.certificadoNumero,
        );

      if (userPermissionsByCertificadoAtivo?.length) {
        const userPermissions =
          userPermissionsByCertificadoAtivo[0].lstServicesAvailable.map(
            permission => permission.operation,
          );

        const tabs = tabsFromRouterFactory(userPermissions);
        setMatrizAcessoPermissions(userPermissions);
        const valuesPrimeiroSelectFiltrado =
          selectModalImprimirDocumentosPrimeirasOpcoesPermissaoCertificado(
            userPermissions,
          );

        setModalImprimirDocumentosPrimeiroSelectOpcoes(
          valuesPrimeiroSelectFiltrado,
        );
        setCertificadoTabs(tabs);
      }
    }
  };

  return {
    isMatrizAcessoLoading,
    fetchMatrizAcesso,
    setPermissoesUsuarioContexto,
  };
};

export default useMatrizAcesso;
