import {
  FormikProps,
  getSessionItem,
  ICertificadoPrevidenciaResponse,
  IDadosPreenchidos,
  PrevidenciaContext,
  tryGetValueOrDefault,
  useCertificadosPrevidencia,
  useContext,
  useFormik,
  useMemo,
  VALIDA_CAMPOS_INITIAL_STATE,
  validarCamposAporte,
  Yup,
} from '@src/features/financeiro/aporte/exports';

type TUseValidarAporteFormik = {
  formik: FormikProps<IDadosPreenchidos>;
  certificado: ICertificadoPrevidenciaResponse | undefined;
};

export const useValidarAporteFormik = (): TUseValidarAporteFormik => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const cpfCnpj = String(getSessionItem('cpfCnpj'));

  const { response: certificadosPrevidenciaResponse } =
    useCertificadosPrevidencia();

  const certificado = useMemo(
    () =>
      certificadosPrevidenciaResponse.find(
        item => item.certificadoNumero === certificadoAtivo?.certificadoNumero,
      ),
    [certificadosPrevidenciaResponse, certificadoAtivo?.certificadoNumero],
  );

  const validarAporteSchema = Yup.object().shape({
    primeiroNome: Yup.string()
      .required('O primeiro nome é obrigatório')
      .min(2, 'O nome deve ter pelo menos 2 caracteres'),
    finalCpfCnpj: Yup.string()
      .required('O CPF/CNPJ é obrigatório')
      .matches(/^\d{11}$/, 'O CPF deve ter 4 dígitos'),
  });

  const formik = useFormik({
    initialValues: VALIDA_CAMPOS_INITIAL_STATE,
    validationSchema: validarAporteSchema,
    validate: valoresPreenchidos =>
      validarCamposAporte(valoresPreenchidos, {
        nome: tryGetValueOrDefault([certificado?.pessoa.pessoaFisica.nome], ''),
        cpfCnpj: tryGetValueOrDefault([cpfCnpj], ''),
      }),

    validateOnMount: true,
    validateOnBlur: true,
    validateOnChange: true,

    onSubmit: () => undefined,
  });

  return { formik, certificado };
};
