{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "airbnb", "airbnb/hooks", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2021, "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "react-hooks", "prettier", "custom-rules"], "rules": {"react/require-default-props": "off", "react/prop-types": "off", "@typescript-eslint/naming-convention": ["error", {"selector": "interface", "format": ["PascalCase"], "custom": {"regex": "^I[A-Z]", "match": true}}, {"selector": "typeAlias", "format": ["PascalCase"], "custom": {"regex": "^T[A-Z]", "match": true}}], "react/jsx-filename-extension": ["warn", {"extensions": [".tsx"]}], "no-use-before-define": "off", "@typescript-eslint/no-use-before-define": ["error"], "react/react-in-jsx-scope": "off", "import/extensions": "off", "import/no-unresolved": "off", "prettier/prettier": ["error", {"endOfLine": "auto"}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "import/prefer-default-export": "off", "react/function-component-definition": ["error", {"namedComponents": "arrow-function", "unnamedComponents": "arrow-function"}], "react/jsx-props-no-spreading": "warn", "no-restricted-exports": "off", "no-shadow": "off", "@typescript-eslint/no-shadow": ["error", {"builtinGlobals": false, "hoist": "all", "allow": ["FlagStatus"]}], "@typescript-eslint/explicit-module-boundary-types": "warn", "import/no-extraneous-dependencies": ["error", {"devDependencies": ["**/__tests__/**", "**/?(*.)+(spec|test).[jt]s?(x)", "**/setupTests.[jt]s?(x)", "test-utils", "**/*.test.tsx", "**/*.test.ts"]}]}, "overrides": [{"files": ["*.tsx"], "rules": {"max-lines": ["error", {"max": 100, "skipBlankLines": true, "skipComments": true}]}}], "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}, "node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}}}}