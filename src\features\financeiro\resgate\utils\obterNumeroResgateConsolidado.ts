import {
  getTernaryResult,
  tryGetValueOrDefault,
  ALIQUOTA,
  IMapearDadosSelecaoAliquotaFactoryRetorno,
} from '@src/features/financeiro/resgate/exports';

/**
 * Obtém o número de resgate consolidado de acordo com o tipo de alíquota selecionada
 *
 * Esta função seleciona o número de resgate correto (progressivo ou regressivo)
 * com base no tipo de alíquota escolhido pelo usuário.
 *
 * @param {IMapearDadosSelecaoAliquotaFactoryRetorno | undefined} dadosSelecaoAliquota - Dados da seleção de alíquota
 * @param {string | undefined} tipoAliquota - Tipo de alíquota selecionada (progressiva ou regressiva)
 * @returns {string} Número do resgate consolidado ou string vazia caso não exista
 */
export const obterNumeroResgateConsolidado = (
  dadosSelecaoAliquota: IMapearDadosSelecaoAliquotaFactoryRetorno | undefined,
  tipoAliquota: string | undefined,
): string => {
  const numeroResgateConsolidado = getTernaryResult(
    tipoAliquota === ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
    dadosSelecaoAliquota?.calculoAliquotaProgressiva?.numeroResgate,
    dadosSelecaoAliquota?.calculoAliquotaRegressiva?.numeroResgate,
  );

  return tryGetValueOrDefault([numeroResgateConsolidado], '');
};
