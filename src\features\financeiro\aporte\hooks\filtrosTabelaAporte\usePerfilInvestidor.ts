import {
  checkIfSomeItemsAreTrue,
  IFiltroTabelaAporte,
  PrevidenciaContext,
  TFormikProps,
  useAporteContext,
  useContext,
} from '@src/features/financeiro/aporte/exports';

type TUsePerfilInvestidor = { declaracaoOrigemRecurso: boolean };

export const usePerfilInvestidor = (
  formik: TFormikProps<IFiltroTabelaAporte>,
): TUsePerfilInvestidor => {
  const { origemRecurso } = useAporteContext();

  const { isClientePep } = useContext(PrevidenciaContext);

  const declaracaoOrigemRecurso =
    checkIfSomeItemsAreTrue([
      parseFloat(formik.values.valorContribuicao) >= 100_000,
      isClientePep === true,
    ]) && origemRecurso === '';
  return { declaracaoOrigemRecurso };
};
