import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IListarMotivosResgateResponse {
  codigo: string;
  descricao: string;
  selecionado?: boolean;
}

export interface IUseListarMotivosResgateReturn {
  listaMotivosResgate: IListarMotivosResgateResponse[];
  isLoadingListaMotivosResgate: boolean;
  listarMotivosResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<IListarMotivosResgateResponse[]> | undefined
  >;
}
