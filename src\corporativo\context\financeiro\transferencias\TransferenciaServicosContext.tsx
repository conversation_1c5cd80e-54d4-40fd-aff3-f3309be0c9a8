import { useContext, createContext } from 'react';
import type { TUseConfirmarTransferencia } from '@src/corporativo/infra/financeiro/transferenciaEntreFundos/useConfirmarTransferencia';
import type { TUseConsultaFundosTransferenciaDestino } from '@src/corporativo/infra/financeiro/transferenciaEntreFundos/useConsultaFundosDestino';
import type { TUseConsultaFundosPorCertificado } from '@src/corporativo/infra/financeiro/transferenciaEntreFundos/useConsultaFundosPorCertificado';
import type { TUseDefineReservaDestino } from '@src/corporativo/infra/financeiro/transferenciaEntreFundos/useDefineReservaDestino';
import type { TUseRevalidarTransferencia } from '@src/corporativo/infra/financeiro/transferenciaEntreFundos/useRevalidarTransferencia';

interface ITransferenciaServicosContextData {
  mensagemErro: string;
  carregando?: string;
}
interface ITransferenciaServicosContext
  extends ITransferenciaServicosContextData {
  consultaOrigem: TUseConsultaFundosPorCertificado;
  consultaDestino: TUseConsultaFundosTransferenciaDestino;
  defineReservaDestino: TUseDefineReservaDestino;
  revalidarTransferencia: TUseRevalidarTransferencia;
  confirmarTransferencia: TUseConfirmarTransferencia;
  setMensagemErro: (erro: string) => void;
}

export const TransferenciaServicosContext = createContext(
  {} as ITransferenciaServicosContext,
);

TransferenciaServicosContext.displayName = 'TransferenciaServicosContext';

export const useTransferenciaServicosContext = () => {
  return useContext(TransferenciaServicosContext);
};
