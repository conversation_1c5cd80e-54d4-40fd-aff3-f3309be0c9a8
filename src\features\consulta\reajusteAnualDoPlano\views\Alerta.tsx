import {
  CONSTANTES,
  Ds,
  Styles,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

type TAlertaProps = {
  alertaDetalhes?: boolean;
};

export const Alerta: React.FC<TAlertaProps> = ({ alertaDetalhes }) => {
  const theme = Ds.useTheme();

  return (
    <Styles.Alerta
      variant="information-01"
      icon={
        <Ds.IconInfoSharp
          size="big"
          color={theme.color.palette.informative['90']}
        />
      }
    >
      <Ds.SwitchCase
        fallback={
          <>
            <Ds.Text variant="text-standard-400">
              {CONSTANTES.TEXTOS.ALERTA.PERGUNTA}
            </Ds.Text>
            <Ds.Text variant="text-standard-400" marginTop="1rem">
              {CONSTANTES.TEXTOS.ALERTA.COMO_FUNCIONA} <br />
              {CONSTANTES.TEXTOS.ALERTA.EXPLICACAO}
            </Ds.Text>
          </>
        }
      >
        <Ds.Match when={alertaDetalhes}>
          <Ds.Text variant="text-standard-400">
            {CONSTANTES.TEXTOS.ALERTA_DETALHES.CASO_VARIACAO}
          </Ds.Text>
          <Ds.Text variant="text-standard-400" marginTop="1rem">
            {CONSTANTES.TEXTOS.ALERTA_DETALHES.INDICE_DESCRICAO}
          </Ds.Text>
        </Ds.Match>
      </Ds.SwitchCase>
    </Styles.Alerta>
  );
};
