import {
  Checkbox,
  EEtapasAporte,
  GridItem,
  IFundosAporte,
  InputValorDistribuicao,
  Match,
  PerfilDoRisco,
  SwitchCase,
  Text,
  formatarValorPadraoBrasileiro,
  porcentagem,
  tryGetValueOrDefault,
  useAporteContext,
} from '@src/features/financeiro/aporte/exports';

interface IDadosTabelaFundosAporteFactorie {
  fundo: IFundosAporte;
  handleFundoSelecionado: (
    checked: boolean | string,
    fundo: IFundosAporte,
  ) => void;
  handleDisableFundos: (fundo: IFundosAporte) => boolean;
  handleDistribuirValor: (value: string, fundoId: string) => void;
}

const TabelaFundosAporte = ({
  fundo,
  handleFundoSelecionado,
  handleDisableFundos,
  handleDistribuirValor,
}: IDadosTabelaFundosAporteFactorie) => {
  const { etapa, fundoSelecionado, formikValorDistribuido } =
    useAporteContext();

  return {
    selecionado: (
      <Checkbox
        key={fundo.fundoId + fundo.reservaId}
        checked={fundoSelecionado.some(item => item.fundoId === fundo.fundoId)}
        onChange={checked => handleFundoSelecionado(checked, fundo)}
        disabled={handleDisableFundos(fundo)}
      />
    ),
    descricaoFundo: (
      <Text variant="text-small-400">{fundo.descricaoFundo}</Text>
    ),

    descPerfilFundo: (
      <GridItem xs="1">
        <PerfilDoRisco perfil={fundo.descPerfilFundo} />
        <Text variant="text-small-400">
          <br />
        </Text>
      </GridItem>
    ),
    rentUlt12Meses: (
      <Text variant="text-small-400">
        {porcentagem.mask(fundo.rentUlt12Meses)}
      </Text>
    ),
    taxaAdministrativa: (
      <Text variant="text-small-400">
        {tryGetValueOrDefault([porcentagem.mask(fundo.taxaAdministração)], '-')}
      </Text>
    ),
    saldo: (
      <Text variant="text-small-400">
        {formatarValorPadraoBrasileiro(fundo.saldo)}
      </Text>
    ),
    valorContribuicao: (
      <SwitchCase>
        <Match when={etapa !== EEtapasAporte.ComprovanteAporte}>
          <InputValorDistribuicao
            key={fundo.fundoId}
            onChange={event => handleDistribuirValor(event, fundo.fundoId)}
            fundo={fundo}
            formik={formikValorDistribuido}
          />
        </Match>
        <Match when={etapa === EEtapasAporte.ComprovanteAporte}>
          <Text variant="text-small-400">
            {formatarValorPadraoBrasileiro(fundo.valorContribuicao)}
          </Text>
        </Match>
      </SwitchCase>
    ),
  };
};

export default TabelaFundosAporte;
