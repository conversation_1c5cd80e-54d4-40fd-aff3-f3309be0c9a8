import React from 'react';

import {
  GridItem,
  Text,
  Alert,
  IconCheckCircleRound,
  IconWarningSharp,
  IconInfoRound,
} from '@cvp/design-system-caixa';

interface IAlertaProps {
  mensagemAlerta: string;
  variant:
    | 'success-01'
    | 'danger-01'
    | 'information-02'
    | 'information-01'
    | 'success-02'
    | 'warning-02'
    | 'warning-01'
    | 'danger-02';
}

export const AlertaModal: React.FC<IAlertaProps> = ({
  mensagemAlerta,
  variant,
}) => {
  const listaAlerta = [
    {
      tipoAlerta: 'success-01',
      icone: <IconCheckCircleRound color="#127527" size="large" />,
      mensagem: mensagemAlerta,
    },
    {
      tipoAlerta: 'danger-01',
      icone: <IconInfoRound color="rgb(178, 44, 44)" size="large" />,
      mensagem: mensagemAlerta,
    },
    {
      tipoAlerta: 'warning-01',
      icone: <IconWarningSharp color="#CA9804" size="large" />,
      mensagem: mensagemAlerta,
    },
  ];

  const retornaAlertaPorTipo = (variantAlert: string) => {
    return listaAlerta.find(alerta => alerta.tipoAlerta === variantAlert);
  };

  const tipoAlerta = retornaAlertaPorTipo(variant);

  return (
    <GridItem xs="1" margin="0">
      <Alert color="#000" icon={tipoAlerta?.icone} variant={variant}>
        <Text variant="text-standard-400">{tipoAlerta?.mensagem}</Text>
      </Alert>
    </GridItem>
  );
};
