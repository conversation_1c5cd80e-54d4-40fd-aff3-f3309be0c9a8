import { novaDataFormatada, Text, S, TSinistroInfoProps } from '../exports';

export const InfoSinistro: React.FC<TSinistroInfoProps> = ({
  codigoCertificado,
  dataSinistro,
  statusAvisoSinitro,
}) => {
  return (
    <S.SinistroInfoStyled>
      <S.SinistroInfoColumnStyled>
        <S.SinistroInfoDataStyled>
          <Text variant="text-standard-600">Certificado</Text>
          <Text variant="text-standard-400">{codigoCertificado}</Text>
        </S.SinistroInfoDataStyled>
        <S.SinistroInfoDataStyled>
          <Text variant="text-standard-600">Data do sinistro</Text>
          <Text variant="text-standard-400">
            {novaDataFormatada(dataSinistro)}
          </Text>
        </S.SinistroInfoDataStyled>
      </S.SinistroInfoColumnStyled>
      <S.SinistroInfoColumnStyled>
        <S.SinistroInfoDataStyled>
          <Text variant="text-standard-600">Status</Text>
          <Text variant="text-standard-400">{statusAvisoSinitro}</Text>
        </S.SinistroInfoDataStyled>
      </S.SinistroInfoColumnStyled>
    </S.SinistroInfoStyled>
  );
};
