import { FieldInputProps } from 'formik';
import { InputHTMLAttributes } from 'react';

export type TCelulasEditaveis = Array<string>;

export type TRowData = {
  id: number;
  index: number;
  nome: string;
  data: string;
  parentesco: string;
  percentual: string;
  beneficiarioId: string;
  estado: 'novo' | 'existente';
};

export type TTipoCelula = 'texto' | 'select' | 'data' | 'porcentagem';

export type TCelulaTabelaProps = {
  tipo: TTipoCelula;
  id: string;
  coberturaId: string;
  field: FieldInputProps<string>;
  estado: string;
  error?: boolean;
} & InputHTMLAttributes<HTMLInputElement>;

export type TCelulaTabelaBeneficiario = {
  name: string;
  tipo: TTipoCelula;
  error?: boolean;
  estado: string;
  index: number;
  id: string;
  coberturaId: string;
} & InputHTMLAttributes<HTMLInputElement>;
