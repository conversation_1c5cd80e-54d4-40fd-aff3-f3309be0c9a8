import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface ICriarMotivoResgatePayload {
  codigoDoMotivo: string;
  numeroDoResgate: string;
}

export interface ICriarMotivoResgateResponse {
  codigoEmpresa: string;
  codigoMotivo: string;
  numeroDoResgate: string;
}

export interface IUseCriarMotivoResgateReturn {
  dadosCriacaoMotivoResgate: ICriarMotivoResgateResponse;
  isLoadingCriacaoMotivoResgate: boolean;
  criarMotivoResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<ICriarMotivoResgateResponse> | undefined>;
}
