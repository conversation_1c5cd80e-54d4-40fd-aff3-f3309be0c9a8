import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { TCriarPayloadDadosPagamentoFactoryReturn } from '@src/corporativo/types/financeiro/resgate/ICriarPayloadDadosPagamentoFactory';

export interface ISalvarDadosPagamentoPayload {
  codigoBanco: string;
  codigoEmpresa: string;
  dataProgramada: string;
  digitoAgencia: string;
  digitoConta: string;
  numeroAgencia: string;
  numeroConta: string;
  numeroResgate: number;
  tipoConta: string;
  tipoPagamento: string;
}

export interface ISalvarDadosPagamentoResponse {
  codigoEmpresa: string;
  numeroResgate: number;
  codigoBanco: string;
  tipoPagamento: string;
  numeroAgencia: string;
  digitoAgencia: string;
  numeroConta: string;
  digitoConta: string;
  tipoConta: string;
  status: string;
  dataPagamento: string;
  dataProgramada: string;
  codigoCanal: string;
}

export interface IUseSalvarDadosPagamentoReturn {
  dadosSalvosPagamento: ISalvarDadosPagamentoResponse;
  isLoadingDadosPagamento: boolean;
  salvarDadosPagamento: (
    dynamicPayload: TCriarPayloadDadosPagamentoFactoryReturn,
  ) => Promise<IHandleReponseResult<ISalvarDadosPagamentoResponse> | undefined>;
}
