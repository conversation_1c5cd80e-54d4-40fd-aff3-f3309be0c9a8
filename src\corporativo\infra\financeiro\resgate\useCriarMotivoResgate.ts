import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  ICriarMotivoResgatePayload,
  ICriarMotivoResgateResponse,
  IUseCriarMotivoResgateReturn,
} from '@src/corporativo/types/financeiro/resgate/ICriarMotivoResgate';
import { tryGetValueOrDefault } from '@cvp/utils';

export const useCriarMotivoResgate = (): IUseCriarMotivoResgateReturn => {
  const {
    response: dadosCriacaoMotivoResgate,
    loading: isLoadingCriacaoMotivoResgate,
    invocarApiGatewayCvpComToken: criarMotivoResgate,
  } = useApiGatewayCvpInvoker<
    Partial<ICriarMotivoResgatePayload>,
    ICriarMotivoResgateResponse
  >(PECOS.CriarMotivoResgate, {});

  return {
    dadosCriacaoMotivoResgate: tryGetValueOrDefault(
      [dadosCriacaoMotivoResgate?.entidade],
      {} as ICriarMotivoResgateResponse,
    ),
    isLoadingCriacaoMotivoResgate,
    criarMotivoResgate,
  };
};
