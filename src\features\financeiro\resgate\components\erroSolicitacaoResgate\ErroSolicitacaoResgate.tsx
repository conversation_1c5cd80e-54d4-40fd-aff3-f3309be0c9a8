import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ErroSolicitacaoResgate = ({
  mensagens,
}: Resgate.IErroSolicitacaoResgateProps): React.ReactElement => {
  const { isErroExistente, mensagemExibida } =
    Resgate.useErroSolicitacaoResgate(mensagens);

  return (
    <Resgate.ConditionalRenderer condition={isErroExistente}>
      <Resgate.GridItem xs="1">
        <Resgate.Alerta tipo="erro">
          <Resgate.Text variant="heading-standard-600">Erro</Resgate.Text>
          <div>
            <Resgate.Text variant="text-standard-400">
              {Resgate.tryGetValueOrDefault([mensagemExibida], '')}
            </Resgate.Text>
          </div>
        </Resgate.Alerta>
      </Resgate.GridItem>
    </Resgate.ConditionalRenderer>
  );
};
