export interface IAlerta {
  message: React.ReactNode;
  icon?: React.ReactNode;
  variant:
    | 'information-02'
    | 'information-01'
    | 'success-02'
    | 'success-01'
    | 'warning-02'
    | 'warning-01'
    | 'danger-02'
    | 'danger-01';
}

export interface IAlertaTimeoutProps {
  alerta: {
    message: React.ReactNode;
    icon?: React.ReactNode;
    variant:
      | 'information-02'
      | 'information-01'
      | 'success-02'
      | 'success-01'
      | 'warning-02'
      | 'warning-01'
      | 'danger-02'
      | 'danger-01';
  };
  callbackTimeout: () => void;
  duration?: number;
  withoutTimeout?: boolean;
}
