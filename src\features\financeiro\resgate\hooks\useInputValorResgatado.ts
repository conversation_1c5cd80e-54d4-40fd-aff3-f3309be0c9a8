import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useInputValorResgatado = ({
  fundo,
  onChange,
}: Resgate.IUseInputValorResgatado): Resgate.IUseInputValorResgatadoReturn => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const [interacoes, setInteracoes] = Resgate.React.useState<
    Record<string, boolean>
  >({});

  const caminhoFundoResgateValor = `fundosParaResgate.${fundo.codigoFundo}.valorRetirar`;

  const { hasError, message } = Resgate.obterErroFormikInput(
    formik.errors,
    formik.touched,
    caminhoFundoResgateValor,
  );

  const isError: boolean = Resgate.checkIfAllItemsAreTrue([
    hasError,
    interacoes[fundo.codigoFundo],
  ]);

  const colorByErrorState = Resgate.getTernaryResult(
    isError,
    Resgate.VARIANTES_FONTES_TEXT.CONTENT_DANGER_01,
    Resgate.VARIANTES_FONTES_TEXT.CONTENT_NEUTRAL_03,
  );

  const marcarInteragido = (codigoFundo: string): void => {
    setInteracoes(prev => ({
      ...prev,
      [codigoFundo]: true,
    }));
  };

  const handleChangeInput = (
    event: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    marcarInteragido(fundo.codigoFundo);

    const valorRetirar = (
      Number(Resgate.valoresMonetarios.unmask(event.target.value)) / 100
    ).toFixed(2);

    onChange(fundo.codigoFundo, valorRetirar);
  };

  return {
    caminhoFundoResgateValor,
    handleChangeInput,
    isError,
    message,
    colorByErrorState,
  };
};
