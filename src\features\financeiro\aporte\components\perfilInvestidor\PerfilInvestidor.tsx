import {
  BOTOES,
  FILTRO_APORTE,
  GridItem,
  IFiltroTabelaAporte,
  PERFIS,
  Select,
  TFormikProps,
  Text,
  checkIfSomeItemsAreTrue,
  usePerfilInvestidor,
} from '@src/features/financeiro/aporte/exports';

interface IPerfilInvestidor {
  formik: TFormikProps<IFiltroTabelaAporte>;
  perfilInvestidor: string[];
}

const PerfilInvestidor: React.FC<IPerfilInvestidor> = ({
  formik,
  perfilInvestidor,
}) => {
  const { declaracaoOrigemRecurso } = usePerfilInvestidor(formik);

  return (
    <GridItem xs="1 / 4">
      <Text variant="text-standard-600" fontColor="content-neutral-05">
        {FILTRO_APORTE.selectPerfilFundoInvestimento}
      </Text>
      <Select
        id="perfilInvestidor"
        clearable
        errorProps={{
          children: '',
          show: false,
        }}
        multiple
        onChange={selectedOptions => {
          formik.setFieldValue(
            'perfilInvestidor',
            selectedOptions.map(option => option.value),
          );
        }}
        options={[
          {
            text: PERFIS.conservador,
            value: 'conservador',
          },
          {
            text: PERFIS.moderado,
            value: 'moderado',
          },
          {
            text: PERFIS.arrojado,
            value: 'arrojado',
          },
          {
            text: PERFIS.agressivo,
            value: 'agressivo',
          },
        ]}
        placeholder={BOTOES.select}
        size="standard"
        sizeWidth="standard"
        variant="box-classic"
        textVariant="text-small-400"
        selectedValues={perfilInvestidor}
        disabled={checkIfSomeItemsAreTrue([
          !!formik.errors.dataDebito,
          !!formik.errors.valorContribuicao,
          declaracaoOrigemRecurso,
        ])}
      />
    </GridItem>
  );
};

export default PerfilInvestidor;
