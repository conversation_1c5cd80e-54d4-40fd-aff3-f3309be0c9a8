import { useContext } from 'react';
import { TAporteContext } from '@src/corporativo/types/aporte/TAporteContext';
import AporteContext from '@src/corporativo/context/financeiro/aporte/AporteContext';

export const useAporteContext = (): TAporteContext => {
  const context = useContext(AporteContext);
  if (!context) {
    throw new Error('useAporteContext deve ser usado dentro do AporteProvider');
  }
  return context;
};
