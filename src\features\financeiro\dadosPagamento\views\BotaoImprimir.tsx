import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const BotaoImprimir: React.FC<{
  cobrancaSelecionada?: string;
}> = ({ cobrancaSelecionada }) => {
  const { certificadoAtivo, setImpressao, setParametrosScroll } =
    DadosPagamento.useContext(DadosPagamento.PrevidenciaContext);
  const navigate = DadosPagamento.useNavigate();

  const { baixando, obterBoleto } =
    DadosPagamento.useObterBoleto(cobrancaSelecionada);

  const redirectRota = () => {
    navigate('/seguridade/previdencia/pos-venda/imprimir');
  };

  const gerarBoletos = async () => {
    if (cobrancaSelecionada) {
      const arquivo = await obterBoleto(cobrancaSelecionada);
      const rota = window.location.pathname;
      const valorScroll = window.scrollY;

      const tipoEmail =
        DadosPagamento.tipoEmailConstants
          .SEGUNDA_VIA_BOLETO_CONTRIBUICOES_PAGAS;

      const parametrosEnvio = {
        cpfCnpj: String(DadosPagamento.getSessionItem('cpfCnpj')),
        numeroCertificado: certificadoAtivo?.certificadoNumero,
        numeroCobranca: cobrancaSelecionada,
      };

      setImpressao({
        tipoDocumento: DadosPagamento.TEXTOS.TIPO_DOCUMENTO,
        base64: arquivo,
        tipoEmail,
        parametrosEnvio,
      });

      setParametrosScroll({
        rota,
        valorScroll,
      });

      redirectRota();
    }
  };

  return (
    <DadosPagamento.FlexBox $justifyContent="flex-end">
      <DadosPagamento.ButtonGerarBoleto
        variant="secondary-outlined"
        leftIcon={DadosPagamento.getTernaryResult(
          baixando,
          <DadosPagamento.LoadingSpinner size="small" />,
          <DadosPagamento.PDFIcon size="big" />,
        )}
        disabled={DadosPagamento.checkIfSomeItemsAreTrue([
          !cobrancaSelecionada,
          baixando,
        ])}
        onClick={gerarBoletos}
      >
        {DadosPagamento.TEXTOS.GERAR_BOLETO}
      </DadosPagamento.ButtonGerarBoleto>
    </DadosPagamento.FlexBox>
  );
};

export default BotaoImprimir;
