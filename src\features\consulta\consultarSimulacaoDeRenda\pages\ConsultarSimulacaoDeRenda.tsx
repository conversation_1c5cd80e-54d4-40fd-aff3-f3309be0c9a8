import * as SimulacaoExports from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

const ConsultarSimulacaoDeRenda: React.FC = () => {
  const {
    dataUltimasSimulacoes = [],
    handleVerUltimasSimulacoes,
    loading: loadingUltimasSimulacoes,
    handleClearUltimasSolicitacoes,
  } = SimulacaoExports.useVerUltimasSimulacoesDeRenda();

  const useConsultarRendaHook = SimulacaoExports.useConsultarRenda({
    dataUltimasSimulacoes,
    loadingUltimasSimulacoes,
  });

  return (
    <div>
      <SimulacaoExports.SelectTypeRenda
        handleSetIncomeRenda={useConsultarRendaHook.handleSetIncomeRenda}
        handleClearValuesData={useConsultarRendaHook.handleClearValuesData}
        handleClearUltimasSolicitacoes={handleClearUltimasSolicitacoes}
      />
      <SimulacaoExports.AlertsTable
        data={useConsultarRendaHook.data}
        income={useConsultarRendaHook.income}
      />
      <SimulacaoExports.Match
        when={useConsultarRendaHook.showSelectRendaReversivel}
      >
        <SimulacaoExports.RendaVitaliciaReversivelBeneficiario
          consultarRenda={useConsultarRendaHook.consultarRenda}
          getOptionsSelectSexoBeneficiario={
            useConsultarRendaHook.getOptionsSelectSexoBeneficiario
          }
          getOptionsSelectPorcentagemRenda={
            useConsultarRendaHook.getOptionsSelectPorcentagemRenda
          }
        />
      </SimulacaoExports.Match>

      <SimulacaoExports.Match
        when={useConsultarRendaHook.showSelectTemporariaPrazoCertoPrazoMinimo}
      >
        <SimulacaoExports.SimulacaoDeRendaTemporariaPrazoCerto
          consultarRenda={useConsultarRendaHook.consultarRenda}
          income={useConsultarRendaHook.income}
        />
      </SimulacaoExports.Match>

      <SimulacaoExports.Match when={useConsultarRendaHook.showTableConsulta}>
        <SimulacaoExports.TableDataConsulta data={useConsultarRendaHook.data} />
      </SimulacaoExports.Match>

      <SimulacaoExports.Match
        when={useConsultarRendaHook.showTableUltimasSimulacoes}
      >
        <SimulacaoExports.TableDataUltimasSolicitacoes
          data={dataUltimasSimulacoes}
        />
      </SimulacaoExports.Match>

      <SimulacaoExports.LoadingTable
        deveExibirLoading={useConsultarRendaHook.deveExibirLoading}
      />

      <SimulacaoExports.Match when={useConsultarRendaHook.showFooterTable}>
        <SimulacaoExports.FooterSimulacaoDeRenda
          typeRenda={useConsultarRendaHook.income.renda}
          handleClearValuesData={useConsultarRendaHook.handleClearValuesData}
          handleClearUltimasSolicitacoes={handleClearUltimasSolicitacoes}
          handleVerUltimasSimulacoes={handleVerUltimasSimulacoes}
          dataUltimasSimulacoes={dataUltimasSimulacoes}
          data={useConsultarRendaHook.data}
        />
      </SimulacaoExports.Match>
      <SimulacaoExports.ModalCertificadoNaoElegivel
        open={useConsultarRendaHook.openModalCertificado}
        tipoDeRegime={useConsultarRendaHook.tipoRegime}
        setOpenModalCertificado={useConsultarRendaHook.setOpenModalCertificado}
      />
    </div>
  );
};
export default ConsultarSimulacaoDeRenda;
