import { IFormatarDescricaoConta } from '@src/features/financeiro/resgate/exports';

/**
 * Formata a descrição de uma conta bancária para exibição na interface
 *
 * Esta função concatena os dados da conta bancária em um formato padronizado:
 * "agência-conta - tipo da conta - descrição da conta"
 *
 * @param {Object} params - Parâmetros para formatação da descrição da conta
 * @param {string} params.numeroAgencia - Número da agência bancária
 * @param {string} params.codigoContaBancaria - Número da conta bancária
 * @param {string} params.descricaoTipoContaBancaria - Descrição do tipo da conta (corrente, poupança, etc.)
 * @param {string} params.descritivoContaBancaria - Descrição adicional da conta
 * @returns {string} Descrição formatada da conta bancária
 */
export const formatarDescricaoConta = ({
  numeroAgencia,
  codigoContaBancaria,
  descricaoTipoContaBancaria,
  descritivoContaBancaria,
}: IFormatarDescricaoConta): string => {
  return `${numeroAgencia}-${codigoContaBancaria} - ${descricaoTipoContaBancaria} - ${descritivoContaBancaria}`;
};
