import { ModalImprimirDocumentosContext } from '@src/corporativo/context/ModalImprimirDocumentosContext';
import {
  EModalImprDocActionType,
  IModalImprDocState,
  modalImprDocInitialState,
  resetarValoresAcimaPrimeiroSelect,
  resetarValoresAcimaSegundoSelect,
  TModalImprDocActionType,
} from '@src/features/layoutPrevidencia/exports';
import { useMemo, useReducer } from 'react';

const ModalImprimirDocumentosProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const modalImprDocReducer = (
    state: IModalImprDocState,
    action: TModalImprDocActionType,
  ): IModalImprDocState => {
    switch (action.type) {
      case EModalImprDocActionType.ABRIR_MODAL:
        return { ...state, isModalImprDocOpen: true };
      case EModalImprDocActionType.FECHAR_MODAL:
        return modalImprDocInitialState;
      case EModalImprDocActionType.SET_OPTIONS_SEGUNDA_OPCAO:
        return {
          ...state,
          ...resetarValoresAcimaSegundoSelect,
          optionsSegundaOpcao: action.opcoesSegundoSelect,
        };
      case EModalImprDocActionType.SET_OPTIONS_TERCEIRA_OPCAO:
        return { ...state, optionsTerceiraOpcao: action.opcoesTerceiroSelect };
      case EModalImprDocActionType.SET_SELECT_PRIMEIRA_OPCAO:
        return {
          ...state,
          ...resetarValoresAcimaPrimeiroSelect,
          selectPrimeiraOpcao: action.selectPrimeiraOpcao,
        };
      case EModalImprDocActionType.SET_SELECT_SEGUNDA_OPCAO:
        return {
          ...state,
          ...resetarValoresAcimaSegundoSelect,
          selectSegundaOpcao: action.selectSegundaOpcao,
        };
      case EModalImprDocActionType.SET_SELECT_TERCEIRA_OPCAO:
        return {
          ...state,
          selectTerceiraOpcao: action.selectTerceiraOpcao,
        };
      case EModalImprDocActionType.SET_SELECT_DATA_INICIO:
        return {
          ...state,
          selectDataInicio: action.selectDataInicio,
        };
      case EModalImprDocActionType.SET_SELECT_DATA_FIM:
        return {
          ...state,
          selectDataFim: action.selectDataFim,
        };
      case EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_INICIO:
        return {
          ...state,
          mensagemErroSelectDataInicio: action.mensagemErroDataInicio,
        };
      case EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_FIM:
        return {
          ...state,
          mensagemErroSelectDataFim: action.mensagemErroDataFim,
        };
      case EModalImprDocActionType.SET_MENSAGEM_ERRO_OBTER_PDF:
        return {
          ...state,
          mensagemErroObterPdf: action.mensagemErroObterPdf,
        };
      default:
        throw new Error('Tipo de ação inválida');
    }
  };

  const [modalImprDocState, modalImprDocDispatch] = useReducer(
    modalImprDocReducer,
    modalImprDocInitialState,
  );

  const modalImprDocContextValue = useMemo(
    () => ({
      modalImprDocDispatch,
      modalImprDocState,
    }),
    [modalImprDocState],
  );
  return (
    <ModalImprimirDocumentosContext.Provider value={modalImprDocContextValue}>
      {children}
    </ModalImprimirDocumentosContext.Provider>
  );
};

export default ModalImprimirDocumentosProvider;
