import { FormatarDataHora } from '@src/features/consultaSinistro/exports';

/**
 * Formata uma data no formato "DD-MM-YYYY" para um formato desejado,
 * utilizando a função FormatarDataHora.
 *
 * @param {string} data - A data a ser formatada no formato "DD-MM-YYYY".
 * @returns {string} A data formatada no formato desejado ou a data original formatada se houver erro.
 */
export const novaDataFormatada = (data: string): string => {
  // Valida o formato da data
  const regex = /^\d{2}-\d{2}-\d{4}$/;
  if (!regex.test(data)) {
    return FormatarDataHora(data);
  }

  // Inverte a data para o formato "YYYY-MM-DD"
  const dataFormatada = data.split('-').reverse().join('-');

  try {
    return FormatarDataHora(new Date(dataFormatada).toISOString());
  } catch (error) {
    return FormatarDataHora(data);
  }
};
