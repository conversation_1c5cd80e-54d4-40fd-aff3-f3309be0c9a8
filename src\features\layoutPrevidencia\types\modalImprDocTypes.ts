import { ChangeEventHand<PERSON>, Dispatch } from 'react';
import { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
import { NavigateFunction } from 'react-router-dom';
import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { IPECOObterBeneficiariosEntity } from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchObterBeneficiarios';
import { TImpressao } from '@src/corporativo/types/impressaoDocumentosPrevidencia/TImpressao';

export enum EModalImprDocActionType {
  ABRIR_MODAL,
  FECHAR_MODAL,
  SET_OPTIONS_SEGUNDA_OPCAO,
  SET_OPTIONS_TERCEIRA_OPCAO,
  SET_SELECT_PRIMEIRA_OPCAO,
  SET_SELECT_SEGUNDA_OPCAO,
  SET_SELECT_TERCEIRA_OPCAO,
  SET_SELECT_DATA_INICIO,
  SET_SELECT_DATA_FIM,
  SET_MENSAGEM_ERRO_OBTER_PDF,
  SET_MENSAGEM_ERRO_DATA_INICIO,
  SET_MENSAGEM_ERRO_DATA_FIM,
  SET_PDF_TITULO_E_URL,
}

export type TModalImprDocActionType =
  | { type: EModalImprDocActionType.ABRIR_MODAL }
  | { type: EModalImprDocActionType.FECHAR_MODAL }
  | {
      type: EModalImprDocActionType.SET_OPTIONS_SEGUNDA_OPCAO;
      opcoesSegundoSelect: SelectItem[];
    }
  | {
      type: EModalImprDocActionType.SET_OPTIONS_TERCEIRA_OPCAO;
      opcoesTerceiroSelect: SelectItem[];
    }
  | {
      type: EModalImprDocActionType.SET_SELECT_PRIMEIRA_OPCAO;
      selectPrimeiraOpcao: SelectItem;
    }
  | {
      type: EModalImprDocActionType.SET_SELECT_SEGUNDA_OPCAO;
      selectSegundaOpcao: SelectItem;
    }
  | {
      type: EModalImprDocActionType.SET_SELECT_TERCEIRA_OPCAO;
      selectTerceiraOpcao: SelectItem;
    }
  | {
      type: EModalImprDocActionType.SET_SELECT_DATA_INICIO;
      selectDataInicio: SelectItem;
    }
  | {
      type: EModalImprDocActionType.SET_SELECT_DATA_FIM;
      selectDataFim: SelectItem;
    }
  | {
      type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_INICIO;
      mensagemErroDataInicio: string;
    }
  | {
      type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_FIM;
      mensagemErroDataFim: string;
    }
  | {
      type: EModalImprDocActionType.SET_MENSAGEM_ERRO_OBTER_PDF;
      mensagemErroObterPdf: string;
    };

export interface IModalImprDocState {
  isModalImprDocOpen: boolean;
  optionsPrimeiraOpcao: SelectItem[];
  optionsSegundaOpcao: SelectItem[];
  optionsTerceiraOpcao: SelectItem[];
  selectPrimeiraOpcao: SelectItem;
  selectSegundaOpcao: SelectItem;
  selectTerceiraOpcao: SelectItem;
  selectDataInicio: SelectItem;
  selectDataFim: SelectItem;
  mensagemErroSelectDataInicio: string;
  mensagemErroSelectDataFim: string;
  mensagemErroObterPdf: string;
  impressao?: TImpressao;
}

export interface IModalImprDocContext {
  modalImprDocDispatch: Dispatch<TModalImprDocActionType>;
  modalImprDocState: IModalImprDocState;
}

export interface IModalImprDocProps {
  modalImprDocState: IModalImprDocState;
  modalImprDocDispatch: React.Dispatch<TModalImprDocActionType>;
  navigate: NavigateFunction;
}

export interface IUseModalImprDocReturn {
  onGenerateDoc: (navigate: NavigateFunction) => Promise<void>;
  isRequestLoading: boolean;
  showSecondSelect: boolean;
  disableButton: boolean;
  showThirdSelect: boolean;
  getModalImprDocMessage: () => string;
  getPdfDocumentoSelecionado: () => Promise<string | null>;
  thirdSelectType: 'select' | 'period';
  onChangeSelectPrimary: (selectedOption: SelectItem[]) => void;
  onChangeSelectSecondary: (selectedOption: SelectItem[]) => void;
  onChangeSelectThird: (selectedOption: SelectItem[]) => void;
  onChangeDateFieldDataInicio: ChangeEventHandler<HTMLInputElement>;
  onChangeDateFieldDataFim: ChangeEventHandler<HTMLInputElement>;
}

export type TGetPdfDocumentoSelecionadoResponse =
  | IHandleReponseResult<string>
  | IHandleReponseResult<{ return: string }>
  | IHandleReponseResult<{ informe: string }>
  | IHandleReponseResult<{ blob: Blob }>
  | IHandleReponseResult<IPECOObterBeneficiariosEntity[]>
  | string
  | undefined;
