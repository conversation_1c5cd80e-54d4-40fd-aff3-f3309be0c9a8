import { IAlerta } from '../alerta/IAlerta';

export type TUseModalNovoBeneficiario = (params: {
  coberturaId: string;
  beneficiarioId?: string;
  adicionarBeneficiario: (cpf: string, sexo: string) => void;
}) => {
  alertaModal: IAlerta | null;
  setAlertaModal: React.Dispatch<React.SetStateAction<IAlerta | null>>;
  handleSubmit: (e: React.FormEvent) => void;
  numCpf: string;
  setNumCpf: React.Dispatch<React.SetStateAction<string>>;
  sexo: string;
  setSexo: React.Dispatch<React.SetStateAction<string>>;
};
