import '@testing-library/jest-dom';
import {
  anoAtual,
  dia<PERSON>tual,
  getPECOErrorMessageOrDefault,
  indexMesAtual,
  isValidDateRange,
  mesAtual,
  parseAAAAMMDDStringToDateISOString,
  parseDateISOStringToAAAAMMDDString,
  retornaArrayUltimos5Anos,
  retornaDateDiaAtual,
  retornaDateISOStringPeriodoUltimosDias,
  retornaDatePrimeiroDiaDoMes,
  retornaDateUltimoDiaDoMes,
  retornaPeriodoDateISOString,
  retornaPrimeiroDiaDoAno,
  retornaUltimoDiaDoAno,
  selecionarChavesObj,
} from '@src/features/layoutPrevidencia/utils';
import {
  ERRO_MENSAGEM_DATA_FUTURA,
  ERRO_MENSAGEM_DATA_INICIAL_MAIOR_QUE_DATA_FINAL,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF,
} from '@src/features/layoutPrevidencia/exports';

jest.mock('@src/corporativo/infra/config/api/axiosConfig', () => ({
  API_BASE_URL: 'http://mocked.api.url',
}));

describe('Modal Imprimir Documentos utils', () => {
  test('Deve retornar o primeiro dia do ano de 2024', () => {
    const primeiroDiaDoAno = retornaPrimeiroDiaDoAno(2024);
    expect(primeiroDiaDoAno).toStrictEqual(
      new Date('2024-01-01T03:00:00.000Z'),
    );
  });

  test('Deve retornar o ultimo dia do ano de 2024', () => {
    const ultimoDiaDoAno = retornaUltimoDiaDoAno(2024);
    expect(ultimoDiaDoAno).toStrictEqual(new Date('2024-12-31T03:00:00.000Z'));
  });

  test('Deve retornar o date do ultimo dia do mes de janeiro no ano atual', () => {
    const ultimoDiaDeJaneiroDoAnoAtual = retornaDateUltimoDiaDoMes(0);
    expect(ultimoDiaDeJaneiroDoAnoAtual).toStrictEqual(
      new Date(`${anoAtual}-01-31T03:00:00.000Z`),
    );
  });

  test('Deve retornar o date do primeiro dia do mes de janeiro no ano atual', () => {
    const ultimoDiaDeJaneiroDoAnoAtual = retornaDatePrimeiroDiaDoMes(0);
    expect(ultimoDiaDeJaneiroDoAnoAtual).toStrictEqual(
      new Date(`${anoAtual}-01-01T03:00:00.000Z`),
    );
  });

  test('Deve retornar o date atual', () => {
    const dateHoje = retornaDateDiaAtual();
    expect(dateHoje).toStrictEqual(new Date(dateHoje));
  });

  test('Deve retornar o período do primeiro ao último dia do mes mockado no ano atual', () => {
    const retornaPeriodoJaneiro = retornaPeriodoDateISOString('0');

    if (indexMesAtual === 0) {
      return expect(retornaPeriodoJaneiro).toStrictEqual({
        dataInicio: `${anoAtual}-01-01T03:00:00.000Z`,
        dataFim: retornaPeriodoJaneiro.dataFim,
      });
    }

    return expect(retornaPeriodoJaneiro).toStrictEqual({
      dataInicio: `${anoAtual}-01-01T03:00:00.000Z`,
      dataFim: `${anoAtual}-01-31T03:00:00.000Z`,
    });
  });

  test('Deve retornar o período do primeiro ao último dia do ano de 2023', () => {
    const retornaPeriodoJaneiro = retornaPeriodoDateISOString('2023');
    expect(retornaPeriodoJaneiro).toStrictEqual({
      dataInicio: '2023-01-01T03:00:00.000Z',
      dataFim: '2023-12-31T03:00:00.000Z',
    });
  });

  test('Deve retornar um array com os últimos 5 anos', () => {
    const arrayUltimos5Anos = retornaArrayUltimos5Anos();
    expect(arrayUltimos5Anos).toStrictEqual([
      String(anoAtual - 1),
      String(anoAtual - 2),
      String(anoAtual - 3),
      String(anoAtual - 4),
      String(anoAtual - 5),
    ]);
  });

  test('Deve retornar uma ISO String ao receber o value mockado no padrao do componente DateField', () => {
    const dataFormatadaISOString =
      parseAAAAMMDDStringToDateISOString('2024-01-31');
    expect(dataFormatadaISOString).toBe('2024-01-31T03:00:00.000Z');
  });

  test('Deve converter a data em ISO string para o padrao do value do componente DateField', () => {
    const dateFieldValue = parseDateISOStringToAAAAMMDDString(
      '2024-01-31T03:00:00.000Z',
    );
    expect(dateFieldValue).toBe('2024-01-31');
  });

  test('Deve retornar falso e mensagem de erro no callback para data inicial maior que a data atual', () => {
    let errorMessage = '';
    const periodoValido = isValidDateRange(
      `${anoAtual}-${mesAtual}-${diaAtual + 1}`,
      '2024-01-31',
      err => {
        if (err) errorMessage = err;
      },
    );
    expect(periodoValido).toBe(false);
    expect(errorMessage).toBe(ERRO_MENSAGEM_DATA_FUTURA);
  });

  test('Deve retornar falso e mensagem de erro no callback para data final maior que a data atual', () => {
    let errorMessage = '';
    const periodoValido = isValidDateRange(
      '2024-01-31',
      `${anoAtual}-${mesAtual}-${diaAtual + 1}`,
      err => {
        if (err) errorMessage = err;
      },
    );
    expect(periodoValido).toBe(false);
    expect(errorMessage).toBe(ERRO_MENSAGEM_DATA_FUTURA);
  });

  test('Deve retornar falso e mensagem de erro no callback para ambas datas maiores que a data atual', () => {
    let errorMessage = '';
    const periodoValido = isValidDateRange(
      '2024-01-31',
      `${anoAtual}-${mesAtual}-${diaAtual + 1}`,
      err => {
        if (err) errorMessage = err;
      },
    );
    expect(periodoValido).toBe(false);
    expect(errorMessage).toBe(ERRO_MENSAGEM_DATA_FUTURA);
  });

  test('Deve retornar true caso só possua a data inicial', () => {
    let errorMessage = '';
    const periodoValido = isValidDateRange('2024-01-31', '', err => {
      if (err) errorMessage = err;
    });
    expect(periodoValido).toBe(true);
    expect(errorMessage).toBe('');
  });

  test('Deve retornar true caso só possua a data final', () => {
    let errorMessage = '';
    const periodoValido = isValidDateRange('', '2024-01-31', err => {
      if (err) errorMessage = err;
    });
    expect(periodoValido).toBe(true);
    expect(errorMessage).toBe('');
  });

  test('Deve retornar false e mensagem de erro no callback de data inicial maior que a data final', () => {
    let errorMessage = '';
    const periodoValido = isValidDateRange('2024-01-31', '2024-01-30', err => {
      if (err) errorMessage = err;
    });

    expect(periodoValido).toBe(false);
    expect(errorMessage).toBe(ERRO_MENSAGEM_DATA_INICIAL_MAIOR_QUE_DATA_FINAL);
  });

  test('Deve retornar apenas as chaves e valores escolhidas de um objeto', () => {
    const objetoTeste = {
      nome: 'Diego',
      sobrenome: 'Moura',
      gameFavorito: 'Elden Ring',
    };

    const objetoTesteParcial = selecionarChavesObj(objetoTeste, [
      'nome',
      'sobrenome',
    ]);
    expect(objetoTesteParcial).toStrictEqual({
      nome: 'Diego',
      sobrenome: 'Moura',
    });
  });

  test('Deve retornar mensagem de erro default caso o backend não retorne', () => {
    const mensagemDeErro1 = getPECOErrorMessageOrDefault('');
    const mensagemDeErro2 = getPECOErrorMessageOrDefault({
      sucessoGI: true,
      sucessoBFF: false,
      mensagens: [{ descricao: '' }],
    });
    expect(mensagemDeErro1).toBe(
      MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF,
    );
    expect(mensagemDeErro2).toBe(
      MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF,
    );
  });

  test('Deve retornar o periodo com a diferença de 10 dias', () => {
    const { dataInicio, dataFim } =
      retornaDateISOStringPeriodoUltimosDias('10');

    expect(dataFim).toContain(
      `${anoAtual}-${String(mesAtual).padStart(2, '0')}-${String(
        diaAtual,
      ).padStart(2, '0')}T`,
    );

    expect(new Date(dataInicio).getTime()).toBe(
      new Date(dataFim).getTime() - 1000 * 60 * 60 * 24 * 10,
    );
  });
});
