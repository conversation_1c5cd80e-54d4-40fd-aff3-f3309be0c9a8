import {
  EDadosParticipanteActionKind,
  IDadosParticipanteState,
  TActionsDadosParticipante,
} from '../exports';

export function dadosParticipanteReducer(
  state: IDadosParticipanteState,
  action: TActionsDadosParticipante,
): IDadosParticipanteState {
  switch (action.type) {
    case EDadosParticipanteActionKind.EDITAR_DADOS_PARTICIPANTE: {
      return {
        ...state,
        editando: true,
      };
    }
    case EDadosParticipanteActionKind.CANCELAR_EDICAO_DADOS_PARTICIPANTE: {
      return {
        ...state,
        editando: false,
        salvando: false,
        idCidade: '',
        assinaturaValida: false,
        controlesLGPD: state.consetimentoOriginal,
      };
    }
    case EDadosParticipanteActionKind.SALVAR_DADOS_PARTICIPANTE: {
      return {
        ...state,
        salvando: !!action.salvar,
      };
    }
    case EDadosParticipanteActionKind.ALTERAR_CONSETIMENTOS: {
      return {
        ...state,
        controlesLGPD: action.consentimentos,
      };
    }
    case EDadosParticipanteActionKind.CONSULTAR_CEP: {
      const { idCidade } = action.novoEndereco;
      return {
        ...state,
        dadosParticipante: {
          ...state.dadosParticipante,
        },
        idCidade,
      };
    }
    case EDadosParticipanteActionKind.ALTERAR_ASSINATURA_VALIDA: {
      return {
        ...state,
        assinaturaValida: action.assinaturaValida,
      };
    }
    default: {
      throw Error(`Unknown action: ${(action as { type: string }).type}`);
    }
  }
}
