import { IValorContribuicao } from '../exports';

export const validarInputValorDistruibuicao = (values: IValorContribuicao) => {
  const errors: Record<string, string> = {};
  Object.entries(values.valorDistribuido).forEach(([key, value]) => {
    const valorMinimo = values.valoresMinimos[key];
    if (parseFloat(value) < parseFloat(valorMinimo)) {
      errors[`valorDistribuido.${key}`] = 'erro';
    }
  });
  return errors;
};
