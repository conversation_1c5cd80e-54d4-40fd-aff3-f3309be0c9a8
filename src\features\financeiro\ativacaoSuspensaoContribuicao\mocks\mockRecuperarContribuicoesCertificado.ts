import { IResponseStatusCoberturasContratadas } from '../types/EnumsStatusAtivacaoSuspensao.types';

export const mockRecuperarContribuicoesCertificado: IResponseStatusCoberturasContratadas =
  {
    empresaId: '500',
    contaId: '13682742',
    nomePessoaCertificado: 'DIVINO TELES DOS SANTOS',
    cpfPessoaCertificado: '22585931153',
    produtoId: '1115',
    descricaoProduto: 'Previdência - 1115',
    subCategoriaProduto: 'VGBL',
    produtoAgredado: 'false',
    valorTotalSaldo: '5923.96',
    beneficioContribuicaoCertificado: [
      {
        identificadorLegal: '15414.003127/2011-57',
        descricaoRenda: 'Renda Mensal Vitalícia',
        fatorRenda: '0.0042340632082491815',
        indRevisaoPermitida: 'S',
        tipoContribuicao: 'CREG1',
        nomeContribuicao: 'REGULAR MENSAL',
        origemContribuicao: 'PTE',
        categoriaContribuicao: 'RE',
        periodicidade: '',
        statusContribuicao: 'AC',
        dataPagamento: '2025-04-05T00:00:00-03:00',
        diaPagamento: '5',
        valorPagamento: '123.5',
        dataProximoPagamento: '2025-05-05T00:00:00-03:00',
        planoId: '30',
        beneficioId: '11',
        descricaoBeneficio: 'RESERVA',
        tipoBeneficio: 'PR',
        statusBeneficio: 'A',
        subStatusBeneficio: 'CA',
        valorBeneficio: '0.0',
        valorContribuicaoEsperado: '123.5',
        termoDesejado: null,
        tipoRenda: '1',
        valorBeneficioEsperado: '0.52',
        tipoCobertura: '1',
        tipoCalculo: '',
        status: null,
      },
      {
        identificadorLegal: '15414.002675/2011-60',
        descricaoRenda: 'Pecúlio por Morte',
        fatorRenda: '261.2749489270774',
        indRevisaoPermitida: null,
        tipoContribuicao: 'PCMO1',
        nomeContribuicao: 'PECULIO POR MORTE MENSAL',
        origemContribuicao: 'PTE',
        categoriaContribuicao: 'RE',
        periodicidade: '',
        statusContribuicao: 'AC',
        dataPagamento: '2025-04-05T00:00:00-03:00',
        diaPagamento: '5',
        valorPagamento: '50.87',
        dataProximoPagamento: '2025-05-05T00:00:00-03:00',
        planoId: '40',
        beneficioId: '13',
        descricaoBeneficio: 'PECULIO',
        tipoBeneficio: 'RI',
        statusBeneficio: 'A',
        subStatusBeneficio: 'CA',
        valorBeneficio: '13290.38',
        valorContribuicaoEsperado: '50.87',
        termoDesejado: null,
        tipoRenda: '6',
        valorBeneficioEsperado: '13290.38',
        tipoCobertura: '2',
        tipoCalculo: 'Uma única vez',
        status: null,
      },
    ],
  };
