export interface IResponseTransferenciaEntreFundos {
  msgErroExcessao?: string;
  numTransferencia: string;
  reservasDestino: TResponseFundosDestino[];
  reservasExistentes: TResponseFundosExistentes[];
  sta: string;
  desSta: string;
  numMaxFundos: number;
  vlrMinimo: number;
  valorRetirada: number;
  qndDispFundoExistente: number;
  qndDispNovoFundo: number;
}

export type TResponseFundosExistentes = {
  vlrMinContribRegular: string;
  codFundo: string;
  desPerfilFundo: string;
  pctRentabUltimoAno: number;
  saldo: string;
  vlrContribuicao: string;
  desFundo: string;
  vlrMinPermanencia: string;
  vlrSaldo: string;
};

export type TResponseFundosDestino = {
  codFundo: string;
  codObjetivo: number;
  codReserva: string;
  desFundo: string;
  desReserva: string;
  desPerfilFundo: string;
  numCnpj: string;
  pctRentabUltimoAno: number;
  codPerfilFundo: string;
  vlrMinDiversificacao: number;
  vlrMinContribRegular: number;
};
