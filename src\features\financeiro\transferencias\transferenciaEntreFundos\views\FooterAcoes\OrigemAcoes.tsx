import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const OrigemAcoes = (): React.ReactElement => {
  const { etapa } = TransferenciaEntreFundos.useTransferenciaContext();
  const { finalizarEtapa, podeProsseguir } =
    TransferenciaEntreFundos.useProsseguirOrigem();

  return (
    <TransferenciaEntreFundos.SwitchCase fallback={undefined}>
      <TransferenciaEntreFundos.Match
        when={
          etapa ===
          TransferenciaEntreFundos.EEtapasTranferencia.DefinirFundosOrigem
        }
      >
        <TransferenciaEntreFundos.Grid justify="flex-end" margin="2rem 0 1rem">
          <TransferenciaEntreFundos.Button
            disabled={!podeProsseguir}
            variant="secondary"
            onClick={finalizarEtapa}
          >
            {
              TransferenciaEntreFundos.CONSTANTES.SESSOES
                .FOOTER_BOTAO_PROSSEGUIR
            }
          </TransferenciaEntreFundos.Button>
        </TransferenciaEntreFundos.Grid>
      </TransferenciaEntreFundos.Match>
    </TransferenciaEntreFundos.SwitchCase>
  );
};
