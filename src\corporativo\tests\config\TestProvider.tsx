/* eslint-disable import/no-extraneous-dependencies */
import { PropsWithChildren, ReactNode } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AppProviders from '@src/corporativo/context/AppProviders';

const TestProvider = ({ children }: PropsWithChildren) => (
  <AppProviders>
    <QueryClientProvider client={new QueryClient()}>
      {children}
    </QueryClientProvider>
  </AppProviders>
);

const customRender = (ui: ReactNode, options?: RenderOptions): RenderResult =>
  render(ui, { wrapper: TestProvider, ...options });

export * from '@testing-library/react';
export { customRender as render };
