import { CONSTANTES } from '@src/features/consulta/reajusteAnualDoPlano/exports';

type TObterExplicacaoReajusteAnualPlano = (
  explicacaoTipo: 'REAJUSTE' | 'REENQUADRAMENTO',
) => {
  titulo: string;
  valor: string;
}[];

export const obterExplicacaoReajusteAnualPlano: TObterExplicacaoReajusteAnualPlano =
  (explicacaoCampo: 'REAJUSTE' | 'REENQUADRAMENTO') => {
    const chavesTextosEmComum = Object.keys(CONSTANTES.TEXTOS.COMMON);

    return chavesTextosEmComum.map((chave, index) => {
      const titulo =
        CONSTANTES.TEXTOS.COMMON[
          chave as keyof typeof CONSTANTES.TEXTOS.COMMON
        ];

      const valor = Object.values(CONSTANTES.TEXTOS[explicacaoCampo])[index];

      return {
        titulo,
        valor,
      };
    });
  };
