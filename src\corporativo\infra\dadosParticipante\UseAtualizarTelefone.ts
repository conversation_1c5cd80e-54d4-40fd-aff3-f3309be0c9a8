import { useContext } from 'react';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { IDadosBuscarEnderecoCepResponse } from '@src/corporativo/types/dadosParticipante';
import { IDadosBuscarEnderecoCepRequest } from '@src/corporativo/types/dadosParticipante/consultar/IDadosBuscarEnderecoCepResponse';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '../config/api/endpoints';

export const useAtualizarTelefone = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { response, loading, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IDadosBuscarEnderecoCepRequest,
      IDadosBuscarEnderecoCepResponse
    >(PECOS.AtualizarTelefone, {
      data: {
        cpfCnpj: getSessionItem('cpfCnpj'),
        numeroCertificado: certificadoAtivo.certificadoNumero,
      },
      autoFetch: false,
    });

  return {
    response: tryGetValueOrDefault([response?.entidade], null),
    atualizarTelefone: invocarApiGatewayCvpComToken,
    loading,
  };
};
