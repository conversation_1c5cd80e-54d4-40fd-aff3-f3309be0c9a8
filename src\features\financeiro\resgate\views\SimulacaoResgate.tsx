import * as Resgate from '@src/features/financeiro/resgate/exports';

export const SimulacaoResgate = (): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();
  const resgateForm = Resgate.useResgateFormSetup();
  const simulacao = Resgate.useSimulacaoResgate();
  const selecaoAliquota = Resgate.useSelecaoAliquota(simulacao);
  const solicitacao = Resgate.usePrepararSolicitacaoResgate(selecaoAliquota);

  return (
    <Resgate.Grid margin="18" container>
      <Resgate.SwitchCase>
        <Resgate.Match when={resgateForm.isLoadingListaFundosParaResgate}>
          <Resgate.LoaderResgate
            loadingText={Resgate.LOADING_TEXTS.DADOS_RESGATE}
          />
        </Resgate.Match>

        <Resgate.Match when={!resgateForm.isLoadingListaFundosParaResgate}>
          <Resgate.FiltroSimulacaoResgate
            listaFundosParaResgate={resgateForm.listaFundosParaResgate}
            isTipoResgateParcial={simulacao.isTipoResgateParcial}
            saldoTotal={resgateForm.saldoTotal}
            selecionarTipoResgate={simulacao.selecionarTipoResgate}
          />
          <Resgate.TabelaSimulacaoResgate
            fundosParaResgate={simulacao.fundosParaResgate}
            resultadoCalculoResgateRestante={
              simulacao.resultadoCalculoResgateRestante
            }
            isDisabledBtnSimulacaoResgate={
              simulacao.isDisabledBtnSimulacaoResgate
            }
            confirmarSimulacao={selecaoAliquota.confirmarSimulacao}
            reiniciarSimulacao={selecaoAliquota.reiniciarSimulacao}
            isLoadingConfirmacaoSimulacao={
              selecaoAliquota.isLoadingConfirmacaoSimulacao
            }
          />

          <Resgate.Match when={selecaoAliquota.isLoadingConfirmacaoSimulacao}>
            <Resgate.LoaderResgate
              loadingText={Resgate.LOADING_TEXTS.SELECAO_ALIQUOTA}
            />
          </Resgate.Match>
        </Resgate.Match>
      </Resgate.SwitchCase>

      <Resgate.ErroSimulacao
        erroSimulacao={selecaoAliquota.mensagens.erroSimulacao}
      />

      <Resgate.ConditionalRenderer
        condition={!!resgateFeatureData?.hasDadosSimulacao}
      >
        <Resgate.TabelasEscolhaAliquotas
          aliquota={resgateForm.listaFundosParaResgate?.aliquota}
          obterDadosPorAliquota={selecaoAliquota.obterDadosPorAliquota}
          validarAliquotaDesabilitada={
            selecaoAliquota.validarAliquotaDesabilitada
          }
          selecionarOpcaoAliquota={selecaoAliquota.selecionarOpcaoAliquota}
        />
        <Resgate.BotoesAcaoResgate
          onClickDetalhamento={selecaoAliquota.toggleModalDetalhamento}
          onClickSolicitarResgate={solicitacao.toggleModalObservacoes}
        />
      </Resgate.ConditionalRenderer>

      <Resgate.ModalConfirmacaoAliquota
        isOpen={selecaoAliquota.isOpen}
        toggleModalConfirmacaoAliquota={
          selecaoAliquota.toggleModalConfirmacaoAliquota
        }
        cancelarEscolhaAliquota={selecaoAliquota.cancelarEscolhaAliquota}
        confirmarEscolhaAliquota={selecaoAliquota.confirmarEscolhaAliquota}
      />
      <Resgate.ModalDetalhesAliquota
        isOpenModalDetalhamento={selecaoAliquota.isOpenModalDetalhamento}
        toggleModalDetalhamento={selecaoAliquota.toggleModalDetalhamento}
        obterDadosPorAliquota={selecaoAliquota.obterDadosPorAliquota}
        objetoEmail={selecaoAliquota.objetoEmail}
        customizarLinhasTabela={
          selecaoAliquota.customizarEstiloDataTableDetalhesAliquota
        }
      />
      <Resgate.ModalObservacoesSolicitacaoResgate
        isOpenModalObservacoes={solicitacao.isOpenModalObservacoes}
        toggleModalObservacoes={solicitacao.toggleModalObservacoes}
        isLoadingPrepararSolicitacao={solicitacao.isLoadingPrepararSolicitacao}
        prepararSolicitacaoResgate={solicitacao.prepararSolicitacaoResgate}
      />
    </Resgate.Grid>
  );
};
