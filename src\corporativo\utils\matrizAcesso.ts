import { TImprimirDocumentosPermissoes } from '@src/corporativo/types/matrizAcesso/IUseMatrizAcesso';

export const verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias = (
  matrizAcessoPermissoes: string[],
  permissoesComponente: string[],
): boolean => {
  return (
    !permissoesComponente.length ||
    permissoesComponente.every(permissao =>
      matrizAcessoPermissoes.includes(permissao),
    )
  );
};

export const verificarSeMatrizAcessoPossuiAlgumaPermissao = (
  matrizAcessoPermissoes: string[],
  permissoesSolicitadas: string[],
): boolean => {
  return (
    !permissoesSolicitadas.length ||
    permissoesSolicitadas.some(permissao =>
      matrizAcessoPermissoes.includes(permissao),
    )
  );
};

export const retornarTodasPermissoesDaCategoria = (
  mapCategoriaPermissao: TImprimirDocumentosPermissoes,
): string[] => {
  return Object.values(mapCategoriaPermissao).flatMap(
    item => item.permissions || [],
  );
};
