module.exports = {
  presets: [
    [
      "@babel/preset-env",
      {
        useBuiltIns: "usage",
        corejs: 3,
        targets: "> 0.25%, not dead"
      }
    ],
    [
      "@babel/preset-react",
      {
        runtime: "automatic",
        development: process.env.BABEL_ENV === "development"
      }
    ],
    "@babel/preset-typescript"
  ],
  plugins: [
    [
      "@babel/plugin-transform-runtime",
      {
        useESModules: true,
        corejs: 3
      }
    ],
    process.env.BABEL_ENV === "development" && "react-refresh/babel"
  ].filter(Boolean),
  env: {
    production: {
      presets: [
        [
          "@babel/preset-env",
          {
            modules: false
          }
        ]
      ],
      plugins: [
        "transform-react-remove-prop-types"
      ]
    },
    test: {
      presets: [
        [
          "@babel/preset-env",
          {
            targets: {
              node: "current"
            }
          }
        ]
      ],
      plugins: [
        "dynamic-import-node"
      ]
    }
  }
};
