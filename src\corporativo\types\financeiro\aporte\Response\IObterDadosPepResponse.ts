export interface IObterDadosPepResponse {
  pep: boolean;
  dadosPep: IDadosPep[];
}

export interface IDadosPep {
  cpfCnpj: string;
  nomeRazaoSocial: string;
  apelidoNomeFantasia: string;
  sexo: string;
  dataNascimento: string;
  nomeCargo: string;
  nomeOrgao: string;
  municipioCargo: string;
  ufCargo: string;
  dataInicioExercicio: string;
  dataFimExercicio: string;
  dataFimVigenciaPep: string;
  tipoPep: string;
  cpfPepRelacionado: string;
  nomePepRelacionado: string;
  grauRelacionamento: string;
  cnpjEmpresaRelacionada: string;
  nomeEmpresaRelacionada: string;
  tipoPessoa: string;
  textoNoticia: string;
  dataCarga: string;
}
