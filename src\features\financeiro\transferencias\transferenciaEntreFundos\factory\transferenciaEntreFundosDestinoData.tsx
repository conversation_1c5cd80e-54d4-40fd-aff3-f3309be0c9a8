import * as T from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

type TFundoDestinoComComponentes = Omit<
  T.TResponseFundosDestino,
  'desPerfilFundo'
> & {
  desPerfilFundo: React.ReactNode;
  selecionar: React.ReactNode;
  transferenciaValor: React.ReactNode;
};

export const transferenciaEntreFundosDestinoData = (
  fundos: T.TResponseFundosDestino[],
  transferenciaDestino: T.TUseTransferenciaDestino,
  transferenciaContext: T.TTransferenciaContext,
): TFundoDestinoComComponentes[] => {
  const { fundosDestinos, etapa } = transferenciaContext;
  const {
    alteraValorTransferenciaFundo,
    fundosDestinosPorEtapa,
    selecionaFundoDestino,
  } = transferenciaDestino;

  const listaFundos = fundosDestinosPorEtapa(fundos);

  return listaFundos.map(fundo => {
    const fundoDestinoAtivo = fundosDestinos[fundo.codFundo];
    return {
      ...fundo,
      desPerfilFundo: (
        <T.PerfilDoRisco perfil={fundo.desPerfilFundo as T.TTiposDePerfis} />
      ),
      selecionar: (
        <T.Checkbox
          checked={Boolean(fundoDestinoAtivo)}
          disabled={etapa === T.EEtapasTranferencia.ValidarOperacao}
          onChange={e => selecionaFundoDestino(!!e, fundo)}
          variant="outlineBlack"
        />
      ),
      transferenciaValor: (
        <T.InputCurrency
          width="100px"
          onChangeEvent={currencyValue =>
            alteraValorTransferenciaFundo(fundo, currencyValue.unmaskedValue)
          }
          disabled={
            etapa === T.EEtapasTranferencia.ValidarOperacao ||
            fundoDestinoAtivo?.transferenciaValor === undefined
          }
          variant="box-classic"
          size="small"
          value={fundoDestinoAtivo?.transferenciaValor}
        />
      ),
    };
  });
};
