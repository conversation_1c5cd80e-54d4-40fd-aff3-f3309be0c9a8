import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { IDadosBuscarEnderecoCepResponse } from './IDadosBuscarEnderecoCepResponse';

export type TUseBuscarEnderecoCep = () => {
  response: IDadosBuscarEnderecoCepResponse | null;
  loading: boolean;
  buscarEnderecoCep: (payload: {
    cep: string;
  }) => Promise<
    IHandleReponseResult<IDadosBuscarEnderecoCepResponse> | undefined
  >;
};
