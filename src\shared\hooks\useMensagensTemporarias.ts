import { useState } from 'react';
import { IUseMensagensTemporarias } from '@src/shared/types/IUseMensagensTemporarias';

/**
 * Hook para gerenciar múltiplas mensagens temporárias com timeout automático.
 *
 * Ideal para exibir mensagens (ex: erros ou avisos) que devem desaparecer após um tempo.
 *
 * @param delay - Tempo em milissegundos para exibir cada mensagem (padrão: 10000 ms).
 * @returns Um objeto com as mensagens ativas e uma função para configurar cada uma.
 *
 * @example
 * const { mensagens, configurarMensagem } = useMensagensTemporarias();
 *
 * configurarMensagem('erroSimulacao', 'Erro ao simular resgate');
 *
 * return (
 *   <>{mensagens.erroSimulacao && <div>{mensagens.erroSimulacao}</div>}</>
 * );
 */
export const useMensagensTemporarias = (
  delay = 10000,
): IUseMensagensTemporarias => {
  const [mensagens, setMensagens] = useState<Record<string, string>>({});

  const configurarMensagem = (chave: string, mensagem: string): void => {
    setMensagens(prev => ({ ...prev, [chave]: mensagem }));

    setTimeout(() => {
      setMensagens(prev => {
        const mensagensClone = { ...prev };
        delete mensagensClone[chave];
        return mensagensClone;
      });
    }, delay);
  };

  return { mensagens, configurarMensagem };
};
