import * as columnsRendaImports from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const COLUMNS_SIMULACAO_DE_RENDA: columnsRendaImports.TableColumn<columnsRendaImports.ISimulacaoRow>[] =
  [
    {
      id: 'label',
      name: '<PERSON>mp<PERSON><PERSON>',
      cell: row => (
        <columnsRendaImports.Text variant="text-small-700">
          {row.label}
        </columnsRendaImports.Text>
      ),
      sortable: false,
      width: '25%',
    },
    {
      id: 'progressiva',
      name: <columnsRendaImports.TooltipTableProgressiva />,
      cell: row => (
        <columnsRendaImports.TabelaCelulaConsulta text={row.progressiva} />
      ),
      sortable: false,
      width: '25%',
    },
    {
      id: 'regressiva',
      name: <columnsRendaImports.TooltipTableRegressiva />,
      cell: row => (
        <columnsRendaImports.TabelaCelulaConsulta text={row.regressiva} />
      ),
      sortable: false,
      width: '25%',
    },
    {
      id: 'estimativa',
      name: 'Estimativa de renda planejada',
      cell: row => (
        <columnsRendaImports.TabelaCelulaConsulta text={row.estimativa} />
      ),
      sortable: false,
      width: '25%',
    },
  ];
