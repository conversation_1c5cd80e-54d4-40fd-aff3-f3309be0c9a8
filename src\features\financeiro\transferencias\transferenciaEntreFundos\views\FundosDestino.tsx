import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const TransferenciaEntreFundosDestino: React.FC = () => {
  const { verMais, etapa } = TransferenciaEntreFundos.useTransferenciaContext();
  const { consultaDestino } =
    TransferenciaEntreFundos.useTransferenciaServicosContext();
  const transferenciaContext =
    TransferenciaEntreFundos.useTransferenciaContext();
  const transferenciaDestino =
    TransferenciaEntreFundos.useTransferenciaDestino();

  const dataTransferenciaEntreFundosDestino =
    TransferenciaEntreFundos.transferenciaEntreFundosDestinoData(
      consultaDestino.response?.reservasDestino ?? [],
      transferenciaDestino,
      transferenciaContext,
    );

  const dataDestino = dataTransferenciaEntreFundosDestino.slice(
    0,
    verMais ? dataTransferenciaEntreFundosDestino.length : 2,
  );

  return (
    <>
      <TransferenciaEntreFundos.SwitchCase fallback={undefined}>
        <TransferenciaEntreFundos.Match
          when={TransferenciaEntreFundos.checkIfAllItemsAreTrue([
            !consultaDestino.loading,
            etapa !==
              TransferenciaEntreFundos.EEtapasTranferencia.DefinirFundosOrigem,
          ])}
        >
          <TransferenciaEntreFundos.Text
            variant="text-standard-700"
            fontColor="content-neutral-04"
            marginBottom="1rem"
            marginTop="1rem"
          >
            {TransferenciaEntreFundos.CONSTANTES.SESSOES.DESTINO_TITULO}
          </TransferenciaEntreFundos.Text>

          <TransferenciaEntreFundos.TableTranferenciaEntreFundos
            themeTable="cvp-05"
            columns={
              TransferenciaEntreFundos.trasferenciaEntreFundosColunasDestino
            }
            data={dataDestino}
          />
        </TransferenciaEntreFundos.Match>
      </TransferenciaEntreFundos.SwitchCase>
      <TransferenciaEntreFundos.ModalTransferencia
        onClickConfirmar={transferenciaDestino.confirmaDefinicaoTransferencia}
      >
        <TransferenciaEntreFundos.Text
          variant="text-standard-400"
          marginTop="0"
        >
          {
            TransferenciaEntreFundos.CONSTANTES.ALERTAS
              .MENSAGEM_PROSSEGUIR_ATENCAO_MODAL
          }
        </TransferenciaEntreFundos.Text>
        <TransferenciaEntreFundos.Text
          variant="text-standard-400"
          marginTop="0"
        >
          {
            TransferenciaEntreFundos.CONSTANTES.ALERTAS
              .MENSAGEM_PROSSEGUIR_MODAL
          }
        </TransferenciaEntreFundos.Text>
      </TransferenciaEntreFundos.ModalTransferencia>
    </>
  );
};
