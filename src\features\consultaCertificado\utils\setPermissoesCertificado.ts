import { setLocalItem, tryGetValueOrDefault } from '@cvp/utils';
import { ChavesArmazenamento } from '@src/corporativo/constants/ChavesArmazenamento';
import { IMatrizCertificado } from '@src/corporativo/types/shared/consultarMatrizAcessoPrevidencia.ts/IMatrizCertificado';

interface ISetPermissoesCertificado {
  (matriz: IMatrizCertificado | undefined): void;
}

export const setPermissoesCertificado: ISetPermissoesCertificado = (
  matriz: IMatrizCertificado | undefined,
) => {
  const permissoesCertificado = matriz?.lstServicesAvailable.map(
    item => item.operation,
  );

  setLocalItem(
    ChavesArmazenamento.USER_PERMISSIONS,
    tryGetValueOrDefault([permissoesCertificado], []),
  );
};
