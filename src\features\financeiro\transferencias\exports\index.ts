export {
  useContext,
  useEffect,
  useRef,
  createContext,
  useMemo,
  useState,
} from 'react';

export {
  Checkbox,
  InputCurrency,
  Select,
  Table,
  Button,
  Dialog,
  Text,
  Grid,
  GridItem,
  Alert,
  IconWarningSharp,
  IconCheckCircleRound,
  useTheme,
} from '@cvp/design-system-caixa';

export {
  capitalize,
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  converterBase64,
  formatarDataHoraAmigavel,
  formatarValorPadraoBrasileiro,
  getSessionItem,
  getTernaryResult,
  porcentagem,
  setSessionItem,
  tryGetValueOrDefault,
  tryGetMonetaryValueOrDefault,
  valoresMonetarios,
} from '@cvp/utils';

export { styled } from 'styled-components';
export { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';

export type { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
export type { TAssinaturaTransferencia } from '@src/features/financeiro/types/TAssinaturaTransferencia';

export { Alerta } from '@src/corporativo/components/Alerta';
export { default as Assinatura } from '@src/corporativo/components/Assinatura/Assinatura';
export { useRegistrarTokenAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useRegistrarTokenAssinaturaCaixa';
export { default as useValidarAssinatura } from '@src/shared/hooks/useValidarAssinatura';
export { useRealizarAssinaturaTransferencia } from '@src/features/financeiro/hook/useRealizarAssinaturaTransferencia';

export { default as useInputTipoTransferencia } from '@src/features/financeiro/transferencias/hooks/useInputTipoTransferencia';
export { useFiltraTipoTransferencia } from '@src/features/financeiro/transferencias/hooks/useFiltraTipoTransferencia';

export * from '@src/corporativo/context/financeiro/transferencias';
export { useTransferenciaContext } from '@src/corporativo/hooks/useTransferenciaContext';
export {
  verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias,
  retornarTodasPermissoesDaCategoria,
} from '@src/corporativo/utils/matrizAcesso';

export { SwitchCase, Match } from '@cvp/componentes-posvenda';

export { TransferenciaServicosContext } from '@src/corporativo/context/financeiro/transferencias/TransferenciaServicosContext';
export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
export { MatrizAcessoContext } from '@src/corporativo/context/MatrizAcessoContext';
export { TIPO_TRANSFERENCIA_OPCOES_PERMISSOES } from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';
export * from '@src/features/financeiro/transferencias/constants';
export * from '@src/corporativo/types/transferencias';

export type { TSelectOption } from '@src/features/financeiro/transferencias/types/TSelectOption';

export type { TUseComprovanteSolicitacao } from '@src/features/financeiro/transferencias/types/TUseComprovanteSolicitacao';

export { default as AssinaturaTransferencias } from '@src/features/financeiro/transferencias/views/AssinaturaTransferencias';

export { default as PerfilDoRisco } from '@src/corporativo/components/PerfilDoRisco/PerfilDoRisco';
export { Carregando } from '@src/corporativo/components/Carregando';

export { PREV_PERMISSIONS } from '@src/corporativo/factories/matrizAcesso/factoryPerfilPermissoes';
export { tipoEmailConstants } from '@src/corporativo/infra/email/tipoEmail';

export type { IObterComprovanteResgateResponse } from '@src/shared/types/ObterComprovanteResgate/IObterComprovanteResgate';
export type { IHandleReponseResult } from '@cvp/componentes-posvenda';
export type { IResponseHistoricoSolicitacoes } from '@src/corporativo/types/consulta/historicoSolicitacao';

export { useObterComprovante } from '@src/shared/infra/financeiro/useObterComprovante';

export { AddCircleOutline } from '@mui/icons-material';
export { useNavigate } from 'react-router-dom';
export { default as React } from 'react';
