import {
  EEtapasAporte,
  ORIGEM_RECURSOS,
  useAporteContext,
  useState,
} from '@src/features/financeiro/aporte/exports';

type TUseOrigemRecursosAporte = () => {
  declaracaoOrigem: string;
  openTextArea: boolean;
  openModal: boolean;
  etapa: EEtapasAporte;
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleOrigemRecursoEditada: (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => void;
  handleOrigemRecurso: (valueRadio: string) => void;
  handleProximaEtapa: () => void;
  setEtapa: React.Dispatch<React.SetStateAction<EEtapasAporte>>;
};

export const useOrigemRecursosAporte: TUseOrigemRecursosAporte = () => {
  const { etapa, setOrigemRecurso, setEtapa } = useAporteContext();

  const [openTextArea, setOpenTextArea] = useState<boolean>(false);

  const [openModal, setOpenModal] = useState<boolean>(false);

  const [declaracaoOrigem, setDeclaracaoOrigem] = useState<string>('');

  const handleOpenTextArea = (valueRadio: string) => {
    switch (valueRadio) {
      case ORIGEM_RECURSOS[11].descricao:
        return setOpenTextArea(true);
      case ORIGEM_RECURSOS[10].descricao:
        setOpenTextArea(false);
        return setOpenModal(true);
      default:
        setOpenTextArea(false);
    }
    return undefined;
  };

  const handleOrigemRecurso = (valueRadio: string) => {
    setDeclaracaoOrigem(valueRadio);

    handleOpenTextArea(valueRadio);
  };

  const handleOrigemRecursoEditada = (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    setDeclaracaoOrigem(event.target.value);
  };

  const handleProximaEtapa = () => {
    setOrigemRecurso(declaracaoOrigem);
    setEtapa(EEtapasAporte.FiltrosTabelaAporte);
  };

  return {
    declaracaoOrigem,
    openTextArea,
    openModal,
    etapa,
    setOpenModal,
    handleOrigemRecursoEditada,
    handleOrigemRecurso,
    handleProximaEtapa,
    setEtapa,
  };
};
