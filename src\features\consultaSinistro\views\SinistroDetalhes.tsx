import {
  Match,
  novaDataFormatada,
  SwitchCase,
  TDetalheSinistroProps,
  Text,
  Timeline,
  tryGetValueOrDefault,
} from '../exports';

export const DetalhesSinistro: React.FC<TDetalheSinistroProps> = ({
  andamentos,
}) => {
  const timelineItems = tryGetValueOrDefault([andamentos], []).map(item => ({
    title: novaDataFormatada(item.data),
    subtitle: item.descricaoStatus,
    tooltip: {
      status: item.status,
      description: item.textoAuxiliarStatus,
    },
  }));

  return (
    <SwitchCase fallback={undefined}>
      <Match when={!timelineItems.length}>
        <Text variant="text-standard-600">
          Sinistro não possui detalhes cadastrados.
        </Text>
      </Match>
      <Match when={!!timelineItems.length}>
        <Timeline timelineItems={timelineItems} />
      </Match>
    </SwitchCase>
  );
};
