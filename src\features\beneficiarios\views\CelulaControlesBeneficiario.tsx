import {
  BeneficiariosContext,
  Conditional<PERSON><PERSON>er,
  ModalExcluirBeneficiario,
  S,
  TEXTOS_BENEFICIARIOS,
  TRowData,
  useBeneficiariosForm,
  useContext,
} from '../exports';

export const CelulaControleBeneficiarios: React.FC<{
  data: TRowData;
  coberturaId: string;
  removerBeneficiario: (index: number) => void;
}> = ({ data, removerBeneficiario, coberturaId }) => {
  const { verificarCoberturaEditando } = useBeneficiariosForm();
  const { confirmandoEdicao } = useContext(BeneficiariosContext);

  return (
    <ConditionalRenderer
      key={`controls-${data.beneficiarioId}`}
      condition={
        String(data.beneficiarioId) !== TEXTOS_BENEFICIARIOS.HERDEIROS_LEGAIS
      }
    >
      <S.TableControls>
        <ConditionalRenderer
          condition={
            verificarCoberturaEditando(coberturaId) && !confirmandoEdicao
          }
        >
          <ModalExcluirBeneficiario
            index={data.index}
            removerBeneficiario={removerBeneficiario}
          />
        </ConditionalRenderer>
      </S.TableControls>
    </ConditionalRenderer>
  );
};
