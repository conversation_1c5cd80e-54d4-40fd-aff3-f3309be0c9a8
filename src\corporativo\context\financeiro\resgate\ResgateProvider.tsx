import { useCallback, useMemo, useState } from 'react';

import { ResgateContext } from '@src/corporativo/context/financeiro/resgate/ResgateContext';
import {
  EEtapasResgate,
  IResgateContextData,
  IResgateProvider,
} from '@src/corporativo/types/financeiro/resgate/IResgateContext';

export const ResgateProvider = ({
  children,
}: IResgateProvider): React.ReactElement => {
  const [etapa, setEtapa] = useState<EEtapasResgate>(EEtapasResgate.SIMULACAO);
  const [resgateFeatureData, setResgateFeatureData] =
    useState<IResgateContextData>({} as IResgateContextData);

  const handleEtapa = useCallback((etapaAtual: EEtapasResgate): void => {
    setEtapa((prevEtapa: EEtapasResgate) => {
      if (prevEtapa !== etapaAtual) return etapaAtual;
      return prevEtapa;
    });
  }, []);

  const handleResgateFeatureData = useCallback(
    (resgateData: IResgateContextData): void => {
      setResgateFeatureData((prevData: IResgateContextData) => ({
        ...prevData,
        ...resgateData,
      }));
    },
    [],
  );

  const memorizedValues = useMemo(
    () => ({
      etapa,
      handleEtapa,
      resgateFeatureData,
      handleResgateFeatureData,
    }),
    [etapa, handleEtapa, resgateFeatureData],
  );

  return (
    <ResgateContext.Provider value={memorizedValues}>
      {children}
    </ResgateContext.Provider>
  );
};
