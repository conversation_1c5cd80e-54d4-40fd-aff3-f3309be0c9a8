import {
  Alert,
  Text,
  IconWarningSharp,
  IconCheckCircleRound,
} from '@cvp/design-system-caixa';
import { IVariantAlert } from '@cvp/design-system-caixa/dist/atoms/Alert/Alert.types';
import React from 'react';

type TAlertaProps = {
  tipo: 'atencao' | 'sucesso' | 'erro';
  children: React.ReactNode;
};

export const Alerta = ({ tipo, children }: TAlertaProps) => {
  const variant: Record<TAlertaProps['tipo'], IVariantAlert['variant']> = {
    atencao: 'warning-01',
    erro: 'danger-01',
    sucesso: 'success-01',
  };

  const AlertaIcones: Record<TAlertaProps['tipo'], React.ReactNode> = {
    atencao: <IconWarningSharp size="big" color="#CA9804" />,
    erro: <IconWarningSharp size="big" color="#B22C2C" />,
    sucesso: <IconCheckCircleRound size="large" color="#127527" />,
  };

  const Icone = AlertaIcones[tipo];

  return (
    <Alert variant={variant[tipo]} icon={Icone}>
      <Text variant="text-standard-400" fontColor="content-neutral-04">
        {children}
      </Text>
    </Alert>
  );
};
