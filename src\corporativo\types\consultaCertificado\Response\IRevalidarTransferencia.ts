import { IResponse } from './IResponse';

export interface IResponseRevalidarTransferencia
  extends IResponse<IResponseRevalidarTransferenciaEntidade> {}

export interface IResponseRevalidarTransferenciaEntidade {
  numTransferencia: string;
  pendenciasRevalidarTransferencia: TPendencias[];
  statusRevalidarTransferencia: string;
  descricaoStatusRevalidarTransferencia: string;
}

export type TPendencias = {
  codigoErro: string;
  descricaoPendencia: string;
};
