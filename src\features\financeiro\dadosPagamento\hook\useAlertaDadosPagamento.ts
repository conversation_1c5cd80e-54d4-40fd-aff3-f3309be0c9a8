import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

export const useAlertaDadosPagamento =
  (): DadosPagamento.TUseAlertaDadosPagamento => {
    const {
      resetarMensagens,
      responseAtualizacaoFormaPagamento,
      responseAtualizaDiaVencimento,
      responseValidacaoConta,
    } = DadosPagamento.useAlterarDadosPagamento();
    const [alertas, setAlertas] = DadosPagamento.useState<
      DadosPagamento.TAlertaDadosPagamento[]
    >([]);

    const exibirAlertas = DadosPagamento.useCallback((): void => {
      const alertasAlterarFormaPagamento = DadosPagamento.obterAlertaFactory({
        response: responseAtualizacaoFormaPagamento!,
      });

      const alertasAlterarDiaVencimento = DadosPagamento.obterAlertaFactory({
        response: responseAtualizaDiaVencimento!,
      });

      const alertasValidaConta = DadosPagamento.obterAlertaFactory({
        response: responseValidacaoConta!,
        codigoRetorno: responseValidacaoConta?.entidade?.codigoRetorno,
        descricaoMensagemSistema:
          responseValidacaoConta?.entidade?.descricaoMensagemSistema,
        exibirMultiplosAlertas:
          responseValidacaoConta?.entidade?.codigoRetorno !==
          DadosPagamento.CODIGOS_RETORNO.X5,
      });

      const mensagens = [
        ...alertasAlterarFormaPagamento,
        ...alertasAlterarDiaVencimento,
        ...alertasValidaConta,
      ];

      setAlertas(mensagens);

      if (mensagens.length) {
        setTimeout(() => {
          resetarMensagens();
        }, 5000);
      }
    }, [
      resetarMensagens,
      responseAtualizaDiaVencimento,
      responseAtualizacaoFormaPagamento,
      responseValidacaoConta,
    ]);

    DadosPagamento.useEffect(() => {
      exibirAlertas();
    }, [
      exibirAlertas,
      resetarMensagens,
      responseAtualizaDiaVencimento,
      responseAtualizacaoFormaPagamento,
      responseValidacaoConta,
    ]);

    return {
      alertas,
    };
  };
