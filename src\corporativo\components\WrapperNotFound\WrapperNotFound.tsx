import { Grid, Text } from '@cvp/design-system-caixa';
import { TEXTOS_WRAPPER_NOT_FOUND } from '../../constants/wrapperNotFound/TextosWrapperNotFound';

const WrapperNotFound: React.FC = () => {
  return (
    <Grid justify="center" gap={2} margin="32px 24px">
      <Text variant="text-standard-400" fontColor="content-neutral-05">
        {TEXTOS_WRAPPER_NOT_FOUND.PRODUTO_NAO_LOCALIZADO}
      </Text>
    </Grid>
  );
};

export default WrapperNotFound;
