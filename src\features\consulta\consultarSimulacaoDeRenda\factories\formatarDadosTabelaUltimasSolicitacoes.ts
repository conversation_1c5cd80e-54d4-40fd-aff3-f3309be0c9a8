import {
  IUltimasSimulacoesRow,
  IUseVerUltimasSolicitacoesResponse,
  tryGetMonetaryValueOrDefault,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports';

export const formatarDadosTabelaUltimasSolicitacoes = (
  data: IUseVerUltimasSolicitacoesResponse[],
): IUltimasSimulacoesRow[] =>
  data.map(item => {
    return {
      dataHora: `${item.dthDiaSimulacaoFormatada} - ${item.dthHoraSimulacaoFormatada}`,
      tipoRenda: item.nomTipoPagamento,
      tributacao: item.tipoTributacao,
      periodoRecebimento: item.descPeridoBeneficiarioRecebe,
      rendaLiquida: tryGetMonetaryValueOrDefault(item.vlrBeneficioLiquido),
    };
  });
