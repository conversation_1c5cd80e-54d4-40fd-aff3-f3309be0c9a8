import {
  IContaExistente,
  IMapearContaExistenteFormFactory,
  INITIAL_STATE_FORMIK,
  tryGetValueOrDefault,
} from '@src/features/financeiro/resgate/exports';

/**
 * Mapear uma conta bancária existente para o formato do formulário
 *
 * @param {Object} params - Parâmetros para mapear conta existente
 * @param {Array} params.tiposPagamento - Lista de tipos de pagamento disponíveis
 * @param {string} params.codigoContaSelecionada - Código da conta bancária selecionada
 * @returns {IContaExistente} Objeto contendo os dados formatados da conta selecionada
 * ou o estado inicial quando a conta não é encontrada ou não há tipos de pagamento
 */
export const mapearContaExistenteFormFactory = ({
  tiposPagamento,
  codigoContaSelecionada,
}: IMapearContaExistenteFormFactory): IContaExistente => {
  if (!tiposPagamento?.length) return INITIAL_STATE_FORMIK.contaExistente;

  const contaEncontrada = tiposPagamento.find(
    tipoPagamento =>
      tipoPagamento.codigoContaBancaria === codigoContaSelecionada,
  );

  if (!contaEncontrada) return INITIAL_STATE_FORMIK.contaExistente;

  return {
    nomeCanal: tryGetValueOrDefault([contaEncontrada.nomeCanal], ''),
    codigoBanco: tryGetValueOrDefault([contaEncontrada.codigoBanco], ''),
    tipoCanal: tryGetValueOrDefault([contaEncontrada.tipoCanal], ''),
    codigoContaBancaria: tryGetValueOrDefault(
      [contaEncontrada.codigoContaBancaria],
      '',
    ),
    digitoAgencia: tryGetValueOrDefault([contaEncontrada.digitoAgencia], ''),
    digitoConta: tryGetValueOrDefault([contaEncontrada.digitoConta], ''),
    tipoContaBancaria: tryGetValueOrDefault(
      [contaEncontrada.tipoContaBancaria],
      '',
    ),
    descricaoTipoContaBancaria: tryGetValueOrDefault(
      [contaEncontrada.descricaoTipoContaBancaria],
      '',
    ),
    nomeBanco: tryGetValueOrDefault([contaEncontrada.nomeBanco], ''),
    numeroAgencia: tryGetValueOrDefault([contaEncontrada.numeroAgencia], ''),
  };
};
