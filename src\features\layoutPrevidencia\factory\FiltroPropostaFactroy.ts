import {
  ICertificadoPrevidenciaResponse,
  TStatusContratoFilter,
  TProfileFilters,
  DescricaoStatusPrevidencia,
} from '../exports';

const atualizarSituacoes = (
  entidades: ICertificadoPrevidenciaResponse[],
): TProfileFilters => {
  const statusMap: { [key: string]: string } = {
    A: DescricaoStatusPrevidencia.ativo,
    B: DescricaoStatusPrevidencia.emBeneficio,
    C: DescricaoStatusPrevidencia.cancelado,
    D: DescricaoStatusPrevidencia.desligado,
    E: DescricaoStatusPrevidencia.encerrado,
    P: DescricaoStatusPrevidencia.pendente,
    U: DescricaoStatusPrevidencia.ativoCobrancaSuspensa,
    S: DescricaoStatusPrevidencia.sinistro,
    T: DescricaoStatusPrevidencia.emTransicao,
  };

  const filtro: TStatusContratoFilter[] = [];
  entidades.forEach(({ situacao }) => {
    const descricaoStatus = statusMap[situacao];
    const statusJaExiste = filtro.some(
      status => status.description === descricaoStatus,
    );

    if (!statusJaExiste && descricaoStatus) {
      filtro.push({
        id: filtro.length + 1,
        description: descricaoStatus,
        situacao,
      });
    }
  });

  return {
    ANALISTA_POS_VENDA: filtro,
    MEDICO: filtro,
    OPERADOR: filtro,
  };
};

export default atualizarSituacoes;
