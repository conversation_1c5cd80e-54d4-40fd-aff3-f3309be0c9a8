import * as T from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

type TFundoDestinoComComponentes = {
  desPerfilFundo: React.ReactNode;
  selecionar: React.ReactNode;
  tipoFundo: React.ReactNode;
  transferenciaValor?: React.ReactNode;
  codFundo: string;
  codPerfilFundo: string;
  vlrSaldo: number;
};

export const transferenciaEntreFundosOrigemData = (
  fundos: T.TTransferenciaFundo[],
  transferenciaOrigem: T.TUseTransferenciaOrigem,
  transferenciaContext: T.TTransferenciaContext,
): TFundoDestinoComComponentes[] => {
  const { erro, etapa } = transferenciaContext;
  const {
    editaTransferenciaValorOrigem,
    selecionaReservaOrigem,
    selecionaTipoOrigem,
    reservaAtiva,
    verificaTransferenciaMultifundo,
  } = transferenciaOrigem;

  const multiFundo = verificaTransferenciaMultifundo();

  const listaTipoFundos = multiFundo
    ? T.CONSTANTES.TRANSFERENCIA_TOTAL_PARCIAL
    : T.CONSTANTES.TRANSFERENCIA_APENAS_TOTAL;

  return fundos.map(fundoOrigem => {
    const reservaFundo = reservaAtiva(fundoOrigem.codFundo);
    return {
      ...fundoOrigem,
      desPerfilFundo: <T.PerfilDoRisco perfil={fundoOrigem.desPerfilFundo} />,
      selecionar: (
        <T.Checkbox
          disabled={etapa === T.EEtapasTranferencia.ValidarOperacao}
          onChange={() => selecionaReservaOrigem(fundoOrigem)}
          variant="outlineBlack"
        />
      ),
      tipoFundo: (
        <T.Select
          disabled={
            !reservaFundo || etapa === T.EEtapasTranferencia.ValidarOperacao
          }
          minWidth="100px"
          placeholder="Selecione"
          size="small"
          variant="box-classic"
          sizeWidth="small"
          onChange={tipos => selecionaTipoOrigem(tipos, fundoOrigem)}
          options={listaTipoFundos}
        />
      ),
      ...(reservaFundo?.staTipRetirada === T.ETiposTransferencia.Total && {
        transferenciaValor: fundoOrigem.vlrSaldo,
      }),
      ...(reservaFundo?.staTipRetirada === T.ETiposTransferencia.Parcial && {
        transferenciaValor: (
          <T.GridItem>
            <T.InputCurrency
              defaultValue={reservaFundo?.valorSolicitado ?? 0}
              error={erro === T.EErroTransferencia.ValorMin}
              width="100px"
              onChangeEvent={currencyValue =>
                editaTransferenciaValorOrigem(
                  currencyValue.unmaskedValue,
                  fundoOrigem,
                )
              }
              disabled={
                etapa === T.EEtapasTranferencia.ValidarOperacao || !reservaFundo
              }
              variant="box-classic"
              size="small"
              value={reservaFundo?.valorSolicitado ?? 0}
            />
          </T.GridItem>
        ),
      }),
    };
  });
};
