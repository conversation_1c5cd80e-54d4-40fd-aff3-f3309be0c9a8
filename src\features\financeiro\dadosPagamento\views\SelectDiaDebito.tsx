import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const SelectDiaDebito: React.FC = () => {
  const { permissoesMatrizAcesso } = DadosPagamento.useContext(
    DadosPagamento.MatrizAcessoContext,
  );
  const { dadosCertificado, handleNovaContaChange } =
    DadosPagamento.useAlterarDadosPagamento();

  const isSelectDiaDebitoDisabled =
    !DadosPagamento.verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      permissoesMatrizAcesso,
      DadosPagamento.ALTERACOES_COMPONENTES_PERMISSOES.alteracao_dia_vencimento
        .permissions,
    );

  const primeiroCertificado = dadosCertificado?.[0];

  return (
    <DadosPagamento.GridItem xs="1 / 2">
      <DadosPagamento.Text variant="text-large-700">
        Dia do Débito
      </DadosPagamento.Text>
      <DadosPagamento.Select
        options={DadosPagamento.gerarOpcoesDiasVencimento()}
        size="standard"
        placeholder={DadosPagamento.tryGetValueOrDefault(
          [primeiroCertificado?.diaPagamento],
          '-',
        )}
        onChange={([selected]) =>
          handleNovaContaChange(
            DadosPagamento.changeEventBuilder('diaVencimento', selected.value),
          )
        }
        variant="box-classic"
        sizeWidth={undefined}
        disabled={isSelectDiaDebitoDisabled}
      />
    </DadosPagamento.GridItem>
  );
};

export default SelectDiaDebito;
