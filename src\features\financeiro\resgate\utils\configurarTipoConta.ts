import {
  tryGetValueOrDefault,
  CONFIGURACAO_TIPO_CONTA,
} from '@src/features/financeiro/resgate/exports';

/**
 * Normaliza e padroniza o código do tipo de conta bancária
 *
 * Esta função mapeia códigos de tipos de conta bancária para um formato padronizado,
 * permitindo uma identificação consistente independente da origem dos dados.
 *
 * @param {string} tipoContaBancaria - Código do tipo de conta bancária a ser normalizado
 * @returns {string} Código normalizado do tipo de conta ou valor padrão ('-') caso não encontrado
 */
export const configurarTipoConta = (tipoContaBancaria: string): string => {
  const valorVazio = '-';

  if (!tipoContaBancaria) return valorVazio;

  return tryGetValueOrDefault(
    [CONFIGURACAO_TIPO_CONTA[tipoContaBancaria]],
    valorVazio,
  );
};
