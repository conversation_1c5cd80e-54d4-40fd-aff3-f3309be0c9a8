import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/exports';

export const useRealizarAssinaturaTransferencia =
  (): TransferenciaEntreFundos.TAssinaturaTransferencia => {
    const { certificadoAtivo } = TransferenciaEntreFundos.useContext(
      TransferenciaEntreFundos.PrevidenciaContext,
    );
    const cpfCnpj = String(TransferenciaEntreFundos.getSessionItem('cpfCnpj'));

    const { setAssinatura } =
      TransferenciaEntreFundos.useTransferenciaContext();
    const { assinaturaValida, validarAssinatura } =
      TransferenciaEntreFundos.useValidarAssinatura();
    const { invocarApiGatewayCvpComToken } =
      TransferenciaEntreFundos.useRegistrarTokenAssinaturaCaixa();

    const realizarAssinaturaCallback = (
      response: TransferenciaEntreFundos.IAssinaturaResponse,
    ) => {
      invocarApiGatewayCvpComToken(response);
      validarAssinatura(response);
      setAssinatura(assinaturaValida);
    };

    const exibirAssinaturaInvalida =
      TransferenciaEntreFundos.checkIfAllItemsAreTrue([
        !assinaturaValida,
        !!cpfCnpj,
        !!certificadoAtivo?.numeroApolice,
      ]);

    const dadosAssinatura = {
      cpfCnpj: TransferenciaEntreFundos.tryGetValueOrDefault([cpfCnpj], ''),
      numeroCertificado: TransferenciaEntreFundos.tryGetValueOrDefault(
        [certificadoAtivo?.certificadoNumero],
        '',
      ),
    };

    return {
      dadosAssinatura,
      exibirAssinaturaInvalida,
      realizarAssinaturaCallback,
    };
  };
