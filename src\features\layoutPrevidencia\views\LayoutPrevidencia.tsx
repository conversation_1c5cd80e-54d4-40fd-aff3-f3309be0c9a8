import * as LayoutPrevidenciaExport from '@src/features/layoutPrevidencia/exports';

type TLayoutPrevidenciaProps = {
  certificado: LayoutPrevidenciaExport.ICertificadoPrevidenciaResponse;
};
const LayoutPrevidencia: React.FC<TLayoutPrevidenciaProps> = ({
  certificado,
}) => {
  const { activeTab, handleTabChange } =
    LayoutPrevidenciaExport.useTabsFromRouter();
  const { isMatrizAcessoLoading, certificadoTabs } =
    LayoutPrevidenciaExport.useContext(
      LayoutPrevidenciaExport.MatrizAcessoContext,
    );

  const location = LayoutPrevidenciaExport.useLocation();
  const navigate = LayoutPrevidenciaExport.useNavigate();

  const atualizarPosicaoScroll = () => {
    const queryParams = new URLSearchParams(location.search);
    const scrollValue = queryParams.get('scroll');

    if (scrollValue) {
      window.scrollTo({
        top: parseInt(scrollValue, 10),
        behavior: 'smooth',
      });
    }
  };

  LayoutPrevidenciaExport.useLayoutEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const scrollValue = queryParams.get('scroll');

    if (!scrollValue) {
      navigate(certificadoTabs[0].value);
    }
  }, [certificadoTabs]);

  LayoutPrevidenciaExport.useEffect(() => {
    atualizarPosicaoScroll();
  }, [location]);

  return (
    <LayoutPrevidenciaExport.MultiAccordion.Item
      value={certificado.certificadoNumero}
    >
      <LayoutPrevidenciaExport.MultiAccordion.Trigger
        onClick={() => handleTabChange(certificadoTabs[0].value)}
      >
        <LayoutPrevidenciaExport.CertificadoInfo certificado={certificado} />
        <LayoutPrevidenciaExport.StatusPrevidenciaMapper
          status={certificado.situacao}
        />
      </LayoutPrevidenciaExport.MultiAccordion.Trigger>
      <LayoutPrevidenciaExport.MultiAccordion.Content>
        <LayoutPrevidenciaExport.SwitchCase
          fallback={<LayoutPrevidenciaExport.CertificadoContentLoading />}
        >
          <LayoutPrevidenciaExport.Match when={!isMatrizAcessoLoading}>
            <LayoutPrevidenciaExport.BotoesApolice />
            <LayoutPrevidenciaExport.TabNav
              tabs={certificadoTabs}
              value={activeTab}
              onValueChange={handleTabChange}
            />
            <LayoutPrevidenciaExport.Outlet />
          </LayoutPrevidenciaExport.Match>
        </LayoutPrevidenciaExport.SwitchCase>
      </LayoutPrevidenciaExport.MultiAccordion.Content>
    </LayoutPrevidenciaExport.MultiAccordion.Item>
  );
};

export default LayoutPrevidencia;
