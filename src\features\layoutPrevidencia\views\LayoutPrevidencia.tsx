import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { MultiAccordion, TabNav } from '@cvp/design-system-caixa';
import { useContext, useEffect, useLayoutEffect } from 'react';
import { Match, SwitchCase } from '@cvp/componentes-posvenda';
import {
  BotoesApolice,
  CertificadoInfo,
  ICertificadoPrevidenciaResponse,
  MatrizAcessoContext,
  StatusCertificadoMapper,
  useTabsFromRouter,
} from '../exports';
import { CertificadoContentLoading } from './CertificadoContentLoading';

type TLayoutPrevidenciaProps = {
  certificado: ICertificadoPrevidenciaResponse;
};
const LayoutPrevidencia: React.FC<TLayoutPrevidenciaProps> = ({
  certificado,
}) => {
  const { activeTab, handleTabChange } = useTabsFromRouter();
  const { isMatrizAcessoLoading, certificadoTabs } =
    useContext(MatrizAcessoContext);

  const location = useLocation();
  const navigate = useNavigate();

  const atualizarPosicaoScroll = () => {
    const queryParams = new URLSearchParams(location.search);
    const scrollValue = queryParams.get('scroll');

    if (scrollValue) {
      window.scrollTo({
        top: parseInt(scrollValue, 10),
        behavior: 'smooth',
      });
    }
  };

  useLayoutEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const scrollValue = queryParams.get('scroll');

    if (!scrollValue) {
      navigate(certificadoTabs[0].value);
    }
  }, [certificadoTabs]);

  useEffect(() => {
    atualizarPosicaoScroll();
  }, [location]);

  return (
    <MultiAccordion.Item value={certificado.certificadoNumero}>
      <MultiAccordion.Trigger
        onClick={() => handleTabChange(certificadoTabs[0].value)}
      >
        <CertificadoInfo certificado={certificado} />
        <StatusCertificadoMapper status={certificado.situacao} />
      </MultiAccordion.Trigger>
      <MultiAccordion.Content>
        <SwitchCase fallback={<CertificadoContentLoading />}>
          <Match when={!isMatrizAcessoLoading}>
            <BotoesApolice />
            <TabNav
              tabs={certificadoTabs}
              value={activeTab}
              onValueChange={handleTabChange}
            />
            <Outlet />
          </Match>
        </SwitchCase>
      </MultiAccordion.Content>
    </MultiAccordion.Item>
  );
};

export default LayoutPrevidencia;
