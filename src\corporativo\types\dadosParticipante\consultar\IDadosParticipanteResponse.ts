import { ITelefoneParticipanteResponse } from './ITelefoneParticipanteResponse';
import { IEnderecoParticipanteResponse } from './IEnderecoParticipanteResponse';

export interface IDadosParticipanteResponse {
  tipoOperacao: string;
  numeroCertificado: string;
  numeroBilhete: string;
  canalVendas: string;
  pontoVenda: string;
  codigoUsuario: string;
  numeroMatricula: string;
  codigoCliente: string;
  agenciaContaDebito: string;
  operacaoContaDebito: string;
  numeroContaDebito: string;
  digitoContaDebito: string;
  email: string;
  dadosEnderecoParticipante: IEnderecoParticipanteResponse;
  dadosTelefoneParticipante: ITelefoneParticipanteResponse;
  diaDebito: string;
}
