import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const Acoes: React.FC<DadosPagamento.TAlterarDadosPagamentoProps> = ({
  onClose,
}) => {
  const { handleAtualizarDadosPagamento, deveAtualizarDadosPagamento } =
    DadosPagamento.useAlterarDadosPagamento();

  return (
    <DadosPagamento.Grid justify="flex-end">
      <DadosPagamento.GridItem>
        <DadosPagamento.Button onClick={onClose} variant="secondary-outlined">
          <DadosPagamento.CancelOutlined /> Cancelar
        </DadosPagamento.Button>
      </DadosPagamento.GridItem>
      <DadosPagamento.GridItem>
        <DadosPagamento.Button
          onClick={handleAtualizarDadosPagamento}
          variant="secondary"
          disabled={!deveAtualizarDadosPagamento}
        >
          <DadosPagamento.SaveRounded /> Salvar
        </DadosPagamento.Button>
      </DadosPagamento.GridItem>
    </DadosPagamento.Grid>
  );
};

export default Acoes;
