import {
  Alerta,
  ALERTAS_CONSULTA,
  AlertsTablePersonalizado,
  IAletsTable,
  Match,
  Grid,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const AlertsTable: React.FC<IAletsTable> = ({ data, income }) => {
  return (
    <>
      <Match when={!income.renda}>
        <Alerta tipo="atencao">
          <p>{ALERTAS_CONSULTA.MENSAGEM_ATENCAO}</p>
        </Alerta>
      </Match>
      <Match when={Object.keys(data).length}>
        <Grid>
          <AlertsTablePersonalizado>
            <Alerta tipo="atencao">
              {ALERTAS_CONSULTA.ESTIMATIVA_DE_RENDA.slice(0, 3).map(
                (item: string) => (
                  <p key={item}>{item}</p>
                ),
              )}{' '}
              <br />
              {ALERTAS_CONSULTA.ESTIMATIVA_DE_RENDA.slice(3, 5).map(
                (item: string) => (
                  <p key={item}>{item}</p>
                ),
              )}{' '}
              <br />
              {ALERTAS_CONSULTA.ESTIMATIVA_DE_RENDA.slice(5, 6).map(
                (item: string) => (
                  <p key={item}>{item}</p>
                ),
              )}{' '}
            </Alerta>
          </AlertsTablePersonalizado>
        </Grid>
      </Match>
    </>
  );
};
