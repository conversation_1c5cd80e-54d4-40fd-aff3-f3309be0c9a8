import {
  ACEITAR_SMS,
  EXTENSAO_NUMERO,
  ITelefone,
  LOCAL_TELEFONE,
  TIPO_TELEFONE,
  Utils,
} from '../exports';

export const montarDadosTelefone = (
  telefonesOriginal: Array<ITelefone | undefined>,
  telefonesEditados: Array<string>,
): Array<{
  telefoneId: string;
  codigoArea: string;
  numeroTelefone: string;
  aceitarSms: string;
  tipoTelefone: string;
  localTelefone: string;
  telefonePrincipal: string;
  extensaoNumero: string;
}> =>
  telefonesOriginal
    .map((telefoneOriginal, index) => {
      if (
        Utils.checkIfSomeItemsAreTrue([
          telefoneOriginal?.numeroTelefone ===
            telefonesEditados[index].slice(2),
        ])
      )
        return null;
      return {
        telefoneId: Utils.tryGetValueOrDefault(
          [telefoneOriginal?.idTelefone],
          '',
        ),
        codigoArea: telefonesEditados[index].slice(0, 2),
        numeroTelefone: telefonesEditados[index].slice(2),
        aceitarSms: Utils.getTernaryResult(
          index === 0,
          ACEITAR_SMS.SIM,
          ACEITAR_SMS.NAO,
        ),
        tipoTelefone: Utils.tryGetValueOrDefault(
          [telefoneOriginal?.tipoTelefone],
          Utils.getTernaryResult(
            index === 0,
            TIPO_TELEFONE.CELULAR,
            TIPO_TELEFONE.TELEFONE_DIRETO,
          ),
        ),
        localTelefone: Utils.getTernaryResult(
          index === 0,
          LOCAL_TELEFONE.CELULAR,
          LOCAL_TELEFONE.COMERCIAL,
        ),
        telefonePrincipal: Utils.getTernaryResult(
          index === 0,
          ACEITAR_SMS.SIM,
          ACEITAR_SMS.NAO,
        ),
        extensaoNumero: EXTENSAO_NUMERO.PADRAO,
      };
    })
    .filter(telefone => telefone !== null);
