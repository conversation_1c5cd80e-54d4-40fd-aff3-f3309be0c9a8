import { getSessionItem } from '@cvp/utils';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IDadosNomeSocialResponse,
  TUseObterNomeSocial,
} from '@src/corporativo/types/dadosParticipante';

type TPayload = {
  codCpfCnpj: string | null;
  situacao?: string;
};

export const useObterNomeSocial: TUseObterNomeSocial = () => {
  const payload: TPayload = {
    codCpfCnpj: getSessionItem('cpfCnpj'),
  };

  const { invocarApiGatewayCvpComToken, loading } = useApiGatewayCvpInvoker<
    TPayload,
    IDadosNomeSocialResponse[]
  >('PECO_RecuperarContratos_Sf', {
    data: payload,
    autoFetch: false,
  });

  return {
    invocarApiGatewayCvpComToken,
    loading,
  };
};
