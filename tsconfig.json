{"extends": "ts-config-single-spa", "compilerOptions": {"target": "es6", "lib": ["dom", "dom.iterable", "esnext"], "jsx": "react-jsx", "typeRoots": ["node_modules/@types", "node_modules"], "types": ["eslint", "jest", "@testing-library/jest-dom"], "composite": true, "strict": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@src/*": ["src/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/**/*", "tests/**/*", "src/setupTests.ts", "jest.setup.ts"], "exclude": ["node_modules", "dist"]}