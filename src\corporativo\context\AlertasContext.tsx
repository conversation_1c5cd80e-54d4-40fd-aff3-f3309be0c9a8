import { createContext, PropsWithChildren, useMemo, useState } from 'react';

export interface IAlerta {
  message: React.ReactNode;
  icon?: React.ReactNode;
  variant:
    | 'information-02'
    | 'information-01'
    | 'success-02'
    | 'success-01'
    | 'warning-02'
    | 'warning-01'
    | 'danger-02'
    | 'danger-01';
  duration?: number;
  withoutTimeout?: boolean;
}

export interface IAlertaIdentificado extends IAlerta {
  identificador: string;
}

interface IAlertaState {
  alertas: Record<string, IAlerta>;
  setAlertas: React.Dispatch<React.SetStateAction<Record<string, IAlerta>>>;
}

const initialAlertaState = {
  alertas: {},
  setAlertas: {} as VoidFunction,
};

export const AlertasContext = createContext<IAlertaState>(initialAlertaState);

export const AlertasProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [alertas, setAlertas] = useState<Record<string, IAlerta>>({});

  const state = useMemo(
    () => ({
      alertas,
      setAlertas,
    }),
    [alertas, setAlertas],
  );

  return (
    <AlertasContext.Provider value={state}>{children}</AlertasContext.Provider>
  );
};
