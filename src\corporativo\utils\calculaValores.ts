import { TFundoDestinos } from '@src/corporativo/types/transferencias';

const obtemValoresFormatadosDeFundos = (
  fundosDestinos: Record<string, TFundoDestinos>,
) => {
  return Object.keys(fundosDestinos).map(
    chave => fundosDestinos[chave].transferenciaValor ?? 0,
  );
};

export const calculaSomaEntreOrigens = (
  reservasOrigem: { valorSolicitado: number }[],
) => {
  return reservasOrigem.reduce(
    (anterior, atual) => anterior + atual.valorSolicitado,
    0,
  );
};

export const calculaSomaDistribuicaoEntreFundos = (
  fundosDestinos: Record<string, TFundoDestinos>,
) => {
  const listaFormatada = obtemValoresFormatadosDeFundos(fundosDestinos);

  const valorDistribuido = listaFormatada.reduce((previous, current) => {
    return previous + current;
  }, 0);

  if (typeof valorDistribuido !== 'number') return 0;

  return valorDistribuido;
};

export const calculaValorRestante = (
  valorDistribuidoEntreFundos: number,
  reservasOrigem: { valorSolicitado: number }[],
) => {
  const transferenciaValor = reservasOrigem.reduce(
    (anterior, atual) => anterior + atual.valorSolicitado,
    0,
  );
  const valorRestante = transferenciaValor - valorDistribuidoEntreFundos;

  if (typeof valorRestante !== 'number') return 0;
  return valorRestante;
};
