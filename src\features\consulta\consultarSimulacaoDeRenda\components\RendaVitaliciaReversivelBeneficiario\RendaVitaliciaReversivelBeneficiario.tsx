import * as ConsultarSimulacao from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const RendaVitaliciaReversivelBeneficiario: React.FC<
  ConsultarSimulacao.IRendaVitaliciaReversivelBeneficiario
> = ({
  consultarRenda,
  getOptionsSelectSexoBeneficiario,
  getOptionsSelectPorcentagemRenda,
}) => {
  const formik = ConsultarSimulacao.useFormik({
    initialValues: {
      ...ConsultarSimulacao.INITIAL_VALUES_FORMIK_RENDA_VITALICIA,
    },
    validationSchema: ConsultarSimulacao.validarReversivelAoBeneficiarioSchema,
    onSubmit: consultarRenda,
    validateOnChange: false,
  });

  return (
    <ConsultarSimulacao.ContainerRendaVitaliciaReversivelBeneficiario
      onSubmit={formik.handleSubmit}
    >
      <div>
        <ConsultarSimulacao.Select
          id="porcentagem"
          options={getOptionsSelectPorcentagemRenda}
          placeholder="Selecione"
          size="standard"
          textVariant="text-standard-600"
          sizeWidth="large"
          variant="box-classic"
          onChange={value =>
            formik.setFieldValue('pctReversao', value[0]?.value || '')
          }
          errorProps={{
            children: ConsultarSimulacao.CAMPO_OBRIGATORIO,
            show: !!formik.errors.pctReversao,
          }}
          label={
            ConsultarSimulacao.LABEL_SELECT_RENDA_VITALICIA
              .porcentagem_reversivel
          }
        />
        <ConsultarSimulacao.Select
          id="sexobeneficiario"
          options={getOptionsSelectSexoBeneficiario}
          label={
            ConsultarSimulacao.LABEL_SELECT_RENDA_VITALICIA.sexo_beneficiario
          }
          placeholder="Selecione"
          size="standard"
          textVariant="text-standard-600"
          sizeWidth="large"
          variant="box-classic"
          onChange={value =>
            formik.setFieldValue('staGeneroConjuge', value[0]?.value || '')
          }
          errorProps={{
            children: ConsultarSimulacao.CAMPO_OBRIGATORIO,
            show: !!formik.errors.staGeneroConjuge,
          }}
        />
        <span>
          <ConsultarSimulacao.Text
            variant="text-standard-600"
            fontColor="content-neutral-05"
          >
            {ConsultarSimulacao.LABEL_SELECT_RENDA_VITALICIA.data_nascimento}
          </ConsultarSimulacao.Text>
          <ConsultarSimulacao.InputText
            name="dthNascimentoConjuge"
            arialabel="label"
            placeholder="Digite o prazo"
            size="standard"
            type="date"
            variant="box-classic"
            value={formik.getFieldProps('dthNascimentoConjuge').value}
            onChange={e =>
              formik.setFieldValue('dthNascimentoConjuge', e.target.value)
            }
            error={!!formik.errors.dthNascimentoConjuge}
          />
          <ConsultarSimulacao.Match when={!!formik.errors.dthNascimentoConjuge}>
            <ConsultarSimulacao.Text
              fontColor="content-danger-01"
              variant="text-small-400"
            >
              {ConsultarSimulacao.CAMPO_OBRIGATORIO}
            </ConsultarSimulacao.Text>
          </ConsultarSimulacao.Match>
        </span>
      </div>
      <ConsultarSimulacao.Button type="submit" variant="secondary">
        Prosseguir
      </ConsultarSimulacao.Button>
    </ConsultarSimulacao.ContainerRendaVitaliciaReversivelBeneficiario>
  );
};
