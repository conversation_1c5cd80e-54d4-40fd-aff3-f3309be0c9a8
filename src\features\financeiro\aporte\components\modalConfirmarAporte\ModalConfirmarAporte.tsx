import {
  BOTOES,
  But<PERSON>,
  ButtonContainer,
  checkIfAllItemsAreTrue,
  CONFIRMAR_APORTE,
  Dialog,
  EEtapasAporte,
  getTernaryResult,
  LoadingSpinner,
  Text,
  useModalConfirmarAporte,
} from '@src/features/financeiro/aporte/exports';

const ModalConfirmarAporte: React.FC = () => {
  const {
    etapa,
    efetuarAporteLoading,
    certificadoAtivo,
    assinatura,
    handleProximaEtapa,
    handleEtapaAnterior,
  } = useModalConfirmarAporte();

  return (
    <Dialog open={etapa === EEtapasAporte.ModalConfirmarAporte}>
      <Dialog.Content>
        <Dialog.Body>
          <Text
            variant="text-big-400"
            fontColor="content-neutral-04"
            textAlign="center"
          >
            {CONFIRMAR_APORTE.confirmacao}
          </Text>
          <Text
            variant="text-big-400"
            fontColor="content-neutral-04"
            textAlign="center"
          >
            {`${certificadoAtivo.produto.descricaoProduto.toUpperCase()}?`}
          </Text>
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.Cancel asChild>
            <Button
              onClick={handleEtapaAnterior}
              size="standard"
              variant="secondary"
              disabled={efetuarAporteLoading}
            >
              <ButtonContainer>{BOTOES.voltar}</ButtonContainer>
            </Button>
          </Dialog.Cancel>

          <Dialog.Action asChild>
            <Button
              onClick={handleProximaEtapa}
              size="standard"
              variant="primary"
              disabled={checkIfAllItemsAreTrue([
                efetuarAporteLoading,
                !assinatura,
              ])}
            >
              <ButtonContainer>
                {getTernaryResult(
                  efetuarAporteLoading,
                  <LoadingSpinner />,
                  <p>{BOTOES.confirmar}</p>,
                )}
              </ButtonContainer>
            </Button>
          </Dialog.Action>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};

export default ModalConfirmarAporte;
