import {
  Button,
  FooterConsulta,
  IFooterSimulacaoDeRendaProps,
  Match,
  RENDA_SIMULADA_ABAIXO_DO_PREVISTO,
  Text,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const FooterSimulacaoDeRenda: React.FC<IFooterSimulacaoDeRendaProps> = ({
  typeRenda,
  handleClearValuesData,
  handleVerUltimasSimulacoes,
  handleClearUltimasSolicitacoes,
  dataUltimasSimulacoes,
  data,
}) => {
  const dataSimulation = data.entidade;

  return (
    <FooterConsulta>
      <div>
        <Match
          when={
            dataSimulation?.codTipoPagamento ===
            dataSimulation?.codTipoPagamentoOriginal
          }
        >
          <Text variant="text-huge-400" fontColor="brand-primary-05">
            Renda contratada <br /> {typeRenda}
          </Text>
        </Match>
        <Match when={!!dataSimulation?.descMensagemError}>
          <span>
            <Text variant="text-standard-700">
              {RENDA_SIMULADA_ABAIXO_DO_PREVISTO}
              <br /> {dataSimulation?.descMensagemError}
            </Text>
          </span>
        </Match>
      </div>
      <div>
        <Match when={!dataUltimasSimulacoes.length}>
          <Button
            variant="secondary"
            onClick={() => handleVerUltimasSimulacoes()}
          >
            Ver últimas Simulações
          </Button>
        </Match>
        <span>
          <Button
            variant="primary"
            onClick={() => {
              handleClearValuesData();
              handleClearUltimasSolicitacoes();
            }}
          >
            Nova Simulação
          </Button>
        </span>
      </div>
    </FooterConsulta>
  );
};
