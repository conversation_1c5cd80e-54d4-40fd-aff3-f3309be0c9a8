import LayoutConsultasRoutes from '@src/corporativo/routes/constants/LayoutConsultasRoutes';
import LayoutFinanceiroRoutes from '@src/corporativo/routes/constants/LayoutFinanceiroRoutes';
import Beneficiarios from '@src/features/beneficiarios/pages/Beneficiarios';
import HistoricoSinistro from '@src/features/consultaSinistro/pages/SinistroHistorico';
import DadosParticipante from '@src/features/dadosParticipante/pages/DadosParticipante';
import DadosPlanoPage from '@src/features/dadosPlano/pages/DadosPlanoPage';
import Aporte from '@src/features/financeiro/aporte/pages/Aporte';
import DadosPagamentos from '@src/features/financeiro/dadosPagamento/pages/DadosPagamentos';
import Resgate from '@src/features/financeiro/resgate/pages/Resgate';
import { Transferencia } from '@src/features/financeiro/transferencias/pages/Transferencia';
import AtivacaoSuspensaoContribuicao from '@src/features/financeiro/ativacaoSuspensaoContribuicao/pages/AtivacaoSuspensaoContribuicao';
import { ImprimirDocumento } from '@src/features/imprimirDocumento/exports';
import LayoutPrevidencia from '@src/features/layoutPrevidencia/pages/LayoutPrevidencia';
import { LayoutFuncionalidadesAgrupadas } from '@src/shared/components/LayoutFuncionalidadesAgrupadas/LayoutFuncionalidadesAgrupadas';
import { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';
import { ROTAS_CONSULTAS } from '@src/corporativo/routes/constants/RotasConsulta';

export const ROUTE_LIST = [
  {
    id: 'Previdência',
    path: '/seguridade/previdencia/pos-venda',
    element: <LayoutPrevidencia />,
    children: [
      {
        index: true,
        path: 'dados-plano',
        id: 'Dados do plano',
        element: <DadosPlanoPage />,
      },
      {
        path: 'dados-participante',
        id: 'Dados do Participante',
        element: <DadosParticipante />,
      },
      {
        path: 'beneficiarios',
        id: 'Beneficiários',
        element: <Beneficiarios />,
      },
      {
        path: 'financeiro',
        id: 'Financeiro',
        element: (
          <LayoutFuncionalidadesAgrupadas
            routesLayout={LayoutFinanceiroRoutes}
          />
        ),
        children: [
          {
            id: 'Dados do Pagamento',
            path: 'dados-do-pagamento',
            element: <DadosPagamentos />,
          },
          {
            id: 'Transferências',
            path: 'transferencias',
            element: <Transferencia />,
          },
          {
            id: 'Aporte',
            path: 'aporte',
            element: <Aporte />,
          },
          {
            id: 'Resgate',
            path: 'resgate',
            element: <Resgate />,
          },
          {
            id: 'Ativação/Suspensão de Contribuição',
            path: 'ativacao-suspensao-contribuicao',
            element: <AtivacaoSuspensaoContribuicao />,
          },
        ],
      },
      {
        path: 'consulta',
        id: 'Consulta',
        element: (
          <LayoutFuncionalidadesAgrupadas
            routesLayout={LayoutConsultasRoutes}
          />
        ),
        children: ROTAS_CONSULTAS,
      },
    ],
  },
  {
    id: 'Histórico de sinistros',
    path: ROUTES.SINISTROS,
    element: <HistoricoSinistro />,
  },
  {
    id: 'Impressão de documentos',
    path: ROUTES.IMPRIMIR,
    element: <ImprimirDocumento />,
  },
];
