import {
  TIPOS_CANAL_PAGAMENTO,
  tryGetValueOrDefault,
  IConsultarTiposPagamentoTipo,
} from '@src/features/financeiro/resgate/exports';

/**
 * Filtra contas bancárias válidas para operações de resgate
 *
 * Esta função filtra os tipos de pagamento para incluir apenas aqueles
 * cujo canal seja do tipo transferência para resgate.
 *
 * @param {IConsultarTiposPagamentoTipo[] | undefined} tiposPagamento - Lista de tipos de pagamento disponíveis
 * @returns {IConsultarTiposPagamentoTipo[]} Lista filtrada contendo apenas contas válidas para resgate
 */
export const filtrarContasValidasParaResgate = (
  tiposPagamento: IConsultarTiposPagamentoTipo[] | undefined,
): IConsultarTiposPagamentoTipo[] => {
  const contasValidas = tiposPagamento?.filter(
    ({ tipoCanal }) =>
      tipoCanal === TIPOS_CANAL_PAGAMENTO.RESGATE_TRANSFERENCIA,
  );

  return tryGetValueOrDefault([contasValidas], []);
};
