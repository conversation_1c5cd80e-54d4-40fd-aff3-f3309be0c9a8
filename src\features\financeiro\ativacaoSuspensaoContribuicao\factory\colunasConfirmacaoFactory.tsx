import * as AtivacaoSuspensao from '../exports';
import { IDadosCertificado } from './dadosTabelaCertificadoFactory';
import { IDadosCuidadoExtra } from './dadosTabelaCuidadoExtraFactory';

type TDadosConfirmacao = IDadosCertificado | IDadosCuidadoExtra;

export const colunasConfirmacaoFactory =
  (): AtivacaoSuspensao.TableColumn<TDadosConfirmacao>[] => [
    {
      name: '',
      selector: (row: TDadosConfirmacao) => row.campo,
      cell: (row: TDadosConfirmacao) => (
        <AtivacaoSuspensao.Text variant="text-standard-600">
          {row.campo}
        </AtivacaoSuspensao.Text>
      ),
      sortable: false,
      width: '50%',
    },
    {
      name: '',
      selector: (row: TDadosConfirmacao) => row.valor,
      cell: (row: TDadosConfirmacao) => (
        <AtivacaoSuspensao.Text variant="text-standard-400">
          {row.valor}
        </AtivacaoSuspensao.Text>
      ),
      sortable: false,
      width: '50%',
    },
  ];
