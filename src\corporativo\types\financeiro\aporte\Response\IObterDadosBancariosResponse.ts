export interface IObterDadosBancariosResponse {
  msgErroExcessao: string;
  dados: IDadosBancariosAporte[];
}

export interface IDadosBancariosAporte {
  tipoPagamentoId: string;
  descricaoPagamento: string;
  canalId: string;
  dataPagamento?: string;
  metodoPagamento: string;
  tipoContaBanco: string;
  numeroBanco: string;
  nomeBanco: string;
  numeroAgencia: string;
  digitoAgencia: string;
  numeroConta: string;
  digitoConta: string;
  operacao: string;
  banco: string;
  conta: string;
}
