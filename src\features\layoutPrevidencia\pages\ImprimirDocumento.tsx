import { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { GerarDocumento } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';

const ImprimirDocumento: React.FC = () => {
  const navigate = useNavigate();
  const { impressao } = useContext(PrevidenciaContext);

  const voltar = () => {
    navigate(-1);
  };

  return (
    <GerarDocumento
      titulo="Previdência"
      tipoDocumento={impressao.tipoDocumento}
      base64File={tryGetValueOrDefault([impressao.base64], [])}
      onVoltar={voltar}
      enviandoEmail={false}
    />
  );
};

export default ImprimirDocumento;
