import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { useDadosPlano } from '@src/corporativo/infra/dadosPlano/useDadosPlano';
import {
  IUseConsultarRegimeTributarioReturn,
  IUseObterConsultarRegimeTributario,
  IUseObterConsultarRegimeTributarioResponse,
} from '@src/corporativo/types/consulta/ISimularRenda';
import { useContext } from 'react';

const useConsultarRegimeTributario =
  (): IUseConsultarRegimeTributarioReturn => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);
    const { loading, response } = useApiGatewayCvpInvoker<
      IUseObterConsultarRegimeTributario,
      IUseObterConsultarRegimeTributarioResponse
    >(PECOS.ConsultarParametrosRegimeTributario, {
      autoFetch: true,
      data: {
        Cpf: String(getSessionItem('cpfCnpj')),
        NumeroCertificado: certificadoAtivo?.certificadoNumero,
        numeroConta: certificadoAtivo?.certificadoNumero,
      },
    });
    const { response: responseDadosPlano } = useDadosPlano({
      cpf: String(getSessionItem('cpfCnpj')),
      NumeroCertificado: certificadoAtivo?.certificadoNumero,
    });

    const regimeTributario = response?.entidade;

    const podeAlterarRegimeTributario =
      regimeTributario?.dados?.podeAlterarRegimeTributario ?? '';

    const tipoRegime = `${certificadoAtivo?.produto?.modalidade.toUpperCase()}/${responseDadosPlano?.return?.tributacao.toLowerCase()}`;

    return { loading, podeAlterarRegimeTributario, tipoRegime };
  };

export default useConsultarRegimeTributario;
