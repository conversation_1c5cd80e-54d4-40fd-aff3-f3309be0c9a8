import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Calcula o valor total dos resgates solicitados e o valor restante disponível após o resgate,
 * com base nos dados do formulário do Formik.
 *
 * A função considera apenas os fundos selecionados e com valores preenchidos.
 *
 * @param formik - Instância do Formik com os valores atuais do formulário.
 * @param saldoTotal - Valor total disponível para resgate.
 *
 * @returns Um objeto contendo:
 * - `calculoTotal`: soma dos valores informados nos fundos selecionados.
 * - `calculoRestante`: diferença entre o saldo total e o valor total de retirada.
 */
export const calcularValorResgateRestante = (
  formik: Resgate.FormikProps<Resgate.IFormikValuesSimulacaoResgate>,
  saldoTotal: number | undefined,
): Resgate.ICalcularValorResgateRestanteRetorno => {
  const saldoTotalResgate: number = Resgate.tryGetValueOrDefault(
    [saldoTotal],
    0,
  );

  const valorRetirarSomado: number = formik.values.fundosParaResgate
    .filter(fundo =>
      Resgate.checkIfAllItemsAreTrue([
        !!fundo.selecionado,
        !!fundo.valorRetirar,
      ]),
    )
    .reduce((acumulador, fundo) => {
      return acumulador + Number(fundo.valorRetirar);
    }, 0);

  const calculoTotal: number = +valorRetirarSomado.toFixed(2);

  const calculoRestante: number =
    saldoTotalResgate - Resgate.tryGetValueOrDefault([calculoTotal], 0);

  return {
    calculoRestante,
    calculoTotal,
  };
};
