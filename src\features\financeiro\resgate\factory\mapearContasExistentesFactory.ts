import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Mapear contas bancárias existentes para o formato do select
 *
 * @param {IConsultarTiposPagamentoTipo[]} contasBancariasValidas - Lista de contas bancárias válidas para resgate
 * @returns {IMapearContasExistentesFactoryReturn[]} Array contendo todas as contas existentes formatadas para exibição no componente Select,
 * incluindo a opção de nova conta no final da lista
 */
export const mapearContasExistentesFactory = (
  contasBancariasValidas: Resgate.IConsultarTiposPagamentoTipo[],
): Resgate.IMapearContasExistentesFactoryReturn[] => {
  const contasExistentes = contasBancariasValidas.map(conta => ({
    value: conta.codigoContaBancaria,
    text: Resgate.formatarDescricaoConta({
      numeroAgencia: conta.numeroAgencia,
      codigoContaBancaria: conta.codigoContaBancaria,
      descricaoTipoContaBancaria: conta.descricaoTipoContaBancaria,
      descritivoContaBancaria: Resgate.obterDescritivoContaBancaria(conta),
    }),
  }));

  return [...contasExistentes, Resgate.NOVA_CONTA];
};
