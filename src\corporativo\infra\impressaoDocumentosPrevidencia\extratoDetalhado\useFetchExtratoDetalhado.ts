import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IUseFetchExtratoDetalhadoPayload,
  IUseFetchExtratoDetalhadoReturn,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchExtratoDetalhadoPayload';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const useFetchExtratoDetalhado = (
  payload?: IUseFetchExtratoDetalhadoPayload,
): IUseFetchExtratoDetalhadoReturn => {
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    IUseFetchExtratoDetalhadoPayload,
    { return: string }
  >(PECOS.ObterExtratoPdf, {
    data: payload,
    autoFetch: false,
  });

  return {
    isLoadingExtratoDetalhado: loading,
    fetchExtratoDetalhado: invocarApiGatewayCvpComToken,
  };
};

export default useFetchExtratoDetalhado;
