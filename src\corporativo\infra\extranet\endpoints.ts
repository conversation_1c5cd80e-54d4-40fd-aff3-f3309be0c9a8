import { accessPdf } from '@src/corporativo/utils/accessPdF';

export const baseURL = 'https://statics.caixavidaeprevidencia.com.br/extranet';

export const graficosFundosInvestimentoImgURL = `${baseURL}/Previdencia/Fundos_invest_prev/grafico_composicao_carteiras.jpg`;

export const relatorioGestaoPdfUrl = `${baseURL}/Previdencia/Fundos_invest_prev/Relatorio_Gestao.pdf`;

export const carteirasDiariasCVPPdfUrl = `${baseURL}/Previdencia/Fundos_invest_prev/Carteiras_Diarias_CVP.pdf`;

const portfolioNovosFundosPdfUrl = `${baseURL}/Previdencia/Fundos_invest_prev/Portfolio_Novos_Fundos.pdf`;

const fundosPJPdfUrl = `${baseURL}/Previdencia/Fundos_invest_prev/fundos_PJ.pdf`;

export const accessPortfolioNovosFundosPdf: () => void = () =>
  accessPdf(portfolioNovosFundosPdfUrl);

export const accessFundosPJPdf: () => void = () => accessPdf(fundosPJPdfUrl);
