import * as DadosParticipante from '../exports';

export interface IControlesLGPD {
  dadosPessoaisOutrosProdutos: boolean;
  dadosPessoaisParceiros: boolean;
}

export const FormDadosParticipante: React.FC<{
  obterDadosParticipante: VoidFunction;
}> = ({ obterDadosParticipante }) => {
  const {
    dadosParticipante,
    lgpdControls,
    setLgpdControls,
    editando,
    atualizarDadosParticipante,
  } = DadosParticipante.useFormDadosParticipante(obterDadosParticipante);

  return (
    <DadosParticipante.Formik
      initialValues={{
        cep: dadosParticipante.cep,
        endereco: dadosParticipante.endereco,
        numero: dadosParticipante.numero,
        complemento: dadosParticipante.complemento,
        bairro: dadosParticipante.bairro,
        cidade: dadosParticipante.cidade,
        uf: dadosParticipante.uf,
        email: dadosParticipante.email,
        celular: dadosParticipante.celular,
        telefone: dadosParticipante.telefone,
        nomeSocial: dadosParticipante.nomeSocial,
      }}
      onSubmit={atualizarDadosParticipante}
      validateOnBlur
      validationSchema={DadosParticipante.dadosParticipanteValidationSchema}
    >
      <DadosParticipante.S.Container>
        <DadosParticipante.Form>
          <div>
            <DadosParticipante.CamposEditaveis
              dadosParticipante={dadosParticipante}
              editando={editando}
            />
            <DadosParticipante.ConsentimentoDadosParticipante
              editando={editando}
              lgpdControls={lgpdControls}
              setLgpdControls={setLgpdControls}
            />
          </div>
        </DadosParticipante.Form>
        <DadosParticipante.ConditionalRenderer condition={editando}>
          <DadosParticipante.AssinaturaDadosParticipante />
        </DadosParticipante.ConditionalRenderer>
        <DadosParticipante.AlertaPrevidencia identificador="dados-participante-envio" />
        <DadosParticipante.ControlesDadosParticipante />
      </DadosParticipante.S.Container>
    </DadosParticipante.Formik>
  );
};
