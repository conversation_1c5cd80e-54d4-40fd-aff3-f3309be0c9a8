import * as SimularRendaPrazoCerto from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const SimulacaoDeRendaTemporariaPrazoCerto: React.FC<
  SimularRendaPrazoCerto.ISimulacaoDeRendaTemporariaPrazoCerto
> = ({ consultarRenda, income }) => {
  const formik = SimularRendaPrazoCerto.useFormik({
    initialValues: {
      ...SimularRendaPrazoCerto.INITIAL_VALUES_FORMIK_RENDA_VITALICIA,
    },
    validationSchema: SimularRendaPrazoCerto.validarPrazoSchema(income.renda),
    onSubmit: consultarRenda,
  });

  return (
    <SimularRendaPrazoCerto.ContainerSimulacaoTemporaria
      onSubmit={formik.handleSubmit}
    >
      <SimularRendaPrazoCerto.GridItem xs="1 / 3" margin="0">
        <SimularRendaPrazoCerto.Text
          variant="text-standard-600"
          fontColor="content-neutral-05"
        >
          Por quanto tempo deseja receber a renda
        </SimularRendaPrazoCerto.Text>
        <SimularRendaPrazoCerto.InputText
          arialabel="Aria Label"
          placeholder="Digite o prazo"
          size="standard"
          type="text"
          variant="box-classic"
          name="qtdPrazoAnos"
          error={!!formik.errors.qtdPrazoAnos}
          value={formik.getFieldProps('qtdPrazoAnos').value}
          onChange={e => {
            formik.setFieldValue('qtdPrazoAnos', e.target.value);
          }}
        />

        <SimularRendaPrazoCerto.Match when={!formik.errors.qtdPrazoAnos}>
          <SimularRendaPrazoCerto.Text
            variant="text-small-400"
            fontColor="content-neutral-05"
          >
            {
              SimularRendaPrazoCerto.textSmulationRendaTemporariaPrazoCerto[
                income.renda
              ]
            }
          </SimularRendaPrazoCerto.Text>
        </SimularRendaPrazoCerto.Match>
        <SimularRendaPrazoCerto.Match when={!!formik.errors.qtdPrazoAnos}>
          <SimularRendaPrazoCerto.Text
            fontColor="content-danger-01"
            variant="text-small-400"
          >
            Campo obrigatório
          </SimularRendaPrazoCerto.Text>
        </SimularRendaPrazoCerto.Match>
      </SimularRendaPrazoCerto.GridItem>
      <span>
        <SimularRendaPrazoCerto.Button type="submit" variant="secondary">
          Prosseguir
        </SimularRendaPrazoCerto.Button>
      </span>
    </SimularRendaPrazoCerto.ContainerSimulacaoTemporaria>
  );
};
