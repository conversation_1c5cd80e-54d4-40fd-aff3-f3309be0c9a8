import * as DadosParticipante from '../exports';

const DadosParticipantePage: React.FC = () => {
  const {
    loading,
    response: dadosParticipante,
    obterDadosParticipante,
  } = DadosParticipante.useObterDadosDoParticipante();

  const { nomeSocial } = DadosParticipante.React.useContext(
    DadosParticipante.PrevidenciaContext,
  );

  return (
    <DadosParticipante.ConditionalRenderer
      condition={!loading}
      fallback={
        <DadosParticipante.Grid
          margin="32px 0"
          justify="center"
          alignitem="center"
        >
          <DadosParticipante.LoadingSpinner size="large">
            Carregando...
          </DadosParticipante.LoadingSpinner>
        </DadosParticipante.Grid>
      }
    >
      <DadosParticipante.ConditionalRenderer
        condition={!!dadosParticipante.numCpf}
      >
        <DadosParticipante.DadosParticipanteProvider
          dadosParticipante={{
            ...dadosParticipante,
            nomeSocial,
          }}
        >
          <DadosParticipante.AlertaPrevidencia identificador="dados-participante" />
          <DadosParticipante.FormDadosParticipante
            obterDadosParticipante={obterDadosParticipante}
          />
        </DadosParticipante.DadosParticipanteProvider>
      </DadosParticipante.ConditionalRenderer>
    </DadosParticipante.ConditionalRenderer>
  );
};

export default DadosParticipantePage;
