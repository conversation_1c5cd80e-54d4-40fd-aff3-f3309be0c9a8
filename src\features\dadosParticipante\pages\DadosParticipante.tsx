import * as DadosParticipanteExport from '@src/features/dadosParticipante/exports';

const DadosParticipante: React.FC = () => {
  const {
    loading,
    response: dadosParticipante,
    obterDadosParticipante,
  } = DadosParticipanteExport.useObterDadosDoParticipante();

  const { nomeSocial } = DadosParticipanteExport.React.useContext(
    DadosParticipanteExport.PrevidenciaContext,
  );

  return (
    <DadosParticipanteExport.ConditionalRenderer
      condition={!loading}
      fallback={
        <DadosParticipanteExport.Grid
          margin="32px 0"
          justify="center"
          alignitem="center"
        >
          <DadosParticipanteExport.LoadingSpinner size="large">
            Carregando...
          </DadosParticipanteExport.LoadingSpinner>
        </DadosParticipanteExport.Grid>
      }
    >
      <DadosParticipanteExport.ConditionalRenderer
        condition={!!dadosParticipante.numCpf}
      >
        <DadosParticipanteExport.DadosParticipanteProvider
          dadosParticipante={{
            ...dadosParticipante,
            nomeSocial,
          }}
        >
          <DadosParticipanteExport.AlertaPrevidencia identificador="dados-participante" />
          <DadosParticipanteExport.FormDadosParticipante
            obterDadosParticipante={obterDadosParticipante}
          />
        </DadosParticipanteExport.DadosParticipanteProvider>
      </DadosParticipanteExport.ConditionalRenderer>
    </DadosParticipanteExport.ConditionalRenderer>
  );
};

export default DadosParticipante;
