import { useServiceAlterarDadosPagamento } from '@src/features/financeiro/dadosPagamento/hook/useServiceAlterarDadosPagamento';
import React, { createContext } from 'react';

type TAlterarDadosPagamentoContext = ReturnType<
  typeof useServiceAlterarDadosPagamento
>;

export const AlterarDadosPagamentoContext = createContext(
  {} as TAlterarDadosPagamentoContext,
);

export const AlterarDadosPagamentoProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const dadosPagamento = useServiceAlterarDadosPagamento();

  return (
    <AlterarDadosPagamentoContext.Provider value={dadosPagamento}>
      {children}
    </AlterarDadosPagamentoContext.Provider>
  );
};
