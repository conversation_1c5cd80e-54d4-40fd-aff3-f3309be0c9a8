import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Montar o resumo dos dados da alíquota de resgate
 *
 * Esta função constrói uma lista resumida com as principais informações financeiras
 * da simulação de resgate para exibição na interface, como saldo, valores brutos e líquidos,
 * taxas e impostos.
 *
 * @param {Object} params - Parâmetros para montagem do resumo
 * @param {string} params.tipoAliquota - Tipo de alíquota (progressiva ou regressiva)
 * @param {Object} params.dadosSelecaoAliquota - Dados da seleção de alíquota
 * @param {Object} params.saldo - Informações de saldo do cliente
 * @returns {IMontarResumoAliquotaFactoryRetorno[]} Lista estruturada com o resumo financeiro
 * formatado para exibição na interface
 */
export const montarResumoAliquotaFactory = ({
  tipoAliquota,
  dadosSelecaoAliquota,
  saldo,
}: Resgate.IMontarResumoAliquotaFactory): Resgate.IMontarResumoAliquotaFactoryRetorno[] => {
  const isAliquotaProgressiva: boolean =
    tipoAliquota === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO;

  const { detalhado }: Resgate.IConsultarDetalhesDaAliquotaFactoryRetorno =
    Resgate.mapearDadosPorAliquotaFactory(
      isAliquotaProgressiva,
      dadosSelecaoAliquota,
    );

  const dataFormatada: string = Resgate.dateTimeFormat(new Date());

  return [
    {
      label: Resgate.RESUMO_ALIQUOTA_LABELS.SALDO_EM(dataFormatada),
      value: saldo?.saldoTotal?.toString(),
      mask: Resgate.RESUMO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.RESUMO_ALIQUOTA_LABELS.VALOR_BRUTO_RESGATE,
      value: detalhado?.totalValorSolicitado,
      mask: Resgate.RESUMO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.RESUMO_ALIQUOTA_LABELS.VALOR_IR,
      value: detalhado?.totalValorIrrf,
      mask: Resgate.RESUMO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.RESUMO_ALIQUOTA_LABELS.TAXA_CARREGAMENTO_SAIDA,
      value: detalhado?.totalCarregamentoSaida,
      mask: Resgate.RESUMO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.RESUMO_ALIQUOTA_LABELS.VALOR_LIQUIDO,
      value: detalhado?.totalValorLiquido,
      mask: Resgate.RESUMO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.RESUMO_ALIQUOTA_LABELS.SAIDA_APOS_RESGATE,
      value: detalhado?.totalSaldoPrincipal,
      mask: Resgate.RESUMO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
  ];
};
