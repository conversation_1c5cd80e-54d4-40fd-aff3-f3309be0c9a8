import { useContext, useEffect, useMemo, useState } from 'react';
import { checkIfSomeItemsAreTrue } from '@cvp/utils';
import {
  useConsultaFundosPorCertificado,
  useConsultaFundosTransferenciaDestino,
  useRevalidarTransferencia,
  useDefineReservaDestino,
  useConfirmarTransferencia,
} from '@src/corporativo/infra/financeiro/transferenciaEntreFundos';
import * as CONSTANTES from '@src/corporativo/constants/transferencias/constants';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { TransferenciaServicosContext } from '@src/corporativo/context/financeiro/transferencias/TransferenciaServicosContext';

type TTransferenciaServicosProvider = {
  children: React.ReactNode;
};

export const TransferenciaServicosProvider = ({
  children,
}: TTransferenciaServicosProvider) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const consultaPorCertificado = useConsultaFundosPorCertificado();
  const defineReservaDestino = useDefineReservaDestino();
  const revalidarTransferencia = useRevalidarTransferencia();
  const confirmarTransferencia = useConfirmarTransferencia();
  const consultaDestino = useConsultaFundosTransferenciaDestino();
  const [mensagemErro, setMensagemErro] = useState('');

  useEffect(() => {
    if (certificadoAtivo)
      consultaPorCertificado.fetchData(certificadoAtivo.certificadoNumero);
  }, []);

  const obterMensagemDeCarregamento = (): string | undefined => {
    const carregando = [
      {
        estaCarregando: checkIfSomeItemsAreTrue([
          consultaDestino.loading,
          consultaPorCertificado.loading,
        ]),
        mensagem: CONSTANTES.CARREGANDO.CONSULTAS,
      },
      {
        estaCarregando: defineReservaDestino.loading,
        mensagem: CONSTANTES.CARREGANDO.DEFINIR_DESTINOS,
      },
      {
        estaCarregando: revalidarTransferencia.loading,
        mensagem: CONSTANTES.CARREGANDO.REVALIDAR_TRANSFERENCIA,
      },
      {
        estaCarregando: confirmarTransferencia.loading,
        mensagem: CONSTANTES.CARREGANDO.CONFIRMAR_TRANSFERENCIA,
      },
    ];

    const itemCarregando = carregando.find(item => item.estaCarregando);

    return itemCarregando?.mensagem;
  };

  const memoizedValues = useMemo(
    () => ({
      consultaOrigem: consultaPorCertificado,
      consultaDestino,
      defineReservaDestino,
      revalidarTransferencia,
      confirmarTransferencia,
      mensagemErro,
      setMensagemErro,
      carregando: obterMensagemDeCarregamento(),
    }),
    [
      mensagemErro,
      consultaPorCertificado,
      consultaDestino,
      defineReservaDestino,
      revalidarTransferencia,
      confirmarTransferencia,
    ],
  );

  return (
    <TransferenciaServicosContext.Provider value={memoizedValues}>
      {children}
    </TransferenciaServicosContext.Provider>
  );
};
