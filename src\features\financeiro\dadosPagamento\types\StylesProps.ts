export interface IDisplayProps {
  $alignContent?:
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'stretch';
  $alignItems?: 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'baseline';
  $flexDirection?: 'row' | 'row-reverse' | 'column' | 'column-reverse';
  $flexFlow?: 'row nowrap' | 'row wrap' | 'column nowrap' | 'column wrap';
  $flexWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  $justifyContent?:
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'space-evenly';
  $gap?: string;
}
