import { useState } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  checkIfAllItemsAreTrue,
  getTernaryResult,
  tryGetValueOrDefault,
} from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { RESULTADO_ERRO_DEFAULT } from '@src/shared/constants/modalEnvioEmail';
import {
  IIUseEnviarEmailResult,
  IUseEnviarEmailProps,
  IUseEnviarEmailRetorno,
  TPayloadEnviarEmail,
  TResponseEnviarEmail,
} from '@src/shared/types/IUseEnviarEmail';

export const useEnviarEmail = ({
  parametrosEnvio,
  tipoEmail,
}: IUseEnviarEmailProps): IUseEnviarEmailRetorno => {
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<IIUseEnviarEmailResult | undefined>();

  const { invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    TPayloadEnviarEmail,
    TResponseEnviarEmail
  >(PECOS.EnviarEmailPrevidencia, {
    autoFetch: false,
  });

  const enviarEmail = async (
    email: string,
  ): Promise<IIUseEnviarEmailResult> => {
    setLoading(true);

    const payloadWithMail = {
      parametrosEnvio,
      tipoEmail,
      enderecoEmail: email,
    };

    const dados = await invocarApiGatewayCvpComToken(payloadWithMail);

    const mensagem = dados?.mensagens?.[0];

    const resultadoBemSucedido: IIUseEnviarEmailResult = {
      sucesso: checkIfAllItemsAreTrue([
        !!dados?.sucessoBFF,
        !!dados?.sucessoGI,
      ]),
      codigo: tryGetValueOrDefault([mensagem?.codigo], ''),
      mensagem: tryGetValueOrDefault([mensagem?.descricao], ''),
    };

    const resultado: IIUseEnviarEmailResult = getTernaryResult(
      !!mensagem,
      resultadoBemSucedido,
      RESULTADO_ERRO_DEFAULT,
    );

    setResult(resultado);
    setLoading(false);

    return resultado;
  };

  return {
    loading,
    result,
    enviarEmail,
  };
};
