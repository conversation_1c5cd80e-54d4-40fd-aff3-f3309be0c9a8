import { TTipoEmail } from '@src/corporativo/infra/email/tipoEmail';

export interface IUseEnviarEmailProps {
  parametrosEnvio: unknown;
  tipoEmail: TTipoEmail;
}

export interface IIUseEnviarEmailResult {
  sucesso: boolean;
  codigo: string;
  mensagem: string;
}

export interface IUseEnviarEmailRetorno {
  loading: boolean;
  result?: IIUseEnviarEmailResult;
  enviarEmail: (email: string) => Promise<IIUseEnviarEmailResult>;
}

type TBoletoEnvioParams = {
  cpfCnpj: string;
  numeroCertificado: string;
  numeroCobranca: string;
};

export type TPayloadEnviarEmail = {
  parametrosEnvio: TBoletoEnvioParams;
  tipoEmail: string;
  enderecoEmail: string;
};

export type TResponseEnviarEmail = unknown;
