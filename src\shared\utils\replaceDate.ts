/**
 * Retorna a data nesse formato Thu Jul 29 2049 00:00:00 GMT-0300.
 *
 * @example
 * // Exemplo de uso com uma data incompleta
 * const finalVigencia = replaceDate({ data: dataFinal })
 * console.log(finalVigencia); // Thu Jul 29 2049 00:00:00 GMT-0300
 */

export interface IReplaceDate {
  data: string;
}

export function replaceDate({ data }: IReplaceDate): Date {
  return new Date(data?.replace(/(\d+[/])(\d+[/])/, '$2$1'));
}
