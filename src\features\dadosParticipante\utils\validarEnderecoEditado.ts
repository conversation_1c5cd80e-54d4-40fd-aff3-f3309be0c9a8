import { CODIGO_PAIS, IEndereco } from '../exports';

export function validarEnderecoEditado(
  editado: IEndereco,
  original: IEndereco,
): IEndereco | null {
  const diferencas: Partial<IEndereco> = {
    ...original,
    pais: CODIGO_PAIS.brasil,
  };
  let contadorDiferencas = 0;
  const incrementarDiferenca = () => {
    contadorDiferencas += 1;
  };

  Object.keys(original).forEach(keyName => {
    const key = keyName as keyof IEndereco;
    if (editado[key] && original[key] !== editado[key]) {
      diferencas[key] = editado[key];
      incrementarDiferenca();
    }
  });

  return contadorDiferencas > 0 ? (diferencas as IEndereco) : null;
}
