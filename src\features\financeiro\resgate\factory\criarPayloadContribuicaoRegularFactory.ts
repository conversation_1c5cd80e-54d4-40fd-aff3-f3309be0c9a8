import {
  tryGetValueOrDefault,
  IConsultarContribuicaoRegularFundos,
  ICriarPayloadContribuicaoRegularFactoryReturn,
} from '@src/features/financeiro/resgate/exports';

/**
 * Criar o payload da contribuição regular para o resgate
 *
 * @param {IConsultarContribuicaoRegularFundos[]} fundos - Lista de fundos para contribuição regular
 * @param {string | undefined} numeroResgate - Número do resgate consolidado
 * @returns {ICriarPayloadContribuicaoRegularFactoryReturn} Payload formatado para envio à API contendo
 * apenas os fundos com valor de contribuição maior que zero
 */
export const criarPayloadContribuicaoRegularFactory = (
  fundos: IConsultarContribuicaoRegularFundos[] | undefined,
  numeroResgate: string | undefined,
): ICriarPayloadContribuicaoRegularFactoryReturn => {
  const fundosContribuicaoRegular = tryGetValueOrDefault([fundos], []);

  const fundosComValor = fundosContribuicaoRegular
    ?.map(fundo => ({
      codigoFundo: fundo.codigoFundo,
      codigoReserva: fundo.codigoReserva,
      valorContribuicao: tryGetValueOrDefault([fundo.valorContribuicao], 0),
    }))
    .filter(fundo => !!fundo.valorContribuicao);

  return {
    fundos: fundosComValor,
    numeroResgate: tryGetValueOrDefault([numeroResgate], ''),
  };
};
