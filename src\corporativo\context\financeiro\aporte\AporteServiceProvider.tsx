import {
  AporteServiceContext,
  useConsultarDadosPep,
  useEfetuarAporte,
  useMemo,
  useObterComprovante,
  useObterDadosBancarios,
  useObterDatas,
  useObterFundosDistribuicao,
  useValidarCampoAporte,
  useValidarConta,
} from '@src/features/financeiro/aporte/exports';
import { useObterExtratoUnificado } from '@src/shared/infra/financeiro/useObterExtratoUnificado';

interface IAporteServiceProvider {
  children: React.ReactNode;
}

const AporteServiceProvider: React.FC<IAporteServiceProvider> = ({
  children,
}) => {
  const clientePep = useConsultarDadosPep();

  const fundosDistribuicao = useObterFundosDistribuicao();

  const validarCamposAporte = useValidarCampoAporte();

  const efetuarAporte = useEfetuarAporte();

  const obterDadosBancarios = useObterDadosBancarios();

  const obterComprovanteResgate = useObterComprovante();

  const validarConta = useValidarConta();

  const datasDebito = useObterDatas();

  const extratoUnificado = useObterExtratoUnificado();

  const memorizedValues = useMemo(
    () => ({
      clientePep,
      fundosDistribuicao,
      validarCamposAporte,
      efetuarAporte,
      obterDadosBancarios,
      obterComprovanteResgate,
      validarConta,
      datasDebito,
      extratoUnificado,
    }),
    [
      clientePep,
      fundosDistribuicao,
      validarCamposAporte,
      efetuarAporte,
      obterDadosBancarios,
      obterComprovanteResgate,
      validarConta,
      datasDebito,
      extratoUnificado,
    ],
  );
  return (
    <AporteServiceContext.Provider value={memorizedValues}>
      {children}
    </AporteServiceContext.Provider>
  );
};

export default AporteServiceProvider;
