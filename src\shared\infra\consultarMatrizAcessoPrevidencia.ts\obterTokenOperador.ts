import {
  checkIfAllItemsAreTrue,
  getSessionItem,
  setSessionItem,
} from '@cvp/utils';
import { ChavesArmazenamento } from '@src/corporativo/constants/ChavesArmazenamento';
import { IApiResponse } from '@cvp/componentes-posvenda';
import { IMatrizAcessoClienteResponse } from '@src/corporativo/types/shared/consultarMatrizAcessoPrevidencia.ts/IMatrizAcessoClienteResponse';
import { ConfigApiGatewayConstants } from '@src/corporativo/constants/ConfigApiGatewayConstants';
import { obterPayloadMatrizAcesso } from '@src/shared/infra/consultarMatrizAcessoPrevidencia.ts/obterPayloadMatrizAcesso';
import { PECO_MATRIZ } from '@src/corporativo/infra/config/api/endpoints';
import api from '@src/corporativo/infra/config/api/api';

export const obterTokenOperador = async () => {
  const tokenCached = getSessionItem<IMatrizAcessoClienteResponse>(
    ChavesArmazenamento.TOKEN_OPERADOR,
  );

  if (checkIfAllItemsAreTrue([!!tokenCached, !!tokenCached?.tokenOperador])) {
    return tokenCached?.tokenOperador;
  }

  const { SHArsaKey, UserName } = await obterPayloadMatrizAcesso();

  const response = await api.post<IApiResponse<IMatrizAcessoClienteResponse>>(
    `${ConfigApiGatewayConstants.configure.operationPath}${PECO_MATRIZ.ConsultarMatrizAcessoPrevidencia}`,
    { SHArsaKey, UserName, codCliente: '' },
  );

  const token = response.data.dados.entidade;

  setSessionItem(ChavesArmazenamento.TOKEN_OPERADOR, token);

  return token?.tokenOperador ?? '';
};
