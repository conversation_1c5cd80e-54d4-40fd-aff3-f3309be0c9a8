import { TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';
import * as AtivacaoSuspensao from '../exports';

interface IContribuicaoTableRow {
  nome: string;
  valorContribuicao: number;
  ativo: boolean;
  index: number;
}

export const colunasTabelaContribuicoes = (
  onToggleContribuicao: (index: number) => void
): TableColumn<IContribuicaoTableRow>[] => [
  {
    name: 'Contribui<PERSON>',
    selector: ({ nome }: IContribuicaoTableRow) => nome,
    cell: (row: IContribuicaoTableRow) => (
      <AtivacaoSuspensao.Text variant="text-standard-600">
        {row.nome}
      </AtivacaoSuspensao.Text>
    ),
    sortable: false,
    width: '40%',
  },
  {
    name: 'Valor',
    selector: ({ valorContribuicao }: IContribuicaoTableRow) => 
      AtivacaoSuspensao.formatarValorPadraoBrasileiro(valorContribuicao),
    cell: (row: IContribuicaoTableRow) => (
      <AtivacaoSuspensao.Text 
        variant="text-standard-400" 
        fontColor="content-neutral-04"
      >
        {AtivacaoSuspensao.formatarValorPadraoBrasileiro(row.valorContribuicao)}
      </AtivacaoSuspensao.Text>
    ),
    sortable: false,
    width: '30%',
  },
  {
    name: 'Status',
    selector: ({ ativo }: IContribuicaoTableRow) => ativo ? 'Ativo' : 'Inativo',
    cell: (row: IContribuicaoTableRow) => (
      <AtivacaoSuspensao.StatusButton
        $ativo={row.ativo}
        onClick={() => onToggleContribuicao(row.index)}
        variant="primary"
        size="small"
      >
        {row.ativo ? 'Ativo' : 'Inativo'}
      </AtivacaoSuspensao.StatusButton>
    ),
    sortable: false,
    width: '30%',
  },
];
