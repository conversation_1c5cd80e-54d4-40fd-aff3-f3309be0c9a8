import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useSimulacaoResgate = (): Resgate.IUseSimulacaoResgate => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const {
    isLoadingListaFundosParaResgate,
    valorMinimoResgate,
    valorMinimoPermanencia,
    saldoTotal,
  } = Resgate.useResgateFormSetup();

  const fundos = formik.values.fundosParaResgate;

  const isTipoResgateTotal: boolean =
    formik.values.tipoResgate === Resgate.TIPOS_RESGATE.TOTAL.id;

  const isTipoResgateParcial: boolean =
    formik.values.tipoResgate === Resgate.TIPOS_RESGATE.PARCIAL.id;

  const selecionarTipoResgate = (tipoResgateSelecionado: string): void => {
    formik.setValues({
      ...formik.values,
      tipoResgate: tipoResgateSelecionado,
    });
  };

  const atualizarFundosPorTipoResgate = (): void => {
    if (isTipoResgateParcial) {
      formik.setValues({
        ...formik.values,
        fundosParaResgate: Resgate.mapearFundosParaTipoResgateFactory({
          fundos,
          tipoResgate: Resgate.TIPOS_RESGATE.PARCIAL.value.toLowerCase(),
          isTipoTotalSelecionado: false,
        }),
      });
    }

    if (isTipoResgateTotal) {
      formik.setValues({
        ...formik.values,
        fundosParaResgate: Resgate.mapearFundosParaTipoResgateFactory({
          fundos,
          tipoResgate: Resgate.TIPOS_RESGATE.TOTAL.value.toLowerCase(),
          isTipoTotalSelecionado: true,
        }),
      });
    }
  };

  const selecionarFundoParaResgate = (codigoFundo: string): void => {
    formik.setValues({
      ...formik.values,
      fundosParaResgate: Resgate.alterarSelecaoFundoFactory({
        fundos,
        codigoFundo,
      }),
    });
  };

  const alterarValorResgatado = (
    codigoFundo: string,
    valorRetirar: string,
  ): void => {
    formik.setValues({
      ...formik.values,
      fundosParaResgate: Resgate.alterarValorFundoFactory({
        fundos,
        codigoFundo,
        valorRetirar,
      }),
    });
  };

  const resultadoCalculoResgateRestante = Resgate.calcularValorResgateRestante(
    formik,
    saldoTotal,
  );

  const continuarFluxo: boolean = !Resgate.continuarSimulacaoResgate({
    formik,
    saldoTotal,
    valorMinimoPermanencia,
    isTipoResgateTotal,
  });

  const isDisabledBtnSimulacaoResgate: boolean =
    Resgate.checkIfSomeItemsAreTrue([
      isLoadingListaFundosParaResgate,
      continuarFluxo,
    ]);

  const fundosParaResgate: Resgate.IFundosParaResgateFactory[] =
    Resgate.mapearFundosResgateFactory({
      fundos,
      selecionarFundoParaResgate,
      alterarValorResgatado,
      isTipoResgateTotal,
      valorMinimoResgate,
    });

  Resgate.useEffect(() => {
    atualizarFundosPorTipoResgate();
  }, [formik.values.tipoResgate]);

  return {
    isTipoResgateParcial,
    fundosParaResgate,
    resultadoCalculoResgateRestante,
    isDisabledBtnSimulacaoResgate,
    selecionarTipoResgate,
  };
};
