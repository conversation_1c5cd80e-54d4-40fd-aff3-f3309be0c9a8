import {
  MSG_TEMPORARIA,
  IUseErroSolicitacaoResgate,
} from '@src/features/financeiro/resgate/exports';

export const useErroSolicitacaoResgate = (
  mensagens: Record<string, string>,
): IUseErroSolicitacaoResgate => {
  const mensagensErroTemporaria = Object.values(
    MSG_TEMPORARIA.SOLICITACAO_RESGATE,
  );

  const chavesErro: string[] = mensagensErroTemporaria.map(item => item.CHAVE);

  const isErroExistente: boolean = Object.values(mensagens).some(
    mensagem => !!mensagem,
  );

  const mensagemExibida: string | undefined = chavesErro
    .map(chave => mensagens[chave])
    .find(mensagem => !!mensagem);

  return { isErroExistente, mensagemExibida };
};
