import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IRecuperarBancosPayload {
  cpfCnpj: string;
}

export interface IRecuperarBancosResponse {
  codigoBanco: number | string;
  nomeBanco: string;
  nomeAbreviado?: string;
  cgcCpf?: number;
  numeroTitulo?: number;
  convenioCobranca?: string;
  situacaoRegistro?: string;
  codigoEmpresa?: number;
  data?: string;
}

export interface IUseRecuperarBancosRetorno {
  dadosBancarios: IRecuperarBancosResponse[];
  isLoadingDadosBancarios: boolean;
  recuperarBancos: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<IRecuperarBancosResponse[]> | undefined>;
}
