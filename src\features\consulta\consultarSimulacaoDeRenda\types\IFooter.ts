import {
  IHandleReponseResult,
  ISimulacaoDeRendaResponse,
  IUseVerUltimasSolicitacoesResponse,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports';

export interface IFooterSimulacaoDeRendaProps {
  typeRenda: string;
  handleVerUltimasSimulacoes: () => Promise<void>;
  handleClearValuesData: () => void;
  handleClearUltimasSolicitacoes: () => void;
  dataUltimasSimulacoes: IUseVerUltimasSolicitacoesResponse[];

  data: IHandleReponseResult<ISimulacaoDeRendaResponse>;
}
