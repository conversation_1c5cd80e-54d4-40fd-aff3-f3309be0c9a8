import {
  CelulaControleBeneficiarios,
  CelulaTabelaBeneficiario,
  TableHeaderPorcentual,
  TColumn,
  TRowData,
} from '../exports';

export const colunasTabelaBeneficiarios = (
  coberturaId: string,
  removerBeneficiario: (index: number) => void,
): TColumn[] => [
  {
    name: 'Nome',
    selector: ({ nome }: TRowData) => nome,
    cell: (row: TRowData) => (
      <CelulaTabelaBeneficiario
        coberturaId={coberturaId}
        id={row.beneficiarioId}
        tipo="texto"
        name="nomeBeneficiario"
        index={row.index}
        estado={row.estado}
        required
      />
    ),
  },
  {
    name: 'Data de nascimento',
    maxWidth: '200px',
    selector: ({ data }: TRowData) => data,
    cell: (row: TRowData) => (
      <CelulaTabelaBeneficiario
        tipo="data"
        name="dataNascimento"
        index={row.index}
        estado={row.estado}
        id={row.beneficiarioId}
        coberturaId={coberturaId}
      />
    ),
  },
  {
    name: '<PERSON>rentes<PERSON>',
    maxWidth: '240px',
    selector: ({ parentesco }: TRowData) => parentesco,
    cell: (row: TRowData) => (
      <CelulaTabelaBeneficiario
        tipo="select"
        name="idParentesco"
        index={row.index}
        estado={row.estado}
        id={row.beneficiarioId}
        coberturaId={coberturaId}
        required
      />
    ),
  },
  {
    name: <TableHeaderPorcentual coberturaId={coberturaId} />,
    maxWidth: '120px',
    selector: ({ percentual }: TRowData) => percentual,
    cell: (row: TRowData) => (
      <CelulaTabelaBeneficiario
        tipo="porcentagem"
        name="porcentagem"
        index={row.index}
        estado={row.estado}
        id={row.beneficiarioId}
        coberturaId={coberturaId}
        required
      />
    ),
  },
  {
    name: '',
    maxWidth: '150px',
    cell: row => (
      <CelulaControleBeneficiarios
        data={row}
        coberturaId={coberturaId}
        removerBeneficiario={removerBeneficiario}
      />
    ),
  },
];
