import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Obtém a descrição textual do tipo de conta bancária
 *
 * Esta função utiliza o código do banco e o tipo de conta para determinar
 * a descrição textual adequada para o tipo de conta bancária.
 *
 * @param {IConsultarTiposPagamentoTipo} conta - Objeto contendo os dados da conta bancária
 * @returns {string} Descrição textual do tipo de conta bancária ou string vazia se não encontrado
 */
export const obterDescritivoContaBancaria = (
  conta: Resgate.IConsultarTiposPagamentoTipo,
): string => {
  const tipoContaBancaria: Resgate.ITipoContasBancarias[] =
    Resgate.definirTipoContasBancarias(conta.codigoBanco);

  const tipoConta: Resgate.ITipoContasBancarias | undefined =
    tipoContaBancaria.find(
      ({ codigo }) =>
        codigo === Resgate.configurarTipoConta(conta.tipoContaBancaria),
    );

  return Resgate.tryGetValueOrDefault([tipoConta?.descricao], '');
};
