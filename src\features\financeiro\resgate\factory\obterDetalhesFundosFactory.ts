import {
  IListarFundosParaResgateFundosDisponiveis,
  IObterDetalhesFundosFactoryRetorno,
} from '@src/features/financeiro/resgate/exports';

/**
 * Extrair os detalhes essenciais dos fundos para resgate
 *
 * Esta função filtra e mapeia apenas os fundos que possuem valor de resgate definido,
 * extraindo apenas as propriedades necessárias para o processamento do resgate.
 *
 * @param {IListarFundosParaResgateFundosDisponiveis[]} fundosParaResgate - Lista completa de fundos disponíveis para resgate
 * @returns {IObterDetalhesFundosFactoryRetorno[]} Lista filtrada contendo apenas fundos com valores de resgate
 * e apenas as propriedades essenciais para o processamento
 */
export const obterDetalhesFundosFactory = (
  fundosParaResgate: IListarFundosParaResgateFundosDisponiveis[],
): IObterDetalhesFundosFactoryRetorno[] => {
  return fundosParaResgate
    .map(fundo => ({
      codigoFundo: fundo.codigoFundo,
      codigoReserva: fundo.codigoReserva,
      valorResgate: fundo.valorRetirar,
    }))
    .filter(fundoComValor => !!fundoComValor.valorResgate);
};
