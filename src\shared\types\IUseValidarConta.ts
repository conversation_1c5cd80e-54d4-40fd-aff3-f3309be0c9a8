import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IValidarContaPayload {
  cpfCnpj: string;
  codigoAgencia: string;
  codigoOperacao: string;
  digitoVerificador: string;
  numeroBanco: string;
  numeroConta: string;
}

export interface IValidarContaResponse {
  codigoRetorno: string;
  descricaoMensagemAmigavel: string;
  descricaoMensagemSistema: string;
}

export interface IUseValidarContaRetorno {
  responseValidacaoConta:
    | IHandleReponseResult<IValidarContaResponse>
    | undefined;
  setResponse: React.Dispatch<
    React.SetStateAction<
      IHandleReponseResult<IValidarContaResponse> | undefined
    >
  >;

  isLoadingValidacaoConta: boolean;
  validarConta: (
    dynamicPayload?: Partial<IValidarContaPayload>,
  ) => Promise<IHandleReponseResult<IValidarContaResponse> | undefined>;
}
