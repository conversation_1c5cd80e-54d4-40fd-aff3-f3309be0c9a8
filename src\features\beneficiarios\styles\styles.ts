import styled from 'styled-components';
import { But<PERSON>, Dialog } from '../exports';

export const HeaderTooltip = styled.div`
  display: flex;
  align-items: flex-end;
`;

export const ButtonsContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 32px;
`;

export const DeleteButton = styled(Button)`
  border: none;
`;

export const ModalNovoUsuarioBody: React.JSX.Element = styled(Dialog)`
  flex-direction: column;
  gap: 32px;
`;

export const TableControls = styled.div`
  display: flex;
  width: 100%;
  justify-content: flex-end;
  gap: 6px;
`;

export const SaveButton = styled(Button)`
  min-width: 126px;
`;

export const ContainerInputPorcentagem = styled.div`
  display: flex;
  gap: 4px;
  font-size: 1.25rem;
  align-items: center;
`;
