import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import {
  ICertificadoCoberturas,
  ICertificadoCoberturasResponse,
} from '../consultaCertificado/Response/ICertificadoCoberturasResponse';

export type TUseObterBeneficiarios = () => {
  loading: boolean;
  response: ICertificadoCoberturas;
  invocarApiGatewayCvpComToken: () => Promise<
    IHandleReponseResult<ICertificadoCoberturasResponse> | undefined
  >;
};
