import * as Yup from 'yup';

export const coberturaBeneficiariosSchema = Yup.object({
  nomeBeneficiario: Yup.string().required(),
  porcentagem: Yup.string().required(),
  dataNascimento: Yup.string().required(),
  numCpf: Yup.string().required(),
  sexo: Yup.string().required(),
  idBeneficiario: Yup.string().required(),
  idParentesco: Yup.string().required(),
  estado: Yup.string().required(),
});

export type TCoberturaBeneficiariosSchema = Yup.InferType<
  typeof coberturaBeneficiariosSchema
>;
