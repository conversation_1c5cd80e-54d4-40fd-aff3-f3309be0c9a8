import { tryGetValueOrDefault } from '@cvp/utils';
import { TUseObterCoberturasResponseReturn } from '@src/corporativo/types/obterCoberturas/TUseObterCoberturasResponse';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { useContext } from 'react';
import { useObterCoberturas, UTILS } from '../exports';

export interface IObterDados {
  loadingCobertura: boolean;
  responseCobertura: TUseObterCoberturasResponseReturn;
}

export function useGetCoberturas(): IObterDados {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { loading, response } = useObterCoberturas({
    Cpf: UTILS.tryGetValueOrDefault(
      [String(UTILS.getSessionItem('cpfCnpj'))],
      '',
    ),
    NumeroCertificado: UTILS.tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    ),
  });

  return {
    loadingCobertura: loading,
    responseCobertura: tryGetValueOrDefault(
      [response!],
      {} as TUseObterCoberturasResponseReturn,
    ),
  };
}
