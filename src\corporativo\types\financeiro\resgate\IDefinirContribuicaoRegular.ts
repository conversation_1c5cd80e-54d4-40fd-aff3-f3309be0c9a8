import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { ICriarPayloadContribuicaoRegularFactoryReturn } from '@src/corporativo/types/financeiro/resgate/ICriarPayloadContribuicaoRegularFactory';

export interface IDefinirContribuicaoRegularPayload {
  codigoCertificado: string;
  numeroResgate: string;
  fundos: IDefinirContribuicaoRegularFundos[];
}

export interface IDefinirContribuicaoRegularFundos {
  codigoFundo: string;
  codigoReserva: string;
  valorContribuicao: number;
}

export interface IDefinirContribuicaoRegularResponse {
  aviso: string;
}

export interface IUseDefinirContribuicaoRegularReturn {
  dadosContribuicaoRegular: IDefinirContribuicaoRegularResponse;
  isLoadingDefinicaoContribuicaoRegular: boolean;
  definirContribuicaoRegular: (
    dynamicPayload?: ICriarPayloadContribuicaoRegularFactoryReturn,
  ) => Promise<
    IHandleReponseResult<IDefinirContribuicaoRegularResponse> | undefined
  >;
}
