import { GridItem, Text, Utils, React } from '../exports';

interface ICampoTextoDadosParticipanteProps {
  fracao: string;
  label: string;
  text: string | undefined;
}

export const CampoTextoDadosParticipante: React.FC<ICampoTextoDadosParticipanteProps> =
  React.memo(({ fracao, label, text }) => {
    return (
      <GridItem
        style={{
          overflowWrap: 'break-word',
        }}
        xl={fracao}
        lg={fracao}
        md="1/1"
        sm="1/1"
        xs="1/4"
      >
        <Text variant="text-standard-600" marginBottom="12px">
          {label}
        </Text>
        <Text variant="text-standard-400">
          {Utils.tryGetValueOrDefault([text], '')}
        </Text>
      </GridItem>
    );
  });
