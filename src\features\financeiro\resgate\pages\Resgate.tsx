import {
  React,
  ResgateProvider,
  FluxoTelasResgate,
  FormikProvider,
  useResgateFormSetup,
} from '@src/features/financeiro/resgate/exports';

const Resgate: React.FC = () => {
  const { formik } = useResgateFormSetup();

  return (
    <ResgateProvider>
      <FormikProvider value={formik}>
        <FluxoTelasResgate />
      </FormikProvider>
    </ResgateProvider>
  );
};

export default Resgate;
