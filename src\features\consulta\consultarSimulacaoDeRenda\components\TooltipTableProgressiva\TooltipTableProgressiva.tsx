import {
  ALIQUOTA_PROGRESSIVA_TOOLTIP,
  IconInfoOutlined,
  Text,
  ToolTip,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const TooltipTableProgressiva: React.FC = () => {
  return (
    <div>
      {ALIQUOTA_PROGRESSIVA_TOOLTIP.title}
      <ToolTip
        maxWidth="300px"
        text={
          (
            <>
              <Text variant="text-small-600">
                {ALIQUOTA_PROGRESSIVA_TOOLTIP.titleTooltip}
              </Text>
              <Text variant="text-small-600">
                {ALIQUOTA_PROGRESSIVA_TOOLTIP.text}
              </Text>
            </>
          ) as unknown as string
        }
      >
        <IconInfoOutlined size="small" />
      </ToolTip>
    </div>
  );
};
