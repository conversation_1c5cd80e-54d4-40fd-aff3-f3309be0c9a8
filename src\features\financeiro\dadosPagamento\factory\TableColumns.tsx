import { TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';
import {
  checkIfAllItemsAreTrue,
  formatarDataHoraAmigavel,
  getTernaryResult,
  tryGetMonetaryValueOrDefault,
} from '@cvp/utils';
import { Checkbox } from '@cvp/design-system-caixa';
import { TResponseContribuicoes } from '../types/DadosPagamento';
import { converterFormaPagamento } from '../utils/contribuicoes';
import {
  CODIGO_MEIO_PAGAMENTO,
  CODIGO_STATUS_COBRANCA,
} from '../constants/constants';
import { coverterDescStatus } from '../utils/status';

export const columns = (
  cobrancaSelecionada: string | undefined,
  setCobrancaSelecionada: React.Dispatch<
    React.SetStateAction<string | undefined>
  >,
): TableColumn<TResponseContribuicoes>[] => [
  {
    cell: line => {
      const validarEmissaoBoleto = (e: TResponseContribuicoes) =>
        checkIfAllItemsAreTrue([
          e.codStatusCobranca === CODIGO_STATUS_COBRANCA.PENDENTE,
          e.codMeioPagamento === CODIGO_MEIO_PAGAMENTO.BOLETO,
          e.dataDentroLimite === true,
        ]);

      if (validarEmissaoBoleto(line))
        return (
          <Checkbox
            checked={cobrancaSelecionada === line.numCobranca}
            variant="outlineBlack"
            onChange={checked => {
              setCobrancaSelecionada(
                getTernaryResult(!checked, undefined, line.numCobranca),
              );
            }}
          />
        );

      return null;
    },
    width: '60px',
  },
  {
    name: 'Status',
    selector: ({ codStatusCobranca }) => coverterDescStatus(codStatusCobranca),
  },
  {
    name: 'Forma de pagamento',
    selector: ({ codMeioPagamento }) =>
      converterFormaPagamento(codMeioPagamento),
  },
  {
    name: 'Vencimento',
    selector: ({ dataVencimento }) => formatarDataHoraAmigavel(dataVencimento),
  },
  {
    name: 'Valor',
    selector: ({ valorCobrado }) =>
      tryGetMonetaryValueOrDefault(valorCobrado, 0),
  },
  {
    name: 'Data de pagamento',
    selector: ({ dataBaixa, valorBaixado }) =>
      getTernaryResult(
        !dataBaixa || valorBaixado <= 0,
        '-',
        formatarDataHoraAmigavel(dataBaixa),
      ),
  },
];
