import { ReactNode } from 'react';
import { TProfileFilters } from '@src/shared/types/TProfileFilters';
import { ICertificadoPrevidenciaResponse } from '@src/features/layoutPrevidencia/exports';
import { TImpressao } from '../impressaoDocumentosPrevidencia/TImpressao';
import { TParametrosScroll } from '../impressaoDocumentosPrevidencia/TParametrosScroll';

export interface IPrevidenciaContext {
  nomeSocial: string;
  resetarCliente: () => void;
  statusContratoFilter: TProfileFilters;
  setStatusContratoFilter: (data: TProfileFilters) => void;
  certificadoAtivo: ICertificadoPrevidenciaResponse;
  setCertificadoAtivo: (
    certificadoAtivo: ICertificadoPrevidenciaResponse,
  ) => void;
  impressao: TImpressao;
  setImpressao: (impressao: TImpressao) => void;
  parametrosScroll: TParametrosScroll;
  setParametrosScroll: (parametrosScroll: TParametrosScroll) => void;
  isClientePep: boolean;
  setIsClientePep: (isClientePep: boolean) => void;
  setNomeSocial: (valor: string) => void;
}

export type TPrevidenciaContextProviderProps = { children: ReactNode };
