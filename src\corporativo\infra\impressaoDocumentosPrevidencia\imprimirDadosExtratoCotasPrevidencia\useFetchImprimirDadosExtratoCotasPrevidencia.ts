import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import {
  IUseFetchImprimirDadosExtratoCotasPrevidenciaPayload,
  IUseFetchImprimirDadosExtratoCotasPrevidenciaReturn,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchImprimirDadosExtratoCotasPrevidencia';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const useFetchImprimirDadosExtratoCotasPrevidencia = (
  payload?: IUseFetchImprimirDadosExtratoCotasPrevidenciaPayload,
): IUseFetchImprimirDadosExtratoCotasPrevidenciaReturn => {
  const { loading, invocarApiGatewayCvpComRetornoBinary } =
    useApiGatewayCvpInvoker<
      IUseFetchImprimirDadosExtratoCotasPrevidenciaPayload,
      string
    >(PECOS.ImprimirDadosExtratoCotasPrevdencia, {
      data: payload,
      autoFetch: false,
      requestConfig: { responseType: 'blob' },
    });

  const returnIHandleResult = async (): Promise<
    IHandleReponseResult<{ blob: Blob }>
  > => {
    const BlobHtmlResponse = (await invocarApiGatewayCvpComRetornoBinary(
      payload,
    )) as Blob;

    return {
      sucessoBFF: true,
      sucessoGI: true,
      entidade: { blob: BlobHtmlResponse },
      mensagens: [],
    };
  };

  return {
    isLoadingImprimirDadosExtratoCotasPrevidencia: loading,
    fetchImprimirDadosExtratoCotasPrevidencia: returnIHandleResult,
  };
};

export default useFetchImprimirDadosExtratoCotasPrevidencia;
