import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  ISalvarDadosPagamentoPayload,
  ISalvarDadosPagamentoResponse,
  IUseSalvarDadosPagamentoReturn,
} from '@src/corporativo/types/financeiro/resgate/ISalvarDadosPagamento';
import { tryGetValueOrDefault } from '@cvp/utils';

export const useSalvarDadosPagamento = (): IUseSalvarDadosPagamentoReturn => {
  const {
    response: dadosSalvosPagamento,
    loading: isLoadingDadosPagamento,
    invocarApiGatewayCvpComToken: salvarDadosPagamento,
  } = useApiGatewayCvpInvoker<
    Partial<ISalvarDadosPagamentoPayload>,
    ISalvarDadosPagamentoResponse
  >(PECOS.SalvarDadosPagamento, {});

  return {
    dadosSalvosPagamento: tryGetValueOrDefault(
      [dadosSalvosPagamento?.entidade],
      {} as ISalvarDadosPagamentoResponse,
    ),
    isLoadingDadosPagamento,
    salvarDadosPagamento,
  };
};
