import * as ListaHistorico from '@src/features/consulta/historicoSolicitacoes/exports';

type TListaHistoricoPaginacaoProps = {
  dados: ListaHistorico.IResponseHistoricoSolicitacoes[];
  paginator: ReturnType<typeof ListaHistorico.usePaginator>;
};

export const ListaHistoricoPaginacao: React.FC<
  TListaHistoricoPaginacaoProps
> = ({ dados, paginator }) => {
  const { currentPage, rowsPerPage, handleChangePage } = paginator;
  const theme = ListaHistorico.useTheme();

  return (
    <ListaHistorico.Paginator
      variants="default"
      IconFirstPage={
        <ListaHistorico.IconFirstPageSharp
          color={theme.color.palette.primary['90']}
          size="large"
        />
      }
      IconLastPage={
        <ListaHistorico.IconLastPageSharp
          color={theme.color.palette.primary['90']}
          size="large"
        />
      }
      IconLeft={
        <ListaHistorico.IconChevronLeftSharp
          color={theme.color.palette.primary['90']}
          size="large"
        />
      }
      IconRight={
        <ListaHistorico.IconChevronRightSharp
          color={theme.color.palette.primary['90']}
          size="large"
        />
      }
      currentPage={currentPage}
      rowsPerPage={rowsPerPage}
      rowCount={dados.length}
      hasItemsPerPage={false}
      onChangePage={handleChangePage}
      onChangeRowsPerPage={() => null}
    />
  );
};
