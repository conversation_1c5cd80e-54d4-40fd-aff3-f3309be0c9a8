export interface IResponseDadosCertificadosPorCpf {
  codigoApolice: string;
  codigoCanalRecuperacao: string;
  codigoConta: string;
  codigoTipoPagamento: string;
  dataAposentadoria: string;
  dataNascimentoPessoa: string;
  desProduto: string;
  desSubProdutoCategoria: string;
  diaPagamento: string;
  digitoConta: string;
  idPessoaFisica: string;
  metodoPagamento: string;
  nomePessoa: string;
  numeroAgencia: string;
  numeroBanco: string;
  numeroConta: string;
  numeroContrato: string;
  produtoEstaAgregado: string;
  sexoPessoa: string;
  status: string;
  tipoConta: string;
  tipoProduto: string;
  tributacao: string;
}

export interface IRequestDadosCertificadoPorCpf {
  cpfCnpj: string | null;
}
