import {
  DESCRICAO_ALIQUOTA_MAP,
  tryGetValueOrDefault,
} from '@src/features/financeiro/resgate/exports';

/**
 * Obtém a descrição textual de uma alíquota a partir de sua sigla
 *
 * @param {string} [sigla] - Sigla ou código da alíquota (ex: progressiva ou regressiva)
 * @param {string} [fallback='-'] - <PERSON><PERSON> padrão a ser retornado caso a sigla não seja encontrada
 * @returns {string} Descrição textual da alíquota ou o valor de fallback
 */
export const obterDescricaoAliquota = (
  sigla?: string,
  fallback = '-',
): string => {
  const tipoAliquota: string = tryGetValueOrDefault([sigla], '');
  return tryGetValueOrDefault([DESCRICAO_ALIQUOTA_MAP[tipoAliquota]], fallback);
};
