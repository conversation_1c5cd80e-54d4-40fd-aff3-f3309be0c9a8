import {
  IDisplayProps,
  css,
  styled,
  Button,
  TextField,
} from '@src/features/financeiro/dadosPagamento/exports';

export const FlexBox = styled.div<IDisplayProps>(
  ({
    $alignContent,
    $alignItems,
    $flexDirection,
    $flexFlow,
    $flexWrap,
    $justifyContent,
    $gap,
  }) => ({
    display: 'flex',
    alignContent: $alignContent,
    alignItems: $alignItems,
    flexDirection: $flexDirection,
    flexFlow: $flexFlow,
    flexWrap: $flexWrap,
    justifyContent: $justifyContent,
    gap: $gap,
  }),
);

export const ExpandButton = styled.button(() => ({
  alignItems: 'center',
  background: 'none',
  border: 'none',
  display: 'flex',
  width: '100%',
  padding: 0,
  margin: 0,
}));

export const Divider = styled.div<{
  color?: string;
  size?: string;
  topDistance?: string;
  bottomDistance?: string;
}>(({ color, size, bottomDistance, topDistance }) => ({
  border: 'none',
  backgroundColor: color ?? '#D0E0E3',
  height: size ?? '1px',
  marginTop: topDistance ?? '20px',
  marginBottom: bottomDistance ?? '20px',
}));

export const ButtonGerarBoleto = styled(Button)(({ theme }) => {
  return css`
    &:disabled {
      border-color: ${theme.color.content.neutral['02']};
      color: ${theme.color.content.neutral['02']};
      background: none;
      > svg > path {
        fill: ${theme.color.content.neutral['02']};
      }
    }
  `;
});

export const Label = styled(TextField)`
  border: none;
  outline: none;
  pointer-events: none;
`;
