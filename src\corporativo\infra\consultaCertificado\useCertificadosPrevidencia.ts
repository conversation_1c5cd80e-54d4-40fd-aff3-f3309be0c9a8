import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { IUseCertificadosPrevidencia } from '@src/corporativo/types/consultaCertificado/IUseCretificadosPrevidencia';
import { ICertificadoPrevidenciaResponse } from '@src/shared/types/ICertificadoPrevidenciaResponse';
import { useMemo } from 'react';

interface ICertificadosPrevidenciaPayload {
  cpfCliente: string | null;
}
const ARRAY_VAZIO = [] as ICertificadoPrevidenciaResponse[];

const useCertificadosPrevidencia: () => IUseCertificadosPrevidencia = () => {
  const payload: ICertificadosPrevidenciaPayload = {
    cpfCliente: String(getSessionItem('cpfCnpj')),
  };

  const { loading, response } = useApiGatewayCvpInvoker<
    ICertificadosPrevidenciaPayload,
    ICertificadoPrevidenciaResponse[]
  >('PECO_ListarCertificadosPrevidencia', {
    data: { cpfCliente: payload.cpfCliente },
    autoFetch: true,
  });

  const mapSituacaoRegistroFilter = (
    certificados: ICertificadoPrevidenciaResponse[],
  ) => {
    const dados = tryGetValueOrDefault([certificados], ARRAY_VAZIO);
    if (dados?.length > 0) {
      return dados.map(item => ({
        ...item,
        situacaoRegistro: item.situacao,
      }));
    }
    return dados;
  };

  const memoizedData = useMemo(() => {
    return mapSituacaoRegistroFilter(
      tryGetValueOrDefault([response?.entidade], ARRAY_VAZIO),
    );
  }, [response]);

  return {
    loading,
    response: memoizedData,
  };
};

export default useCertificadosPrevidencia;
