import { ICertificadoBeneficioPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/ICertificadoBeneficioPrevidencia';
import { IPagamentoRegularPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IPagamentoRegularPrevidencia';
import { IPessoaPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IPessoaPrevidencia';
import { IProdutoCertificadoPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IProdutoCertificadoPrevidencia';

export interface ICertificadoPrevidenciaResponse {
  empresaId: string;
  certificadoNumero: string;
  certificadoTipo: string;
  situacao: string;
  abertura: string;
  emissao: string;
  aposentadoria: string;
  opcaoImpostoTipo: string;
  regimeTributario: string;
  apoliceId: string;
  certificadoBeneficios: ICertificadoBeneficioPrevidencia[];
  pagamentoRegular: IPagamentoRegularPrevidencia;
  pessoa: IPessoaPrevidencia;
  produto: IProdutoCertificadoPrevidencia;
  ordem?: number;
  [key: string]: unknown;
}
