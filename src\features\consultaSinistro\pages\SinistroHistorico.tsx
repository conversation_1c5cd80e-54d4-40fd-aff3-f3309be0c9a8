import {
  Alert,
  Button,
  CardSinistro,
  checkIfAllItemsAreTrue,
  For,
  Grid,
  IconInfoRound,
  IconWarningRound,
  LayoutPlataforma,
  LOADING,
  LoadingSpinner,
  Match,
  previdencia,
  REDIRECT_BUTTONS,
  ROUTES,
  S,
  SwitchCase,
  useListarSinistro,
  useNavigate,
} from '../exports';

const HistoricoSinistro: React.FC = () => {
  const { loading, response: dadosSinistro, error } = useListarSinistro();
  const navigate = useNavigate();

  return (
    <LayoutPlataforma
      primaryTitle={previdencia.primaryTitle}
      buttonsRedirect={REDIRECT_BUTTONS('sinistros')}
      secondaryTitle={previdencia.sinistroTitle}
      filterBykey="situacao"
      profileFilters={{}}
      habilitarPesquisa={false}
      exibirFiltros={false}
      userProfile="OPERADOR"
    >
      <SwitchCase fallback={null}>
        <Match when={loading}>
          <Grid margin="30px" justify="center">
            <LoadingSpinner size="medium">{LOADING}</LoadingSpinner>
          </Grid>
        </Match>
        <Match when={checkIfAllItemsAreTrue([!loading, error])}>
          <Alert
            variant="warning-01"
            icon={<IconWarningRound size="medium" color="#CA9804" />}
          >
            Não foi possível carregar os sinistros. Tente novamente mais tarde.
          </Alert>
        </Match>
        <Match when={checkIfAllItemsAreTrue([!loading, !dadosSinistro.length])}>
          <Alert
            variant="information-01"
            icon={<IconInfoRound size="medium" color="#038299" />}
          >
            Não foi encontrado nenhum sinistro para este cliente
          </Alert>
        </Match>
        <Match when={[!loading, !!dadosSinistro.length]}>
          <S.ContainerSinistrosStyled>
            <S.SinistrosGridStyled>
              <For each={dadosSinistro}>
                {sinistro => (
                  <CardSinistro
                    key={sinistro.numeroAvisoSinistro}
                    {...sinistro}
                  />
                )}
              </For>
            </S.SinistrosGridStyled>
          </S.ContainerSinistrosStyled>
        </Match>
      </SwitchCase>
      <S.GoBackButtonContainerStyled>
        <Button variant="secondary" onClick={() => navigate(ROUTES.INICIO)}>
          Voltar
        </Button>
      </S.GoBackButtonContainerStyled>
    </LayoutPlataforma>
  );
};

export default HistoricoSinistro;
