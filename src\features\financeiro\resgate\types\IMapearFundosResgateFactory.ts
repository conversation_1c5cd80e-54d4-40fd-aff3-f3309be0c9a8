import { IListarFundosParaResgateFundosDisponiveis } from '@src/features/financeiro/resgate/exports';

export interface IMapearFundosResgateFactory {
  fundos: IListarFundosParaResgateFundosDisponiveis[];
  selecionarFundoParaResgate: (codigoFundo: string) => void;
  alterarValorResgatado: (codigoFundo: string, valorRetirar: string) => void;
  isTipoResgateTotal: boolean;
  valorMinimoResgate: number | undefined;
}
