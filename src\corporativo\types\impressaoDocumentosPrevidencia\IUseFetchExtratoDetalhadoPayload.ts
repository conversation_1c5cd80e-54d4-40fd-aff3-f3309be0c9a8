import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IUseFetchExtratoDetalhadoPayload {
  cpfCnpj: string;
  numeroCertificado: string;
  fundo: string;
  dataInicio: string;
  dataFim: string;
}

export interface IUseFetchExtratoDetalhadoReturn {
  isLoadingExtratoDetalhado: boolean;
  fetchExtratoDetalhado: () => Promise<
    IHandleReponseResult<{ return: string }> | undefined
  >;
}
