import {
  capitalize,
  FORMA_PAGAMENTO,
  formatarValorPadraoBrasileiro,
  getTernaryResult,
  Grid,
  GridItem,
  ICertificadoPrevidenciaResponse,
  IFiltroTabelaAporte,
  MODAL_APORTE,
  Text,
  tryGetValueOrDefault,
} from '@src/features/financeiro/aporte/exports';

interface IDadosModalVerificarAporte {
  filtroTabelaFundos: IFiltroTabelaAporte;
  dadosCertificado: ICertificadoPrevidenciaResponse | undefined;
}

const DadosModalVerificarAporte: React.FC<IDadosModalVerificarAporte> = ({
  filtroTabelaFundos,
  dadosCertificado,
}) => {
  return (
    <Grid>
      <GridItem xs="1">
        <Text
          variant="text-big-600"
          fontColor="content-neutral-06"
          textAlign="center"
        >
          {MODAL_APORTE.titulo}
        </Text>
      </GridItem>

      <GridItem xs="1/3">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {MODAL_APORTE.titular}
        </Text>
        <Text variant="text-large-400" fontColor="content-neutral-04">
          {capitalize(
            tryGetValueOrDefault(
              [dadosCertificado?.pessoa.pessoaFisica.nome],
              '',
            ),
          )}
        </Text>
      </GridItem>
      <GridItem xs="1/3">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {MODAL_APORTE.certificado}
        </Text>
        <Text variant="text-large-400" fontColor="content-neutral-04">
          {dadosCertificado?.certificadoNumero}
        </Text>
      </GridItem>
      <GridItem xs="1/3">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {MODAL_APORTE.modalidade}
        </Text>
        <Text variant="text-large-400" fontColor="content-neutral-04">
          {`${dadosCertificado?.produto?.modalidade}`}
          {getTernaryResult(
            dadosCertificado?.regimeTributario === undefined,
            '',
            dadosCertificado?.regimeTributario,
          )}
        </Text>
      </GridItem>
      <GridItem xs="1/3">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {MODAL_APORTE.formaPagamento}
        </Text>
        <Text variant="text-large-400" fontColor="content-neutral-04">
          {getTernaryResult(
            filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito,
            'Débito',
            'Boleto',
          )}
        </Text>
      </GridItem>
      <GridItem xs="1/3">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {MODAL_APORTE.valor}
        </Text>
        <Text variant="text-large-400" fontColor="content-neutral-04">
          {formatarValorPadraoBrasileiro(filtroTabelaFundos.valorContribuicao)}
        </Text>
      </GridItem>
    </Grid>
  );
};

export default DadosModalVerificarAporte;
