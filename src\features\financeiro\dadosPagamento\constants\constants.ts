export const CODIGO_STATUS_COBRANCA = {
  GERADA: 'GE',
  COBRADA: 'CO',
  PAGO: 'BT',
  CANCELADA: 'CC',
  CANCELADA_INADIMPLENCIA: 'CI',
  INADIMPLENTE: 'IN',
  PARCIALMENTE_PAGO: 'BP',
  PENDENTE: 'CB',
};

export const STATUS_COBRANCA_CONVERTIDO = {
  PAGA: 'Paga',
  NAO_PAGO: 'Não pago',
  PENDENTE: 'Pendente',
  CANCELADA: 'Cancelada',
};

export const CODIGO_MEIO_PAGAMENTO = {
  BOLETO: 'FC',
  DEBITOCONTA: 'CB',
};

export const RETORNO_MENSAGENS = {
  AGENCIA: "'Codigo Agencia' must not be empty.",
  DIA_VENCIMENTO: "'Data Vencimento' must not be empty.",
  OPERACAO: "'Codigo Operacao' must not be empty.",
  NUMERO_CONTA: "'Numero Conta' must not be empty.",
  DIGITO: "'Digito Verificador' must not be empty.",
} as const;

export const MENSAGENS_TRADUZIDAS = {
  [RETORNO_MENSAGENS.AGENCIA]: 'Código da Agência não pode estar vazio.',
  [RETORNO_MENSAGENS.DIA_VENCIMENTO]: 'Dia do vencimento não pode estar vazio.',
  [RETORNO_MENSAGENS.OPERACAO]: 'Código da Operação não pode estar vazio.',
  [RETORNO_MENSAGENS.NUMERO_CONTA]: 'Número da conta não pode estar vazio.',
  [RETORNO_MENSAGENS.DIGITO]: 'Dígito da Conta não pode estar vazio.',
} as const;

export const CONFIGURACAO_PAGAMENTO_INICIAL = {
  diaVencimento: '',
  formaPagamento: '',
};

export const CONTA_INICIAL = {
  numeroAgencia: '',
  numeroConta: '',
  digitoConta: '',
  operacao: '',
};

export const STATE_INICIAL_NOVA_CONTA = {
  ...CONTA_INICIAL,
  ...CONFIGURACAO_PAGAMENTO_INICIAL,
};

export const BOTAO_HISTORICO = 'Histórico de pagamentos';

export const MSG_HISTORICO = {
  MARQUE: 'Marque o checkbox do boleto',
  PENDENTE: 'pendente para emitir a 2ª via',
};
