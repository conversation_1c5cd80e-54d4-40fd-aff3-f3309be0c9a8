import * as AtivacaoSuspensao from '../exports';
import {
  IResponseStatusCoberturasContratadas,
  IBeneficioContribuicaoCertificado,
  EnumStatusAtivacaosuspensao,
  EnumTipoBeneficios,
} from '../types/EnumsStatusAtivacaoSuspensao.types';

interface UseFormularioAtivacaoSuspensaoProps {
  dadosApi?: IResponseStatusCoberturasContratadas | null;
}

const mapearDadosApiParaContribuicoes = (
  dadosApi: IResponseStatusCoberturasContratadas | null,
): AtivacaoSuspensao.IContribuicaoItem[] => {
  if (!dadosApi?.beneficioContribuicaoCertificado) {
    return [];
  }

  const contribuicoes: AtivacaoSuspensao.IContribuicaoItem[] = [];

  dadosApi.beneficioContribuicaoCertificado.forEach(
    (beneficio: IBeneficioContribuicaoCertificado) => {
      const ativo =
        beneficio.statusContribuicao === EnumStatusAtivacaosuspensao.Ativo;

      if (beneficio.tipoBeneficio === EnumTipoBeneficios.PREV) {
        contribuicoes.push({
          tipo: 'reserva',
          nome: beneficio.descricaoBeneficio || 'Reserva',
          valorContribuicao: parseFloat(beneficio.valorPagamento) || 0,
          saldoAcumulado: parseFloat(dadosApi.valorTotalSaldo) || 0,
          ativo,
        });
      }

      if (beneficio.tipoBeneficio === EnumTipoBeneficios.RISCO) {
        contribuicoes.push({
          tipo: 'cuidadoExtra',
          nome: beneficio.descricaoBeneficio || 'Pecúlio',
          valorContribuicao: parseFloat(beneficio.valorPagamento) || 0,
          valorIdentizacao: parseFloat(beneficio.valorBeneficioEsperado) || 0,
          prazoRecebimento: beneficio.tipoCalculo || 'Uma única vez',
          ativo,
        });
      }
    },
  );

  return contribuicoes;
};

export const useFormularioAtivacaoSuspensao = (
  props?: UseFormularioAtivacaoSuspensaoProps,
) => {
  const [contribuicoes, setContribuicoes] = AtivacaoSuspensao.useState<
    AtivacaoSuspensao.IContribuicaoItem[]
  >([]);
  const [loading, setLoading] = AtivacaoSuspensao.useState(false);

  // Memoizar a função toggleContribuicao para evitar recriações
  const toggleContribuicao = AtivacaoSuspensao.useCallback((index: number) => {
    setContribuicoes((prev: AtivacaoSuspensao.IContribuicaoItem[]) =>
      prev.map((contrib: AtivacaoSuspensao.IContribuicaoItem, i: number) =>
        i === index ? { ...contrib, ativo: !contrib.ativo } : contrib,
      ),
    );
  }, []);

  // Memoizar os dados mapeados para evitar recálculos desnecessários
  const contribuicoesMapeadas = AtivacaoSuspensao.useMemo(() => {
    if (!props?.dadosApi) return [];
    return mapearDadosApiParaContribuicoes(props.dadosApi);
  }, [props?.dadosApi]);

  const carregarContribuicoes = AtivacaoSuspensao.useCallback(async () => {
    setLoading(true);
    try {
      setContribuicoes(contribuicoesMapeadas);
    } catch (error) {
      console.error(
        AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.MENSAGENS.ERRO_CARREGAR,
        error,
      );
      setContribuicoes([]);
    } finally {
      setLoading(false);
    }
  }, [contribuicoesMapeadas]);

  // Usar useMemo para evitar atualizações desnecessárias quando os dados não mudaram
  AtivacaoSuspensao.useEffect(() => {
    if (contribuicoesMapeadas.length > 0 || props?.dadosApi === null) {
      carregarContribuicoes();
    }
  }, [carregarContribuicoes, contribuicoesMapeadas.length, props?.dadosApi]);

  return {
    contribuicoes,
    loading,
    toggleContribuicao,
    carregarContribuicoes,
  };
};
