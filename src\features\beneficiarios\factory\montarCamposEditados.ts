import { TBeneficiariosFormData } from '../exports';

export const montarCamposEditados = (
  formData: FormData,
): Record<string, TBeneficiariosFormData> => {
  return formData.entries().reduce((acc, [key, value]) => {
    const [name, ...idBeneficiario] = key.split('-') as [
      keyof TBeneficiariosFormData,
      string,
    ];

    const id = idBeneficiario.join('-');

    if (!acc[id]) {
      acc[id] = { idBeneficiario: id };
    }

    acc[id][name] = value as string;
    return acc;
  }, {} as Record<string, TBeneficiariosFormData>);
};
