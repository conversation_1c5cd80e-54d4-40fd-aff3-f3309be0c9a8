import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IConfirmarAssinaturaInput,
  IConfirmarOperacaoAssinaturaPayload,
  IResponseConfirmarOperacaoAssinatura,
  IUseConfirmarOperacaoAssinaturaCaixa,
} from '@src/corporativo/types/assinatura/IConfirmarOperacaoAssinatura';

import { checkIfAllItemsAreTrue, removeSessionItem } from '@cvp/utils';
import { ASSINATURA_SESSION_KEY } from '@src/corporativo/constants/assinatura/assinaturaSession';
import { CODIGO_TIPO_ASSINATURA } from '@src/corporativo/constants/assinatura/confirmarOperacao';
import { useContext } from 'react';

export const useConfirmarOperacaoAssinaturaCaixa =
  (): IUseConfirmarOperacaoAssinaturaCaixa => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const {
      loading,
      invocarApiGatewayCvpComToken: invocarApiConfirmarAssinatura,
      response,
    } = useApiGatewayCvpInvoker<
      IConfirmarAssinaturaInput,
      IResponseConfirmarOperacaoAssinatura
    >(PECOS.AssinaturaCaixaConfimarOperacao, {
      autoFetch: false,
    });

    const confirmarAssinatura = async (
      payload: IConfirmarOperacaoAssinaturaPayload,
    ) => {
      const input: IConfirmarAssinaturaInput = {
        codigoCertificado: certificadoAtivo.certificadoNumero,
        codigoTipoAssinatura: CODIGO_TIPO_ASSINATURA.PLATAFORMA_CAIXA,
        ...payload,
      };

      const result:
        | IHandleReponseResult<IResponseConfirmarOperacaoAssinatura>
        | undefined = await invocarApiConfirmarAssinatura(input);

      if (checkIfAllItemsAreTrue([!!result?.sucessoBFF, !!result?.sucessoGI])) {
        removeSessionItem(ASSINATURA_SESSION_KEY);
      }

      return result;
    };

    return {
      loading,
      confirmarAssinatura,
      response: response?.entidade,
    };
  };
