import {
  capitalize,
  COMPROVANTE_APORTE,
  formatarValorPadraoBrasileiro,
  GridItem,
  IFiltroTabelaAporte,
  Text,
} from '@src/features/financeiro/aporte/exports';

interface IFiltroSelecionadoTabelaFundos {
  dadosAporte: IFiltroTabelaAporte;
  perfilInvestidorConcat: string;
}

const FiltroSelecionadoTabelaFundos: React.FC<
  IFiltroSelecionadoTabelaFundos
> = ({ dadosAporte, perfilInvestidorConcat }) => {
  return (
    <>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.FORMA_PAGAMENTO}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {capitalize(dadosAporte.formaPagamento)}
        </Text>
      </GridItem>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.VALOR_CONTRIBUICAO}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {formatarValorPadraoBrasileiro(dadosAporte.valorContribuicao)}
        </Text>
      </GridItem>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.PERFIS}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {perfilInvestidorConcat}
        </Text>
      </GridItem>
    </>
  );
};

export default FiltroSelecionadoTabelaFundos;
