import {
  IUltimasSimulacoesRow,
  TableColumn,
  Text,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const COLUMNS_ULTIMAS_SIMULACOES: TableColumn<IUltimasSimulacoesRow>[] =
  [
    {
      id: 'DataHora',
      name: 'Data e Hora',
      cell: row => <Text variant="text-small-700">{row.dataHora}</Text>,
      sortable: false,
      width: '20%',
    },
    {
      id: 'tipoRenda',
      name: 'Tip<PERSON> de Renda',
      cell: row => <Text variant="text-small-700">{row.tipoRenda}</Text>,
      sortable: false,
      width: '20%',
    },
    {
      id: 'tributacao',
      name: 'Tribut<PERSON><PERSON>',
      cell: row => <Text variant="text-small-700">{row.tributacao}</Text>,
      sortable: false,
      width: '20%',
    },
    {
      id: 'periodoRecebimento',
      name: '<PERSON><PERSON><PERSON>',
      cell: row => (
        <Text variant="text-small-700">{row.periodoRecebimento}</Text>
      ),
      sortable: false,
      width: '20%',
    },
    {
      id: 'rendaLiquida',
      name: '<PERSON><PERSON>quida',
      cell: row => <Text variant="text-small-700">{row.rendaLiquida}</Text>,
      sortable: false,
      width: '20%',
    },
  ];
