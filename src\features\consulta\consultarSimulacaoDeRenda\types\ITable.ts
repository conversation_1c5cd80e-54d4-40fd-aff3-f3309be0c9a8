import {
  IHandleReponseResult,
  ISimulacaoDeRendaResponse,
  IUseVerUltimasSolicitacoesResponse,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/';

export interface ITableDataUltimasSolicitacoes {
  data: IUseVerUltimasSolicitacoesResponse[];
}

export interface ITableDataConsulta {
  data: IHandleReponseResult<ISimulacaoDeRendaResponse>;
}

export interface ITooltipTableRow {
  aliquotasRegressivas: string;
  prazoDeAcumulacao: string;
}

export interface IAletsTable {
  income: {
    renda: string;
    numeroDaRenda: string;
  };
  data: object;
}

export interface ILoadingTable {
  deveExibirLoading: boolean;
}
