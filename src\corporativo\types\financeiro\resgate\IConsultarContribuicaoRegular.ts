import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { TTiposDePerfis } from '@src/shared/types/TTiposDePerfis';

export interface IConsultarContribuicaoRegularPayload {
  codigoCertificado: string;
  numeroResgate: string;
}

export interface IConsultarContribuicaoRegularResponse {
  permiteDefinirContribRegular: boolean;
  fundos: IConsultarContribuicaoRegularFundos[];
  quantidadeFundosDisponiveis: number;
  valorMinimoContribuicaoRegular: number;
  valorContribuicaoRegularlAtual: number;
}

export interface IConsultarContribuicaoRegularFundos {
  codigoReserva: string;
  codigoFundo: string;
  descricaoFundo: string;
  saldo: number;
  rentabilidade: number;
  perfilFundo: TTiposDePerfis;
  perfilRisco: string;
  fundoEmCarencia: boolean;
  avisoCarencia: string;
  valorContribuicao?: number;
  selecionado?: boolean;
}

export interface IUseConsultarContribuicaoRegularRetorno {
  dadosContribuicaoRegular: IConsultarContribuicaoRegularResponse;
  isLoadingConsultaContribuicaoRegular: boolean;
  consultarContribuicaoRegular: (
    dynamicPayload?: Partial<IConsultarContribuicaoRegularPayload>,
  ) => Promise<
    IHandleReponseResult<IConsultarContribuicaoRegularResponse> | undefined
  >;
}
