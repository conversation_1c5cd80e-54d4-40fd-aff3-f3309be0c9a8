import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ModalConfirmacaoAliquota = ({
  isOpen,
  toggleModalConfirmacaoAliquota,
  cancelarEscolhaAliquota,
  confirmarEscolhaAliquota,
}: Resgate.IModalConfirmacaoAliquotaProps): React.ReactElement => {
  return (
    <Resgate.Dialog open={isOpen} onOpenChange={toggleModalConfirmacaoAliquota}>
      <Resgate.Dialog.Content>
        <Resgate.Dialog.Header variant="highlight">
          <Resgate.Text variant="heading-small-600">
            {Resgate.TEXTOS_MODAL_CONFIRMACAO.TITULO}
          </Resgate.Text>
        </Resgate.Dialog.Header>
        <Resgate.Dialog.Body>
          <Resgate.Text variant="text-small-600">
            {Resgate.TEXTOS_MODAL_CONFIRMACAO.PERGUNTA_CONFIRMACAO}
          </Resgate.Text>
        </Resgate.Dialog.Body>
        <Resgate.Dialog.Footer>
          <Resgate.Button
            onClick={cancelarEscolhaAliquota}
            size="standard"
            variant="secondary-outlined"
          >
            {Resgate.TEXTOS_MODAL_CONFIRMACAO.BOTOES.VOLTAR}
          </Resgate.Button>
          <Resgate.Button
            onClick={confirmarEscolhaAliquota}
            size="standard"
            variant="primary"
          >
            {Resgate.TEXTOS_MODAL_CONFIRMACAO.BOTOES.PROSSEGUIR}
          </Resgate.Button>
        </Resgate.Dialog.Footer>
      </Resgate.Dialog.Content>
    </Resgate.Dialog>
  );
};
