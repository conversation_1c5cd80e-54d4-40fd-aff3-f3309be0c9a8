import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { tiposDeRendaFormatedSelect } from '@src/corporativo/factories/consulta/simulacaoDeRenda/tiposRenda';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IUseObterTipoDeRenda,
  IUseObterTipoDeRendaBody,
  IUseObterTipoDeRendaResponse,
} from '@src/corporativo/types/consulta/ISimularRenda';
import { useContext } from 'react';

const useObterTipoDeRenda = (): IUseObterTipoDeRenda => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { loading, response } = useApiGatewayCvpInvoker<
    IUseObterTipoDeRendaBody,
    IUseObterTipoDeRendaResponse[]
  >(PECOS.TiposDePagamento, {
    autoFetch: true,
    data: {
      Cpf: String(getSessionItem('cpfCnpj')),
      NumeroCertificado: certificadoAtivo?.certificadoNumero,
    },
  });

  const tiposDeRenda: IUseObterTipoDeRendaResponse[] = tryGetValueOrDefault(
    [response?.entidade],
    [] as IUseObterTipoDeRendaResponse[],
  );

  const tiposDeRendaFormatado = tiposDeRendaFormatedSelect(tiposDeRenda);
  return { loading, tiposDeRendaFormatado };
};

export default useObterTipoDeRenda;
