import * as Transferencia from '@src/features/financeiro/transferencias/exports';

export const useComprovanteTransferencia =
  (): Transferencia.TUseComprovanteSolicitacao => {
    const { invocarApiGatewayCvpComToken } =
      Transferencia.useObterComprovante();
    const { certificadoAtivo, setImpressao, setParametrosScroll } =
      Transferencia.useContext(Transferencia.PrevidenciaContext);
    const navigate = Transferencia.useNavigate();
    const [error, setError] = Transferencia.useState('');
    const [loadingComprovantes, setLoadingComprovantes] =
      Transferencia.useState<string[]>([]);

    const prepararComprovante = (
      resultado:
        | Transferencia.IHandleReponseResult<Transferencia.IObterComprovanteResgateResponse>
        | undefined,
    ) => {
      setImpressao({
        tipoDocumento: Transferencia.TIPO_DOCUMENTO,
        base64: resultado?.entidade?.comprovante
          ? [resultado?.entidade?.comprovante]
          : undefined,
        tipoEmail: Transferencia.tipoEmailConstants.COMPROVANTE_RESGATE,
        parametrosEnvio: {
          cpfCnpj: String(Transferencia.getSessionItem('cpfCnpj')),
          numeroCertificado: certificadoAtivo?.certificadoNumero,
        },
      });

      setParametrosScroll({
        rota: window.location.pathname,
        valorScroll: window.scrollY,
      });

      navigate(Transferencia.ROUTES.IMPRIMIR);
    };

    const handleRemoveLoadingComprovante = (idRequisicao: string) => {
      setLoadingComprovantes(prev =>
        prev.filter(item => item !== idRequisicao),
      );
    };

    const handleObterComprovante = async () => {
      const numCertificadoRequisicao = certificadoAtivo?.certificadoNumero;

      setLoadingComprovantes(prev => [...prev, numCertificadoRequisicao]);

      const resultado = await invocarApiGatewayCvpComToken({
        codigoRequisicao: Transferencia.CODIGO_REQUISICAO,
        idRequisicao: numCertificadoRequisicao,
      }).finally(() => {
        handleRemoveLoadingComprovante(numCertificadoRequisicao);
      });

      if (resultado?.sucessoBFF && resultado.sucessoGI) {
        prepararComprovante(resultado);
        return;
      }

      setError(
        Transferencia.tryGetValueOrDefault(
          [resultado?.mensagens?.[0]?.descricao],
          '',
        ),
      );

      setTimeout(() => {
        setError('');
      }, 5000);
    };

    return {
      handleObterComprovante,
      error,
      loadingComprovantes,
      idRequisicao: certificadoAtivo.certificadoNumero,
    };
  };
