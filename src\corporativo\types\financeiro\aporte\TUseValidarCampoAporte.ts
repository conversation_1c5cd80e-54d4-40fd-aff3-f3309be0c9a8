import {
  IHandleReponseResult,
  IValidarCampoAporteResponse,
} from '@src/features/financeiro/aporte/exports';

export type TUseValidarCampoAporte = {
  loading: boolean;
  response: IValidarCampoAporteResponse;
  invocarApiGatewayCvpComToken: (dynamicPayload: {
    nomeParticipante: string;
    cpfParticipante: string;
  }) => Promise<IHandleReponseResult<IValidarCampoAporteResponse> | undefined>;
};
