import * as DadosParticipante from '../exports';

type TUseAtualizarDadosParticipante = (
  lgpdControls: DadosParticipante.IControlesLGPD,
  obterDadosParticipante: VoidFunction,
) => {
  atualizarDadosParticipante: (
    values: DadosParticipante.IFormDadosParticipante,
  ) => Promise<void>;
};

export const useAtualizarDadosParticipante: TUseAtualizarDadosParticipante = (
  lgpdControls,
  obterDadosParticipante,
) => {
  const { alterarDadosParticipante } =
    DadosParticipante.useAlterarDadosParticipante();
  const { certificadoAtivo, nomeSocial, setNomeSocial } =
    DadosParticipante.React.useContext(DadosParticipante.PrevidenciaContext);

  const dispatch = DadosParticipante.React.useContext(
    DadosParticipante.DadosParticipanteDispatchContext,
  );
  const cpfCnpj = DadosParticipante.Utils.getSessionItem('cpfCnpj') as string;
  const numeroCertificado = certificadoAtivo.certificadoNumero;

  const { dadosParticipante, idCidade } = DadosParticipante.React.useContext(
    DadosParticipante.DadosParticipanteContext,
  );
  const { alterarAlerta } = DadosParticipante.useAlertaEdicao();

  const { editarNomeSocial } = DadosParticipante.useAtualizarNomeSocial();

  const atualizarDadosParticipante = async (
    values: DadosParticipante.IFormDadosParticipante,
  ) => {
    if (!values) return;

    const payloadNomeSocial = DadosParticipante.obterPayloadNomeSocial(
      values.nomeSocial,
      nomeSocial,
    );

    if (payloadNomeSocial) {
      const resultado = await editarNomeSocial(payloadNomeSocial);

      if (resultado)
        setNomeSocial(
          DadosParticipante.Utils.tryGetValueOrDefault([values.nomeSocial], ''),
        );
    }

    dispatch({
      type: DadosParticipante.EDadosParticipanteActionKind
        .SALVAR_DADOS_PARTICIPANTE,
      salvar: true,
    });

    try {
      const telefonesEditados = [
        DadosParticipante.Utils.telefoneUtils.unmask(values.celular),
        DadosParticipante.Utils.telefoneUtils.unmask(values.telefone),
      ];
      const encontrarTelefone = (numero: string) =>
        dadosParticipante.telefones.find(
          dadosTelefone =>
            dadosTelefone.numeroTelefone ===
            DadosParticipante.Utils.telefoneUtils.unmask(numero).slice(2),
        );
      const telefonesOriginal = [
        encontrarTelefone(dadosParticipante.celular),
        encontrarTelefone(dadosParticipante.telefone),
      ];

      const telefones = DadosParticipante.montarDadosTelefone(
        telefonesOriginal,
        telefonesEditados,
      );

      const email = {
        cpfCnpj,
        numeroCertificado,
        email: values.email,
        emailId: DadosParticipante.Utils.tryGetValueOrDefault(
          [dadosParticipante.idEmail],
          null,
        ),
        tipoEmail: DadosParticipante.Utils.tryGetValueOrDefault(
          [dadosParticipante.tipoEmail],
          DadosParticipante.TIPO_EMAIL.PESSOAL,
        ),
      };

      const endereco = DadosParticipante.validarEnderecoEditado(
        DadosParticipante.montarDadosEndereco(
          values as unknown as Record<string, string>,
        ),
        DadosParticipante.montarDadosEndereco({
          ...dadosParticipante,
          idCidade: DadosParticipante.Utils.tryGetValueOrDefault(
            [idCidade],
            dadosParticipante.idCidade,
          ),
        } as unknown as Record<string, string>),
      );
      const dadosEnderecoParticipante =
        DadosParticipante.Utils.getTernaryResult(
          !endereco,
          {},
          {
            dadosEnderecoParticipante: {
              cpfCnpj,
              numeroCertificado,
              ...endereco,
            } as DadosParticipante.IEndereco & DadosParticipante.IAlteracaoBase,
          },
        );

      const response = await alterarDadosParticipante({
        dadosTelefoneParticipante: {
          cpfCnpj,
          numeroCertificado,
          listaDadosTelefoneParticipante: telefones,
        },
        consentimentoCaixa: lgpdControls.dadosPessoaisOutrosProdutos,
        consentimentoParceiros: lgpdControls.dadosPessoaisParceiros,
        dadosEmailParticipante: email,
        ...dadosEnderecoParticipante,
      });

      if (!response?.sucessoGI || !response?.sucessoBFF) {
        throw Error(DadosParticipante.ERRO_ATUALIZAR_DADOS_PARTICIPANTE);
      }
      alterarAlerta(
        DadosParticipante.ALERTAS.SUCESSO_ALTERAR_DADOS_PARTICIPANTE,
      );
    } catch (err) {
      if (
        err instanceof Error &&
        err.message === DadosParticipante.ERRO_ATUALIZAR_DADOS_PARTICIPANTE
      ) {
        alterarAlerta(DadosParticipante.alertaErroEdicaoDadosParticipante());
      }
    } finally {
      dispatch({
        type: DadosParticipante.EDadosParticipanteActionKind
          .SALVAR_DADOS_PARTICIPANTE,
        salvar: false,
      });
      obterDadosParticipante();
    }
  };

  return {
    atualizarDadosParticipante,
  };
};
