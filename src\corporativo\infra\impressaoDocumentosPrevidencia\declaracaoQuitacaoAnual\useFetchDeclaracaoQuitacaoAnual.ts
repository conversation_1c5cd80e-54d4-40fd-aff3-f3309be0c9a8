import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IUseFetchDeclaracaoQuitacaoAnualPayload,
  IUseFetchDeclaracaoQuitacaoAnualResponse,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchDeclQuitacaoAnual';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const useFetchDeclaracaoQuitacaoAnual = (
  declaracaoQuitacaoAnualPayload?: IUseFetchDeclaracaoQuitacaoAnualPayload,
): IUseFetchDeclaracaoQuitacaoAnualResponse => {
  const {
    loading: isDeclaracaoQuitacaoAnualLoading,
    invocarApiGatewayCvpComToken: fetchDeclaracaoQuitacaoAnual,
  } = useApiGatewayCvpInvoker<IUseFetchDeclaracaoQuitacaoAnualPayload, string>(
    PECOS.GerarQuitacaoAnual,
    {
      data: declaracaoQuitacaoAnualPayload,
      autoFetch: false,
    },
  );

  return {
    isDeclaracaoQuitacaoAnualLoading,
    fetchDeclaracaoQuitacaoAnual,
  };
};

export default useFetchDeclaracaoQuitacaoAnual;
