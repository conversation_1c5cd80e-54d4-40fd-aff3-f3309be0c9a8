import { useContext } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';
import {
  TUseModificarBeneficiarios,
  TUseModificarBeneficiariosRequest,
} from '@src/corporativo/types/beneficiarios';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const useModificarBeneficiarios: TUseModificarBeneficiarios = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    Partial<TUseModificarBeneficiariosRequest>,
    unknown
  >(PECOS.ModificarBeneficiarios, {
    data: {
      Cpf: String(getSessionItem('cpfCnpj')),
      NumeroCertificado: certificadoAtivo?.certificadoNumero,
    },
  });

  return {
    loading,
    invocarApiGatewayCvpComToken,
  };
};

export default useModificarBeneficiarios;
