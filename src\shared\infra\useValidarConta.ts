import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IValidarContaPayload,
  IValidarContaResponse,
  IUseValidarContaRetorno,
} from '@src/shared/types/IUseValidarConta';

export const useValidarConta = (): IUseValidarContaRetorno => {
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj');

  const {
    response: responseValidacaoConta,
    loading: isLoadingValidacaoConta,
    invocarApiGatewayCvpComToken: validarConta,
    setResponse,
  } = useApiGatewayCvpInvoker<
    Partial<IValidarContaPayload>,
    IValidarContaResponse
  >(PECOS.ValidarConta, {
    data: {
      cpfCnpj: cpfCnpjSession ?? '',
    },
    autoFetch: false,
  });

  return {
    responseValidacaoConta,
    isLoadingValidacaoConta,
    validarConta,
    setResponse,
  };
};
