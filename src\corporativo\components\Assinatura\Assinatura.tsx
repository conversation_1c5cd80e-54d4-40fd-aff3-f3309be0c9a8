/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from 'react';
import { mountRootParcel } from 'single-spa';
import { IAssinaturaRequest } from '@src/corporativo/types/assinatura/IAssinaturaRequest';
import { Match, SwitchCase } from '@cvp/componentes-posvenda';
import { EModuleState } from '@src/corporativo/types/assinatura/EModuleState';
import { ID_ANGULAR_COMPONENT } from '@src/corporativo/constants/assinatura/params';
import { getData } from '@src/corporativo/factories/assinatura/factoryAssinaturaParams';
import { singleSpaPropsSubject } from './single-spa-props';

const Assinatura: React.FC<IAssinaturaRequest> = ({ dados, callback }) => {
  const idAngularComponent = 'angularComponent';
  const angularRootRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<any>(null);
  const nomeSubMfe = AppConfig.REACT_APP_MFE_ASSINATURA_NAME;
  const [moduleState, setModuleState] = useState<EModuleState>(
    EModuleState.INIT,
  );
  const { cpfCnpj, numeroCertificado } = dados;

  useEffect(() => {
    const moduleImported = angularRootRef;
    if (dados) {
      setModuleState(EModuleState.LOADING);
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      System.import(nomeSubMfe)
        .then((app: any) => {
          if (app && angularRootRef.current) {
            mountRootParcel(
              {
                bootstrap: app.bootstrap,
                mount: app.mount,
                unmount: app.unmount,
                name: 'mfe-assinatura',
              },
              {
                domElement: angularRootRef.current,
                nomeSubMFE: nomeSubMfe,
                appPropsReplaySub: singleSpaPropsSubject,
                dados: getData(cpfCnpj, numeroCertificado),
                funcaoCallback: callback,
              },
            );
            appRef.current = app;
            setModuleState(EModuleState.LOADED);
          }
        })
        .catch(() => {
          setModuleState(EModuleState.ERROR);
        });
    }

    return () => {
      if (moduleImported.current && appRef.current) {
        appRef.current.unmount(moduleImported.current);
      }
    };
  }, []);

  useEffect(() => {
    if (moduleState === EModuleState.ERROR) {
      const element = document?.getElementById(ID_ANGULAR_COMPONENT);
      if (element) element.style.display = 'none';
    }
  }, [moduleState]);

  return (
    <>
      <div
        id={idAngularComponent}
        style={{ width: '100%' }}
        ref={angularRootRef}
      />
      <SwitchCase fallback={<div />}>
        <Match when={moduleState === EModuleState.ERROR}>
          Não foi possível carregar a assinatura.
        </Match>
      </SwitchCase>
    </>
  );
};

export default Assinatura;
