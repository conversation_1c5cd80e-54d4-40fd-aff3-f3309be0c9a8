import { useAporteServiceContext } from '@src/features/financeiro/aporte/exports';

type TUseDataDebito = () => {
  datasDeDebitoPossiveis: string[];
  datasDeDebitoPossiveisLoading: boolean;
};

export const useDataDebito: TUseDataDebito = () => {
  const { datasDebito } = useAporteServiceContext();

  const datasDeDebitoPossiveis = datasDebito.response;
  const datasDeDebitoPossiveisLoading = datasDebito.loading;

  return { datasDeDebitoPossiveis, datasDeDebitoPossiveisLoading };
};
