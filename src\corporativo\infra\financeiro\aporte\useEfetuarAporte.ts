import {
  IEfetuarAportePayload,
  IEfetuarAporteResponse,
  PECOS,
  TUseEfetuarAporte,
  useApiGatewayCvpInvoker,
} from '@src/features/financeiro/aporte/exports';

export const useEfetuarAporte = (): TUseEfetuarAporte => {
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    IEfetuarAportePayload,
    IEfetuarAporteResponse
  >(PECOS.EfetuarAporte, {
    autoFetch: false,
    handleResponse: { throwToastErrorBFF: false },
  });

  return {
    loading,
    invocarApiGatewayCvpComToken,
  };
};
