import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import {
  IDadosExtrato,
  IUseFetchExtratoRentabilidadeCotasPayload,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchExtratoRentabilidadeCotas';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

interface IFetchExtratoRentabilidadeCotasEntidade {
  dadosExtrato: IDadosExtrato[];
}

interface IUseFetchExtratoRentabilidadeCotasReturn {
  isLoadingExtratoRentabilidadeCotas: boolean;
  fetchExtratoRentabilidadeCotas: () => Promise<
    IHandleReponseResult<IFetchExtratoRentabilidadeCotasEntidade> | undefined
  >;
}

const useFetchExtratoRentabilidadeCotas = (
  payload?: IUseFetchExtratoRentabilidadeCotasPayload,
): IUseFetchExtratoRentabilidadeCotasReturn => {
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    IUseFetchExtratoRentabilidadeCotasPayload,
    IFetchExtratoRentabilidadeCotasEntidade
  >(PECOS.ConsultarDadosExtratoCotas, {
    data: payload,
    autoFetch: false,
  });

  return {
    isLoadingExtratoRentabilidadeCotas: loading,
    fetchExtratoRentabilidadeCotas: invocarApiGatewayCvpComToken,
  };
};

export default useFetchExtratoRentabilidadeCotas;
