import {
  Button,
  Dialog,
  IconDeleteOutlinedRound,
  S,
  Text,
  TModalExcluirBeneficiarioProps,
} from '../exports';

export const ModalExcluirBeneficiario: React.FC<
  TModalExcluirBeneficiarioProps
> = ({ index, removerBeneficiario }) => {
  return (
    <Dialog>
      <Dialog.Trigger asChild>
        <S.DeleteButton
          variant="secondary-outlined"
          type="button"
          leftIcon={<IconDeleteOutlinedRound size="medium" />}
        />
      </Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Header variant="highlight">
          <Text variant="heading-small-600">Excluir beneficiário</Text>
        </Dialog.Header>
        <Dialog.Body>
          <Text variant="text-big-400">
            Tem certeza que deseja excluir esse beneficiário?
          </Text>
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.Cancel asChild>
            <Button variant="secondary-outlined">Cancelar</Button>
          </Dialog.Cancel>
          <Dialog.Action onClick={() => removerBeneficiario(index)} asChild>
            <Button>Excluir</Button>
          </Dialog.Action>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};
