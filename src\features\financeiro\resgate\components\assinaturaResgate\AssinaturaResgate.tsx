import * as Resgate from '@src/features/financeiro/resgate/exports';

export const AssinaturaResgate = ({
  obterAssinatura,
  podeExibirAssinatura,
}: Resgate.IAssinaturaResgateProps): React.ReactElement => {
  const { certificadoAtivo } = Resgate.useContext(Resgate.PrevidenciaContext);

  const cpfCnpj = String(Resgate.getSessionItem('cpfCnpj'));

  return (
    <Resgate.ConditionalRenderer condition={podeExibirAssinatura}>
      <Resgate.GridItem xs="1" lg="2/3">
        <Resgate.Text
          variant="text-standard-600"
          fontColor="content-neutral-05"
          marginTop="20px"
          marginBottom="20px"
        >
          Autenticação
        </Resgate.Text>

        <Resgate.Assinatura
          dados={{
            cpfCnpj: Resgate.tryGetValueOrDefault([cpfCnpj], ''),
            numeroCertificado: Resgate.tryGetValueOrDefault(
              [certificadoAtivo?.certificadoNumero],
              '',
            ),
          }}
          callback={obterAssinatura}
        />
      </Resgate.GridItem>
    </Resgate.ConditionalRenderer>
  );
};
