import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import {
  checkIfSomeItemsAreTrue,
  getSessionItem,
  tryGetValueOrDefault,
} from '@cvp/utils';
import { TFundoDestinos } from '@src/corporativo/types/transferencias';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useContext } from 'react';
import { TDefinirReservaEntidade } from '@src/corporativo/types/consultaCertificado/Response/IDefinirReservaDestino';

type TPayload = {
  cpf: string;
};

export type TUseDefineReservaDestino = {
  loading: boolean;
  response: IHandleReponseResult<TDefinirReservaEntidade> | undefined;
  fetchData: (
    fundosDestinos: Record<string, TFundoDestinos> | TFundoDestinos[],
    numTransferencia: string,
  ) => Promise<IHandleReponseResult<TDefinirReservaEntidade> | undefined>;
};

function useDefineReservaDestino(): TUseDefineReservaDestino {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const payload = {
    cpf: getSessionItem('cpfCnpj'),
  };

  const { response, invocarApiGatewayCvpComToken, loading } =
    useApiGatewayCvpInvoker<TPayload, TDefinirReservaEntidade>(
      'PECO_DefinirReservaDestino',
      {
        data: { cpf: String(payload.cpf) },
        autoFetch: false,
      },
    );

  const obtemValoresDestino = (
    fundosDestinos: Record<string, TFundoDestinos> | TFundoDestinos[],
  ) => {
    if (Array.isArray(fundosDestinos)) return fundosDestinos;

    return Object.keys(fundosDestinos).map(chave => fundosDestinos[chave]);
  };

  const defineReservasDestino = async (
    fundosDestinos: Record<string, TFundoDestinos> | TFundoDestinos[],
    numTransferencia: string,
  ) => {
    const destinos = obtemValoresDestino(fundosDestinos);

    const result = await invocarApiGatewayCvpComToken({
      codContaOrigem: certificadoAtivo?.certificadoNumero,
      numTransferencia,
      reservasDestino: destinos.map(
        ({ codFundo, codObjetivo, codReserva, transferenciaValor }) => ({
          codFundo,
          codObjetivo,
          codReserva,
          vlrSaldo: transferenciaValor,
        }),
      ),
    });

    if (checkIfSomeItemsAreTrue([!result?.sucessoBFF, !result?.sucessoGI])) {
      const [msgErroExcessao] = tryGetValueOrDefault(
        [result?.mensagens],
        [{ descricao: 'Erro ao tentar definir reserva', codigo: '' }],
      );
      throw new Error(msgErroExcessao.descricao);
    }

    return result;
  };

  return {
    response,
    fetchData: defineReservasDestino,
    loading,
  };
}

export default useDefineReservaDestino;
