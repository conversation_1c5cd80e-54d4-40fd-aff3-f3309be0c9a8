import {
  Ds,
  CONSTANTES,
  Styles,
  Fac,
  Utils,
  Types,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

export const ExplicacaoConteudo: React.FC<Types.TExplicacaoConteudoProps> = ({
  tipo,
}) => {
  const conteudo = Fac.obterExplicacaoReajusteAnualPlano(tipo);

  return (
    <Styles.ExplicacaoContent>
      <Ds.Text variant="text-standard-700">
        {Utils.getTernaryResult(
          tipo === CONSTANTES.REAJUSTE_TIPOS.REAJUSTE,
          CONSTANTES.TEXTOS.REAJUSTE_TITULO,
          CONSTANTES.TEXTOS.REENQUADRAMENTO_TITULO,
        )}
      </Ds.Text>

      {conteudo.map(item => (
        <span key={item.valor}>
          <Styles.Typography key={item.valor} variant="text-standard-700">
            {item.titulo}
          </Styles.Typography>
          <Styles.Typography variant="text-standard-400" display="inline">
            {item.valor}
          </Styles.Typography>
        </span>
      ))}
    </Styles.ExplicacaoContent>
  );
};
