import { useState } from 'react';
import { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';

export interface IUseValidarAssinaturaReturn {
  assinaturaValida: boolean;
  validarAssinatura: (response: IAssinaturaResponse) => boolean;
}

const useValidarAssinatura = (): IUseValidarAssinaturaReturn => {
  const [assinaturaValida, setAssinaturaValida] = useState(false);

  const validarAssinatura = (response: IAssinaturaResponse) => {
    const isValida = response.resposta?.success === true;
    setAssinaturaValida(isValida);
    return isValida;
  };

  return { assinaturaValida, validarAssinatura };
};

export default useValidarAssinatura;
