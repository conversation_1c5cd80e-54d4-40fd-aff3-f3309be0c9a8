import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

export const useAlterarConta = (): DadosPagamento.TUseAlterarConta => {
  const [adicionarNovaConta, setAdicionarNovaConta] =
    DadosPagamento.useState(false);
  const [deveValidarConta, setDeveValidarConta] =
    DadosPagamento.useState(false);
  const [contaExistente, setContaExistente] = DadosPagamento.useState('');
  const [novaConta, setNovaConta] =
    DadosPagamento.useState<DadosPagamento.TNovaConta>(
      DadosPagamento.STATE_INICIAL_NOVA_CONTA,
    );

  const handleAlterarContaExistente = (
    selectedOption: DadosPagamento.SelectItem[],
  ): void => {
    const temAlteracoes =
      selectedOption[0]?.value === DadosPagamento.TEXT_NOVA_CONTA;

    setAdicionarNovaConta(temAlteracoes);
    setDeveValidarConta(temAlteracoes);
    setContaExistente(selectedOption[0]?.value);
  };

  const handleNovaContaChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ): void => {
    const { name, value } = e.target;
    setNovaConta(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const resetNovaContaPorSucesso = (
    params: DadosPagamento.TResetarContaPorResponseParams,
  ): void => {
    const conta = DadosPagamento.resetarContaPorResponse(params);

    setNovaConta(prev => ({ ...prev, ...conta }));
  };

  return {
    adicionarNovaConta,
    deveValidarConta,
    contaExistente,
    novaConta,
    setNovaConta,
    handleNovaContaChange,
    handleAlterarContaExistente,
    resetNovaContaPorSucesso,
  };
};
