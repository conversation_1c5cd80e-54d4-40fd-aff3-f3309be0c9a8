import {
  checkIfAllItemsAreTrue,
  Divider,
  FiltroHistoricoSolicitacoes,
  formatarListagemComFiltros,
  formatarTipoSolicitacoes,
  Grid,
  ListaHistoricoSolicitacoes,
  LoadingSpinner,
  Match,
  Styles as S,
  SwitchCase,
  Text,
  TEXTOS,
  useFiltroHistoricoSolicitacao,
  useObterFiltroTipoSolicitacao,
  useObterHistoricoSolicitacao,
} from '@src/features/consulta/historicoSolicitacoes/exports';

export const HistoricoSolicitacoes: React.FC = () => {
  const obterTiposServico = useObterFiltroTipoSolicitacao();
  const obterHistoricoSolicitacoes = useObterHistoricoSolicitacao();
  const filtroControll = useFiltroHistoricoSolicitacao();

  return (
    <S.Container>
      <S.Header>
        <Text variant="text-standard-400">{TEXTOS.CABECALHO_TITULO}</Text>

        <Divider />
      </S.Header>

      <S.Content>
        <FiltroHistoricoSolicitacoes
          loading={obterHistoricoSolicitacoes.loading}
          controll={filtroControll}
          onSubmit={filtros => obterHistoricoSolicitacoes.fetcher(filtros)}
          tiposSolicitacoes={formatarTipoSolicitacoes(
            obterTiposServico.response,
          )}
        />

        <SwitchCase>
          <Match when={obterHistoricoSolicitacoes.loading}>
            <Grid margin="30px" justify="center">
              <LoadingSpinner size="medium">Carregando...</LoadingSpinner>
            </Grid>
          </Match>

          <Match
            when={checkIfAllItemsAreTrue([
              !obterHistoricoSolicitacoes.loading,
              !!filtroControll.filtros.filtroAtivo,
            ])}
          >
            <ListaHistoricoSolicitacoes
              assinaturaSucessoCallback={() =>
                filtroControll.onClickConsultar(
                  obterHistoricoSolicitacoes.fetcher,
                )
              }
              erroPeriodo={filtroControll.erroPeriodo}
              dados={formatarListagemComFiltros(
                obterHistoricoSolicitacoes.response,
                filtroControll.filtros,
              )}
            />
          </Match>
        </SwitchCase>
      </S.Content>
    </S.Container>
  );
};
