import {
  IFundosParaResgateFactory,
  ICalcularValorResgateRestanteRetorno,
} from '@src/features/financeiro/resgate/exports';

export interface IUseSimulacaoResgate {
  isTipoResgateParcial: boolean;
  fundosParaResgate: IFundosParaResgateFactory[];
  resultadoCalculoResgateRestante: ICalcularValorResgateRestanteRetorno;
  isDisabledBtnSimulacaoResgate: boolean;
  selecionarTipoResgate: (tipoResgateSelecionado: string) => void;
}
