export interface IAnoHistorico {
  codTipoAtualizacao: string;
  desTipoAtualizacao: string;
  dthRecal: string;
  nomCobertura: string;
  desPagamento: string;
  vlrAnterior: string;
  codIndice: string;
  pctAtualizacao: string;
  vlrFinal: string;
}

export interface IResponseDetalhesHistoricoAno {
  retornoAnoHistoricoProxy: {
    anosHistorico: IAnoHistorico[];
    staMensagem: string;
  };
}

export interface IResponseHistoricoAtualizacoes {
  numAno: string;
  vlrContribuicaoAnterior: string;
  vlrContribuicaoReajustada: string;
}

export interface IUseObterHistoricoAtualizacoes {
  loading: boolean;
  response: IResponseHistoricoAtualizacoes[];
}

export interface IUseObterHistoricoAtualizacoesPorAno {
  loading: boolean;
  response: IAnoHistorico[];
  obterHistoricoPorAno: (ano: string) => Promise<void>;
}

export interface IObterHistoricoPayload {
  cpfCnpj: string;
  numeroCertificado: string;
  cpf?: string;
}

export interface IObterHistoricoPorAnoPayload
  extends Partial<IObterHistoricoPayload> {
  ano?: string;
}
