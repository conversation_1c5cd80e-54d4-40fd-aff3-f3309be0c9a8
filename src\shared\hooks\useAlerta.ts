import {
  AlertasContext,
  IAlertaIdentificado,
  IAlerta,
} from '@src/corporativo/context/AlertasContext';
import { useContext } from 'react';

type TUseAlertaEdicao = () => {
  alterarAlerta: (alerta: IAlertaIdentificado) => void;
};
type TUseAlertaConsulta = (identificador: string) => {
  alerta: IAlerta | null;
  callbackAlerta: VoidFunction;
};

export const useAlertaConsulta: TUseAlertaConsulta = identificador => {
  const { alertas, setAlertas } = useContext(AlertasContext);

  const alertaConsultado = identificador ? alertas[identificador] : null;

  const callbackAlerta = () => {
    const alertasGlobais = { ...alertas };
    delete alertasGlobais[identificador];
    setAlertas(alertasGlobais);
  };

  return {
    alerta: alertaConsultado,
    callbackAlerta,
  };
};

export const useAlertaEdicao: TUseAlertaEdicao = () => {
  const { setAlertas } = useContext(AlertasContext);

  const alterarAlerta = (alerta: IAlertaIdentificado) => {
    const { identificador: idAlerta, ...dadosAlerta } = alerta;
    setAlertas(state => ({
      ...state,
      [idAlerta]: dadosAlerta,
    }));
  };
  return { alterarAlerta };
};
