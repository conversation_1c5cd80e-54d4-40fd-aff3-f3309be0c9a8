import {
  checkIfSomeItemsAreTrue,
  getSessionItem,
  tryGetValueOrDefault,
} from '@cvp/utils';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { IUseListarSinistro } from '@src/corporativo/types/consultaSinistro/IUseListarSinistro';
import { IDadosSinistroResponse } from '@src/corporativo/types/consultaSinistro/IDadosSinistroResponse';
import { IPayloadSinistro } from '@src/corporativo/types/consultaSinistro/IPayloadSinistro';
import { CODIGO_EMPRESA } from '@src/shared/constants/codigoEmpresa';
import { PECOS } from '../config/api/endpoints';

const THREE_MINUTES_CACHE = 3 * 1000 * 60;

export const useListarSinistro: IUseListarSinistro = () => {
  const cpfCNpj: string = getSessionItem('cpfCnpj') ?? '';
  const { response, loading } = useApiGatewayCvpInvoker<
    IPayloadSinistro,
    IDadosSinistroResponse
  >(PECOS.StatusSinistroPrevidencia, {
    data: {
      cpfTitular: cpfCNpj,
      codigoEmpresa: CODIGO_EMPRESA,
    },
    autoFetch: true,
    cache: true,
    cacheKey: `${cpfCNpj}-sinistros`,
    cacheTime: THREE_MINUTES_CACHE,
  });

  return {
    response: tryGetValueOrDefault(
      [response?.entidade?.dados?.andamentosCertificados],
      [],
    ),
    error: checkIfSomeItemsAreTrue([
      !response?.sucessoBFF,
      !response?.sucessoGI,
      !response?.entidade?.sucesso,
    ]),
    loading: loading || response === undefined,
  };
};
