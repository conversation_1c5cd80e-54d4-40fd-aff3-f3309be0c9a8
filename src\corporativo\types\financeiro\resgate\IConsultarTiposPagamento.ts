import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IConsultarTiposPagamentoPayload {
  numeroResgate: string;
}

export interface IConsultarTiposPagamentoTipo {
  nomeCanal: string;
  codigoBanco: string;
  tipoCanal: string;
  codigoContaBancaria: string;
  digitoAgencia: string;
  digitoConta: string;
  tipoContaBancaria: string;
  descricaoTipoContaBancaria: string;
  nomeBanco: string;
  numeroAgencia: string;
}

export interface IConsultarTiposPagamentoResponse {
  tipoPagamentos: IConsultarTiposPagamentoTipo[];
}

export interface IUseConsultarTiposPagamentoReturn {
  tiposPagamento: IConsultarTiposPagamentoResponse;
  isLoadingTiposPagamento: boolean;
  consultarTiposPagamento: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<IConsultarTiposPagamentoResponse> | undefined
  >;
}
