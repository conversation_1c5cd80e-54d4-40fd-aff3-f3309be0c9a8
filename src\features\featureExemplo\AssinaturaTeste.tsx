import { Match, SwitchCase } from '@cvp/componentes-posvenda';
import {
  checkIfAllItemsAreTrue,
  setSessionItem,
  tryGetValueOrDefault,
} from '@cvp/utils';
import Assinatura from '@src/corporativo/components/Assinatura/Assinatura';
import { useRegistrarTokenAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useRegistrarTokenAssinaturaCaixa';
import { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
import useValidarAssinatura from '@src/shared/hooks/useValidarAssinatura';
import React, { useContext } from 'react';
import { PrevidenciaContext } from '../layoutPrevidencia/exports';

const AssinaturaTeste: React.FC = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { assinaturaValida, validarAssinatura } = useValidarAssinatura();
  const { invocarApiGatewayCvpComToken } = useRegistrarTokenAssinaturaCaixa();

  const assinaturaCallback = (response: IAssinaturaResponse) => {
    setSessionItem('assinaturaResponse', JSON.stringify(response));
    invocarApiGatewayCvpComToken(response);
    validarAssinatura(response);
  };

  return (
    <>
      <SwitchCase fallback={undefined}>
        <Match
          when={
            !assinaturaValida &&
            checkIfAllItemsAreTrue([!!certificadoAtivo?.numeroApolice])
          }
        >
          Não foi possível carregar a assinatura.
        </Match>
      </SwitchCase>
      <Assinatura
        dados={{
          cpfCnpj: '',
          numeroCertificado: tryGetValueOrDefault(
            [certificadoAtivo?.certificadoNumero],
            '',
          ),
        }}
        callback={assinaturaCallback}
      />
    </>
  );
};

export default AssinaturaTeste;
