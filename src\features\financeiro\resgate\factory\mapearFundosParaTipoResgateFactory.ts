import {
  getTernaryResult,
  IListarFundosParaResgateFundosDisponiveis,
  IMapearFundosParaTipoResgateFactory,
} from '@src/features/financeiro/resgate/exports';

/**
 * Mapear fundos de acordo com o tipo de resgate selecionado
 *
 * @param {Object} params - Parâmetros para mapear os fundos
 * @param {IListarFundosParaResgateFundosDisponiveis[]} params.fundos - Lista de fundos disponíveis para resgate
 * @param {string} params.tipoResgate - Tipo de resgate selecionado (total ou parcial)
 * @param {boolean} params.isTipoTotalSelecionado - Indica se o tipo de resgate total está selecionado
 * @returns {IListarFundosParaResgateFundosDisponiveis[]} Lista de fundos atualizada com seleção e valores baseados no tipo de resgate
 */
export const mapearFundosParaTipoResgateFactory = ({
  fundos,
  tipoResgate,
  isTipoTotalSelecionado,
}: IMapearFundosParaTipoResgateFactory): IListarFundosParaResgateFundosDisponiveis[] => {
  return fundos.map(fundo => ({
    ...fundo,
    selecionado: isTipoTotalSelecionado,
    tipoResgate,
    valorRetirar: getTernaryResult(
      isTipoTotalSelecionado,
      fundo.saldoTotal.toString(),
      '',
    ),
  }));
};
