import { IBeneficios } from '../exports';

export const mockObterCoberturas: IBeneficios = {
  planoId: '123456',
  coberturaId: '1234567',
  empresaId: '',
  descricaoCobertura: '',
  tipoBeneficio: '',
  situacao: 'Ativo',
  subSituacao: '',
  valorBeneficio: '2000.00',
  valorContribuicao: '150.00',
  numCnpjFundo: '',
  certificado: {
    empresaId: '1245678',
    numCertificado: '123456789',
  },
  termoDesejado: '',
  codTipoPagamentoOriginal: '',
  nomTipoPagamento: 'Mensal',
  fatorRenda: '1.5',
  vlrReversao: '500.00',
  descBeneficiarioRecebeOriginal: '',
  beneficiarios: [
    {
      tipoBeneficiario: 'Dependente',
      pessoaFisica: {
        pessoaFisicaId: 'PF123456',
        genero: 'Feminino',
        dataNascimento: new Date('1990-01-01').toISOString(),
        nome: '<PERSON>',
        cpfCnpj: '123.456.789-00',
      },
      percentualDistribuicao: '50',
      situacao: 'Ativo',
      grauParentescoId: '1',
      descricaoGrauParentesco: 'Cônjuge',
    },
    {
      tipoBeneficiario: 'Filho',
      pessoaFisica: {
        pessoaFisicaId: 'PF654321',
        genero: 'Masculino',
        dataNascimento: new Date('2015-05-05').toISOString(),
        nome: 'João da Silva',
        cpfCnpj: '987.654.321-00',
      },
      percentualDistribuicao: '50',
      situacao: 'Ativo',
      grauParentescoId: '2',
      descricaoGrauParentesco: 'Filho',
    },
  ],
  tipoCobertura: 'Individual',
  descPeridoBeneficiarioRecebe: 'Durante a vigência do plano',
};
