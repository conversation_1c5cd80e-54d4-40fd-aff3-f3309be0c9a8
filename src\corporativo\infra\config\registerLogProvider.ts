import { obterRotasMapeadas } from '@cvp/componentes-posvenda';
import {
  configureLogging,
  IConfigureLogging,
  Logger,
} from '@cvp/logging-extensions';
import { getLocalItem, setLocalItem, tryGetValueOrDefault } from '@cvp/utils';
import { ROUTE_LIST } from '@src/corporativo/routes';
import api from '@src/corporativo/infra/config/api/axiosConfig';
import { IConfigurationBuilder } from '@cvp/logging-extensions/interfaces/IConfigurationBuilder';
import {
  LOGGER_EVENTS,
  LOGGER_TELEMETRY_KEYS,
} from '@src/corporativo/constants/logProvider';
import { obterContextoAutenticacao } from '@src/corporativo/factories/obterContextoAutenticacao';

export const registerLogProvider = (): IConfigurationBuilder => {
  const builder = configureLogging({
    getItem: getLocalItem,
    setItem: setLocalItem,
    axiosInstance: api as IConfigureLogging['axiosInstance'],
  });

  const { userId, sessionId, sessionNavigationId } =
    obterContextoAutenticacao();

  const commonProperties = {
    applicationContext: {
      name: AppConfig.REACT_APP_NOME_MFE,
    },
    sessionId: tryGetValueOrDefault([sessionId], ''),
    sessionNavigationId: tryGetValueOrDefault([sessionNavigationId], ''),
  };

  builder.addApplicationInsights({
    ...commonProperties,
    connectionString: AppConfig.REACT_APP_INSIGHTS_CONNECTION_STRING,
    ...(!!userId && {
      addTelemetryCallback: () => ({
        [LOGGER_TELEMETRY_KEYS.USER_ID]: userId,
      }),
    }),
  });

  const logger = Logger.getInstance();

  logger.mapHttpEvents(LOGGER_EVENTS.REQUEST_EVENTS);
  logger.mapRouterEvents(LOGGER_EVENTS.ROUTER_EVENTS);

  builder.addNavigationInterceptor(obterRotasMapeadas(ROUTE_LIST));

  return builder;
};
