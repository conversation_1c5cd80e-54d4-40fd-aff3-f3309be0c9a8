import {
  FILTRO_APORTE,
  formatarValorPadraoBrasileiro,
  GridItem,
  IFiltroTabelaAporte,
  InputText,
  Text,
  TFormikProps,
  useValorContribuicao,
  valoresMonetarios,
} from '@src/features/financeiro/aporte/exports';

interface IValorContribuicao {
  formik: TFormikProps<IFiltroTabelaAporte>;
}

const ValorContribuicao: React.FC<IValorContribuicao> = ({ formik }) => {
  const { handleBlur } = useValorContribuicao(formik);
  return (
    <GridItem xs="1 / 4">
      <Text variant="text-standard-600" fontColor="content-neutral-05">
        {FILTRO_APORTE.valorDaContribuicao}
      </Text>
      <InputText
        name="valorContribuicao"
        type="text"
        variant="box-classic"
        onBlur={handleBlur}
        onChange={event =>
          formik.setFieldValue(
            'valorContribuicao',
            parseFloat(valoresMonetarios.unmask(event.target.value)) / 100,
          )
        }
        arialabel={FILTRO_APORTE.valorDaContribuicao}
        placeholder={FILTRO_APORTE.defaultValorContribuicao}
        value={valoresMonetarios.mask(formik.values.valorContribuicao)}
        error={!!formik.errors.valorContribuicao}
        size="standard"
        aria-label="Digite o valor de contribuição"
        max={15}
        required
      />
      <Text variant="text-small-400" fontColor="content-neutral-03">
        Valor mínimo: {formatarValorPadraoBrasileiro(50)}
      </Text>
    </GridItem>
  );
};

export default ValorContribuicao;
