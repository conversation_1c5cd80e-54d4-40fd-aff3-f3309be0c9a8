export { For } from '@cvp/componentes-posvenda';
export { MultiAccordion, Text } from '@cvp/design-system-caixa';
export { getTernaryResult } from '@cvp/utils';
export { Outlet, useNavigate } from 'react-router-dom';

export { ENVIRONMENTS_NAME } from '@src/shared/constants/environments-names';

export { useAccordionRouter } from '../hooks/useAccordionRouter';

export type { IUseAccordionRouterReturn } from '../routes/types/IUseAccordionRouterReturn';
