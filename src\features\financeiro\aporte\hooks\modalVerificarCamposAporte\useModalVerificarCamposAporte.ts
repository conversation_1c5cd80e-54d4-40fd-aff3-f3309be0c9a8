import {
  checkIfSomeItemsAreTrue,
  EEtapasAporte,
  FORMA_PAGAMENTO,
  getTernaryResult,
  ICertificadoPrevidenciaResponse,
  IDadosPreenchidos,
  IFiltroTabelaAporte,
  TFormikProps,
  tryGetValueOrDefault,
  useAporteContext,
  useAporteServiceContext,
  useValidarAporteFormik,
} from '@src/features/financeiro/aporte/exports';

type TUseModalVerificarCamposAporte = () => {
  formik: TFormikProps<IDadosPreenchidos>;
  disabledForm: boolean;
  certificado: ICertificadoPrevidenciaResponse | undefined;
  validarCamposAporteLoading: boolean;
  etapa: EEtapasAporte;
  filtroTabelaFundos: IFiltroTabelaAporte;
  handleProximaEtapa: () => Promise<void>;
  setEtapa: React.Dispatch<React.SetStateAction<EEtapasAporte>>;
};

export const useModalVerificarCamposAporte: TUseModalVerificarCamposAporte =
  () => {
    const { etapa, filtroTabelaFundos, setEtapa } = useAporteContext();

    const { validarCamposAporte } = useAporteServiceContext();

    const { formik, certificado } = useValidarAporteFormik();

    const determinarNovaEtapa = (
      formaPagamento: string,
      camposValidos: boolean,
    ): EEtapasAporte => {
      if (!camposValidos) {
        return EEtapasAporte.ModalVerificarCamposAporte;
      }

      const formaPagamentoUpper = formaPagamento.toUpperCase();
      const boletoUpper = FORMA_PAGAMENTO.boleto.toUpperCase();

      return getTernaryResult(
        formaPagamentoUpper === boletoUpper,
        EEtapasAporte.ModalConfirmarAporte,
        EEtapasAporte.ContaDebitoAporte,
      );
    };

    const handleProximaEtapa = async () => {
      try {
        const response = await validarCamposAporte.invocarApiGatewayCvpComToken(
          {
            nomeParticipante: formik.values.primeiroNome.toLowerCase(),
            cpfParticipante: formik.values.finalCpfCnpj,
          },
        );

        const camposValidos = tryGetValueOrDefault(
          [response?.entidade?.camposValidos],
          false,
        );

        const novaEtapa = determinarNovaEtapa(
          filtroTabelaFundos.formaPagamento,
          camposValidos,
        );

        setEtapa(novaEtapa);
      } catch (error) {
        setEtapa(EEtapasAporte.ModalVerificarCamposAporte);
      }
    };

    const disabledForm = checkIfSomeItemsAreTrue([
      formik.values.primeiroNome === '',
      formik.values.finalCpfCnpj === '',
      !!formik.errors.primeiroNome,
      !!formik.errors.finalCpfCnpj,
      validarCamposAporte.loading,
    ]);

    return {
      formik,
      disabledForm,
      certificado,
      validarCamposAporteLoading: validarCamposAporte.loading,
      etapa,
      filtroTabelaFundos,
      handleProximaEtapa,
      setEtapa,
    };
  };
