export const VALIDACOES = {
  MENSAGEM_INVALIDO_CEP: 'CEP inválido',
  MENSAGEM_OBRIGATORIO_CEP: 'CEP não informado',
  MENSAGEM_INVALIDO_ENDERECO: 'Endereço inválido',
  MENSAGEM_OBRIGATORIO_ENDERECO: 'Endereço não informado',
  MENSAGEM_INVALIDO_BAIRRO: 'Bairro inválido',
  MENSAGEM_OBRIGATORIO_BAIRRO: 'Bairro não informado',
  MENSAGEM_OBRIGATORIO_CIDADE: 'Cidade não informada',
  MENSAGEM_INVALIDO_EMAIL: 'Email inválido',
  MENSAGEM_OBRIGATORIO_EMAIL: 'Email não informado',
  MENSAGEM_MAX_LENGTH_EMAIL: ' O e-mail deve ter no máximo 40 caracteres',
  MENSAGEM_INVALIDO_DATA_NASCIMENTO: 'Data de nascimento inválida',
  MENSAGEM_OBRIGATORIO_DATA_NASCIMENTO: 'Data de nascimento não informada',
  MENSAGEM_OBRIGATORIO_TELEFONE: 'Telefone não informado',
  MENSAGEM_OBRIGATORIO_CELULAR: 'Celular não informado',
  MENSAGEM_OBRIGATORIO_NUMERO: 'Número não informado',
  MENSAGEM_QUANTIDADE_CARACTERES:
    'Quantidade de caracteres inválida para esse campo',
};

export const MENSAGEM_SUCESSO = 'Dados do participante editados com sucesso!';
export const MENSAGEM_ERRO = 'Erro ao editar dados do participante';

export const DISCLAIMER_CONSENTIMENTO =
  'Observação: Caso Seja realizada marcação nas opções acima referentes à LGPD (Gestão de Consentimento), ao clicar em CONFIRMAR, os dados não são efetivados automaticamente, sendo necessário aguardar prazo de no mínimo 30 minutos.';

export const LABEL_CONSENTIMENTO_DADOS_CAIXA =
  'O cliente permite que seus dados pessoais possam ser utilizados para ofertar outros produtos da Caixa Vida e Previdência e empresas coligadas.';

export const LABEL_CONSENTIMENTO_DADOS_PARCEIROS =
  'O cliente permite que seus dados possam ser compartilhados com parceiros de negócio para recebimento de ofertas de produtos.';
