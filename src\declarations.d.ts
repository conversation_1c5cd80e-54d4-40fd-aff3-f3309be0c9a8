declare module '*.html' {
  const rawHtmlFile: string;
  export = rawHtmlFile;
}

declare module '*.bmp' {
  const src: string;
  export default src;
}

declare module '*.gif' {
  const src: string;
  export default src;
}

declare module '*.jpg' {
  const src: string;
  export default src;
}

declare module '*.jpeg' {
  const src: string;
  export default src;
}

declare module '*.png' {
  const src: string;
  export default src;
}

declare module '*.webp' {
  const src: string;
  export default src;
}

declare module '*.svg' {
  const src: string;
  export default src;
}

declare const AppConfig: {
  CLIENT_ID: string;
  NOME_ACESSO_STORE_KEY: string;
  BEARER_TOKEN_STORE_KEY: string;
  USER_METADATA_STOREY_KEY: string;
  CACHE_DURATION: string;
  API_BASE_URL: string;
  MFE_ENV: string;
  PE_ENV: string;
  CHAT_BASE_URL: string;
  WEBCHAT_BASE_URL: string;
  REACT_APP_NOME_MFE: string;
  REACT_APP_MFE_ASSINATURA_NAME: string;
  REACT_APP_INSIGHTS_CONNECTION_STRING: string;
};
