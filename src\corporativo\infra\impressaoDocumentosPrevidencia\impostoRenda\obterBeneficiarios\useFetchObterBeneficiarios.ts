import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IPECOObterBeneficiariosEntity,
  IUseFetchObterBeneficiariosPayload,
  IUseFetchObterBeneficiariosReturn,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchObterBeneficiarios';

const useFetchObterBeneficiarios = (
  payload?: IUseFetchObterBeneficiariosPayload,
): IUseFetchObterBeneficiariosReturn => {
  const { invocarApiGatewayCvpComToken, loading } = useApiGatewayCvpInvoker<
    IUseFetchObterBeneficiariosPayload,
    IPECOObterBeneficiariosEntity[]
  >(PECOS.ObterBeneficiarios, {
    data: payload,
    autoFetch: false,
  });

  return {
    isLoadingObterBeneficiarios: loading,
    fetchObterBeneficiarios: invocarApiGatewayCvpComToken,
  };
};

export default useFetchObterBeneficiarios;
