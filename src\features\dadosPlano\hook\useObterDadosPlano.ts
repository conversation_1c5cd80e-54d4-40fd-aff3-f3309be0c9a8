import { useContext } from 'react';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';

import {
  calcularDiferencaAnos,
  TDadosPlanoResponse,
  replaceDate,
  useDadosPlano,
  UTILS,
} from '../exports';

interface IUseObterDadosPlanoProps {
  response: TDadosPlanoResponse;
  loading: boolean;
  calcularDuracao: (dataFinal: string, dataInicioVigencia: string) => string;
}

export function useObterDadosPlano(): IUseObterDadosPlanoProps {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const { loading, response } = useDadosPlano({
    cpf: UTILS.tryGetValueOrDefault(
      [String(UTILS.getSessionItem('cpfCnpj'))],
      '',
    ),
    NumeroCertificado: UTILS.tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    ),
  });

  const calcularDuracao = (
    dataFinal: string,
    dataInicioVigencia: string,
  ): string => {
    const finalVigencia = replaceDate({ data: dataFinal });
    const inicioVigencia = replaceDate({ data: dataInicioVigencia });

    const duracao = calcularDiferencaAnos({
      dataInicio: inicioVigencia,
      dataFim: finalVigencia,
    });
    return `${duracao} ${duracao > 1 ? 'anos' : 'ano'}`;
  };

  return {
    response,
    loading,
    calcularDuracao,
  };
}
