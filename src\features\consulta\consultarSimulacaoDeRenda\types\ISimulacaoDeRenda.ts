import { IUseVerUltimasSolicitacoesResponse } from '@src/features/consulta/consultarSimulacaoDeRenda/exports';

export interface ISelectSimulacaoDeRendaProps {
  value: string;
  text: string;
}

export interface IRendaVitaliciaReversivelBeneficiario {
  consultarRenda: () => Promise<void>;
  getOptionsSelectSexoBeneficiario: ISelectSimulacaoDeRendaProps[];

  getOptionsSelectPorcentagemRenda: ISelectSimulacaoDeRendaProps[];
}

export interface ISimulacaoDeRendaTemporariaPrazoCerto {
  consultarRenda: () => Promise<void>;

  income: {
    renda: string;
    numeroDaRenda: string;
  };
}

export interface ISimulacaoDeRendaResponse {
  dthInicioGozo: string;
  pctRentabilidadeMinima: string;
  qtdPrazoAnos: string;
  codTipoPagamento: string;
  nomTipoPagamento: string;
  vlrReversao: string;
  vlrReserva: string;
  vlrIRRF: string;
  pctAlicotaIRRF: string;
  vlrBeneficio: string;
  vlrBeneficioLiquido: string;
  dthDiaSimulacaoFormatada: string;
  dthHoraSimulacaoFormatada: string;
  nomCliente: string;
  numDocumento: string;
  nomProduto: string;
  codTipoPagamentoOriginal: string;
  nomTipoPagamentoOriginal: string;
  qtdPrazoAnosOriginal: string;
  dthInicioGozoOriginal: string;
  vlrReservaOriginal: string;
  vlrIRRFOriginal: string;
  pctAlicotaIRRFOriginal: string;
  vlrBeneficioOriginal: string;
  vlrBeneficioLiquidoOriginal: string;
  codError: string;
  descMensagemError: string;
  descBeneficiarioRecebeOriginal: string;
  descPeridoBeneficiarioRecebeOriginal: string;
  descPeriodoAnosBeneficiarioRecebeOriginal: string;
  descPctBeneficiarioRecebeOriginal: string;
  descBeneficiarioRecebe: string;
  descPeridoBeneficiarioRecebe: string;
  descPeriodoAnosBeneficiarioRecebe: string;
  descPctBeneficiarioRecebe: string;
  staGeneroConjuge: string;
  dthNascimentoBeneficiarioFormatada: string;
  tipoRendaMensagemCabecalhoEmail: string;
  mensagemSimulacaoAbaixoPrevisto: string;
  regimeTributarioSimulacao: string;
}

export interface ITabelaCelulaConsulta {
  text: string;
}
export interface ISimulacaoRow {
  label: string;
  progressiva: string;
  regressiva: string;
  estimativa: string;
}
export interface ITableDataConsulta {
  data: ISimulacaoDeRendaResponse;
}

export interface IUltimasSimulacoesRow {
  dataHora: string;
  tipoRenda: string;
  tributacao: string;

  periodoRecebimento: string;
  rendaLiquida: string;
}
export interface ITooltipTableRow {
  aliquotasRegressivas: string;
  prazoDeAcumulacao: string;
}

export interface ITableDataUltimasSolicitacoes {
  data: IUseVerUltimasSolicitacoesResponse[];
}

export interface ISimulacaoPayload {
  tipoRenda: string;
  tipoTributacao: string;
  qtdPrazoAnos?: string;
  pctReversao?: string;
  dthNascimentoConjuge?: string;
  staGeneroConjuge?: string;
  cpfBeneficiario?: string;
  numeroCertificado?: string;
}

export interface IAletsTable {
  income: {
    renda: string;
    numeroDaRenda: string;
  };
  data: object;
}

export interface ISelectTypeRenda {
  handleSetIncomeRenda: (value: ISelectItem[]) => void;

  handleClearValuesData: () => void;
  handleClearUltimasSolicitacoes: () => void;
}

export interface ILoadingTable {
  deveExibirLoading: boolean;
}

export interface IUseConsultarRenda {
  dataUltimasSimulacoes: IUseVerUltimasSolicitacoesResponse[];
  loadingUltimasSimulacoes: boolean;
}

export interface IModalCertificadoNaoElegivel {
  open: boolean;
  tipoDeRegime: string;

  setOpenModalCertificado: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface ISelectItem {
  text: string;
  value: string;
  disabled?: boolean;
}

interface ISimulacaoPayloadValues {
  qtdPrazoAnos: string;
  pctReversao: string;
  dthNascimentoConjuge: string;
  staGeneroConjuge: string;
}

export type TSimulacaoPayloadMap = Record<
  string,
  (values?: ISimulacaoPayloadValues) => ISimulacaoPayload
>;

export type TSimulacaoPayloadValues = {
  qtdPrazoAnos: string;
  pctReversao: string;
  dthNascimentoConjuge: string;
  staGeneroConjuge: string;
};

export interface IUseVerUltimasSimulacoesDeRendaReturn {
  loading: boolean;
  dataUltimasSimulacoes: IUseVerUltimasSolicitacoesResponse[] | undefined;
  handleVerUltimasSimulacoes: () => Promise<void>;
  setDataUltimasSimulacoes: React.Dispatch<
    React.SetStateAction<IUseVerUltimasSolicitacoesResponse[] | undefined>
  >;
  handleClearUltimasSolicitacoes: () => void;
}
