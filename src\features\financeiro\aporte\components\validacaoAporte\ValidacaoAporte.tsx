import {
  Grid,
  GridItem,
  useAporteServiceContext,
  Text,
  TFormikProps,
  IDadosPreenchidos,
  Match,
  InputText,
  VALIDACAO_APORTE,
} from '@src/features/financeiro/aporte/exports';

interface IValidacaoAporte {
  formik: TFormikProps<IDadosPreenchidos>;
}

const ValidacaoAporte: React.FC<IValidacaoAporte> = ({ formik }) => {
  const { validarCamposAporte } = useAporteServiceContext();

  return (
    <Grid>
      <GridItem xs="1/2">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {VALIDACAO_APORTE.nome}
        </Text>
        <InputText
          id="primeiroNome"
          name="primeiroNome"
          type="text"
          variant="box-classic"
          arialabel={VALIDACAO_APORTE.nomeLabel}
          placeholder={VALIDACAO_APORTE.nomeLabel}
          value={formik.values.primeiroNome}
          error={!!formik.errors.primeiroNome}
          disabled={validarCamposAporte.loading}
          onChange={formik.handleChange}
          required
          size="large"
        />
        <Match when={!!formik.errors.primeiroNome}>
          <Text variant="text-small-400" fontColor="content-danger-04">
            {formik.errors.primeiroNome}
          </Text>
        </Match>
      </GridItem>
      <GridItem xs="1/2">
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {VALIDACAO_APORTE.cpf}
        </Text>
        <InputText
          id="finalCpfCnpj"
          name="finalCpfCnpj"
          type="text"
          variant="box-classic"
          placeholder={VALIDACAO_APORTE.cpfLabel}
          arialabel={VALIDACAO_APORTE.cpfLabel}
          value={formik.values.finalCpfCnpj}
          error={!!formik.errors.finalCpfCnpj}
          disabled={validarCamposAporte.loading}
          onChange={formik.handleChange}
          maxLength={4}
          required
          size="large"
        />
        <Match when={!!formik.errors.finalCpfCnpj}>
          <Text variant="text-small-400" fontColor="content-danger-04">
            {formik.errors.finalCpfCnpj}
          </Text>
        </Match>
      </GridItem>
    </Grid>
  );
};

export default ValidacaoAporte;
