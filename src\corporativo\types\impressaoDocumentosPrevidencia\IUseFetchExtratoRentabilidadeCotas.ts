export interface IUseFetchExtratoRentabilidadeCotasPayload {
  cpfCnpj: string;
  dataFinal: string;
  dataInicial: string;
  numeroCertificado: string;
  queryKey: [string, string];
  signal: object;
}

export interface IDadosExtrato {
  codigoFundo: string;
  valorMotivo: string;
  valorCota: string;
  valorTotalCota: string;
  valorSaldo: string;
  valorRentabilidade: string;
  dataExtrato: string;
  rentabilidade: string;
  dataCota: string;
  dataMotivo: string;
  nomeMotivo: string;
}
