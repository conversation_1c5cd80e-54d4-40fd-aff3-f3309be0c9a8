export type TNomeOperacaoTipos = 'ALTERACAO' | 'INCLUSAO' | 'EXCLUSAO';

export interface INomeSocialPayload {
  indUsarNomeSocial: 'S' | 'N';
  nomeSocial: string;
  tipoOperacao: TNomeOperacaoTipos;
}

export interface INomeSocialPecoPayload extends INomeSocialPayload {
  cpf: string;
}

export interface IUseNomeSocial {
  response: unknown;
  loading: boolean;
  editarNomeSocial: (payload: INomeSocialPayload) => Promise<boolean>;
}
