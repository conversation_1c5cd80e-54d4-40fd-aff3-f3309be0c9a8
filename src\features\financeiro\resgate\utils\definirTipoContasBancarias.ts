import {
  TIPO_CONTAS_BANCARIAS_CEF,
  TIPO_CONTAS_BANCARIAS_NAO_CEF,
  CODIGO_BANCO_CAIXA,
  ITipoContasBancarias,
} from '@src/features/financeiro/resgate/exports';

/**
 * Define os tipos de contas bancárias disponíveis de acordo com o código do banco
 *
 * A função retorna tipos de contas específicos para a Caixa Econômica Federal
 * ou os tipos padrão para outros bancos.
 *
 * @param {string} codigoBanco - Código do banco para determinar os tipos de contas disponíveis
 * @returns {ITipoContasBancarias[]} Lista de tipos de contas bancárias disponíveis para o banco especificado
 */
export const definirTipoContasBancarias = (
  codigoBanco: string,
): ITipoContasBancarias[] => {
  if (codigoBanco === CODIGO_BANCO_CAIXA) return TIPO_CONTAS_BANCARIAS_CEF;

  return TIPO_CONTAS_BANCARIAS_NAO_CEF;
};
