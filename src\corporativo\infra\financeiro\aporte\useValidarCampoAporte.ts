import {
  IValidarCampoAportePayload,
  IValidarCampoAporteResponse,
  PECOS,
  PrevidenciaContext,
  tryGetValueOrDefault,
  TUseValidarCampoAporte,
  useApiGatewayCvpInvoker,
  useContext,
} from '@src/features/financeiro/aporte/exports';

export const useValidarCampoAporte = (): TUseValidarCampoAporte => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const payload = {
    codigoCertificado: certificadoAtivo?.certificadoNumero,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      Partial<IValidarCampoAportePayload>,
      IValidarCampoAporteResponse
    >(PECOS.ValidarAporte, {
      data: payload,
      autoFetch: false,
    });

  return {
    loading,
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as IValidarCampoAporteResponse,
    ),
    invocarApiGatewayCvpComToken,
  };
};
