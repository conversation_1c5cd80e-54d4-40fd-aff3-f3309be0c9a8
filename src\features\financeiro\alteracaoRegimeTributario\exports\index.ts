export {
  Alert,
  Button,
  Card,
  Checkbox,
  CheckboxLabel,
  DialogInfo,
  Grid,
  GridItem,
  IconArrowDownRound,
  IconExpandLessRound,
  IconExpandMoreRound,
  IconPVExclamationCircle,
  IconWarningSharp,
  RadioGroup,
  RadioItem,
  RadioLabel,
  Table,
  Text,
  Separator,
  LoadingSpinner,
  IconCheckCircleRound,
  IconInfoRound,
} from '@cvp/design-system-caixa';
export {
  default as React,
  useEffect,
  useState,
  useMemo,
  useCallback,
  useContext,
} from 'react';
export { useNavigate } from 'react-router-dom';
export type { NavigateFunction as INavigateFunction } from 'react-router-dom';

export type { TColumn } from '@cvp/design-system-caixa/dist/atoms/Table/Table.types';
export { default as styled } from 'styled-components';
export type { ButtonProps as TButtonProps } from '@mui/material';
export {
  checkIfAllItemsAreTrue,
  ordenaValor,
  tryGetValueOrDefault,
  getTernaryResult,
  checkIfSomeItemsAreTrue,
  tryGetMonetaryValueOrDefault,
  getSessionItem,
  setSessionItem,
  converterBase64,
} from '@cvp/utils';
export { Match, SwitchCase, For } from '@cvp/componentes-posvenda';

export { default as CheckAlteracaoRegimeTributario } from '../components/CheckAlteracaoRegimeTributario';
export { default as ModalAvisoAlteracaoAliquota } from '../components/ModalAvisoAlteracaoAliquota';
export { default as Aliquotas } from '../components/Aliquotas';
export { default as TabelaAliquotas } from '../components/TabelaAliquotas';
export { default as FluxoAlteracaoRegimeTributario } from '../pages/FluxoAlteracaoRegimeTributario';
export { default as AssinaturaAlteracaoRegimeTributario } from '../components/AssinaturaAlteracaoRegimeTributario';
export { default as Assinatura } from '@src/corporativo/components/Assinatura/Assinatura';
export { default as AlertAlteracaoRegimeTributario } from '../components/AlertAlteracaoRegimeTributario';
export { default as AlteracaoRegimeTributarioBotoes } from '../components/AlteracaoRegimeTributarioBotoes';

export * as CONSTS from '../constants/constants';
export {
  CODIGO_REQUISICAO,
  TIPO_DOCUMENTO,
} from '@src/shared/constants/comprovante';
export { OPERACOES_PREVIDENCIA } from '@src/corporativo/constants/OperacoesPrevidencia';
export { ASSINATURA_SESSION_KEY } from '@src/corporativo/constants/assinatura/assinaturaSession';

export { tabelaAliquotasDadosColunas } from '../factory/colunasTabelasAliquota';
export { obterPayloadAliquotasCalculo } from '@src/features/financeiro/alteracaoRegimeTributario/factory/obterPayloadAliquotasCalculo';

export * as S from '../style/styles';

export { useCalcularResgate } from '@src/shared/infra/financeiro/useCalcularResgate';
export { useListarFundosParaResgate } from '@src/shared/infra/financeiro/useListarFundosParaResgate';
export { default as useConsultarRegimeTributario } from '@src/shared/infra/useConsultarRegimeTributario';
export { default as useListarAlicotasAgrupadas } from '@src/corporativo/infra/financeiro/alteracaoRegimeTributario/useListarAlicotasAgrupadas';
export { default as useSolicitarAlteracaoPerfilTributario } from '@src/corporativo/infra/financeiro/alteracaoRegimeTributario/useSolicitarAlteracaoPerfilTributario';
export { tipoEmailConstants } from '@src/corporativo/infra/email/tipoEmail';
export { useConfirmarOperacaoAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useConfirmarOperacaoAssinaturaCaixa';

export type {
  IValoresAliquotas,
  IFundosAliquota,
} from '@src/corporativo/types/financeiro/alteracaoRegimeTributario/Response/IListarAlicotasAgrupadasResponse';
export type {
  IDadosTabelaAliquotas,
  ITabelaAliquotas,
} from '@src/corporativo/types/financeiro/alteracaoRegimeTributario/IDadosTabelaAliquotas';
export type { IAliquotas } from '@src/corporativo/types/financeiro/alteracaoRegimeTributario/IAliquotas';
export type { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
export type { ISolicitarAlteracaoPerfilTributarioResponse } from '@src/corporativo/types/financeiro/alteracaoRegimeTributario/Response/ISolicitarAlteracaoPerfilTributarioResponse';
export * from '@src/features/financeiro/alteracaoRegimeTributario/types';

export { obterDadosTabelaAliquotas } from '@src/features/financeiro/alteracaoRegimeTributario/factory/obterDadosTabelaAliquotas';
export { useObterNumeroResgate } from '@src/features/financeiro/alteracaoRegimeTributario/hooks/useObterNumeroResgate';
export { useRegistrarTokenAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useRegistrarTokenAssinaturaCaixa';
export { default as useValidarAssinatura } from '@src/shared/hooks/useValidarAssinatura';
export { useSolicitarAlteracaoRegimeTributario } from '@src/features/financeiro/alteracaoRegimeTributario/hooks/useSolicitarAlteracaoPerfilTributario';
export { default as useFluxoAlteracaoRegimeTributario } from '../hooks/useFluxoAlteracaoRegimeTributario';
export { default as useAliquotas } from '../hooks/useAliquotas';
export { default as useModalAvisoAlteracaoAliquota } from '../hooks/useModalAvisoAlteracaoAliquota';
export { default as useAssinaturaAlteracaoRegimeTributario } from '../hooks/useAssinaturaAlteracaoRegimeTributario';
export { default as useAlteracaoRegimeTributarioBotoes } from '@src/features/financeiro/alteracaoRegimeTributario/hooks/useAlteracaoRegimeTributarioBotoes';
export { useObterDadosTabelasAliquotas } from '@src/features/financeiro/alteracaoRegimeTributario/hooks/useDadosTabelaAliquotas';
export { useObterTabelasAliquotas } from '@src/features/financeiro/alteracaoRegimeTributario/hooks/useObterTabelasAliquotas';
export { default as useAlteracaoRegimeTributarioContext } from '@src/corporativo/hooks/useAlteracaoRegimeTributarioContext';
export { default as AlteracaoRegimeTributarioProvider } from '@src/corporativo/context/financeiro/alteracaoRegimeTributario/AlteracaoRegimeTributarioProvider';
export { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
export { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';
