import {
  useAlterarDadosPagamento,
  GridItem,
  Text,
  TextField,
} from '@src/features/financeiro/dadosPagamento/exports';

const CampoTexto: React.FC<{
  label: string;
  name: string;
  value: string;
  placeholder: string;
  maxNumber: number;
}> = ({ label, name, value, placeholder, maxNumber }) => {
  const { handleNovaContaChange } = useAlterarDadosPagamento();

  return (
    <GridItem xs="1 / 3">
      <Text variant="text-large-700">{label}</Text>
      <TextField
        name={name}
        value={value}
        onChange={handleNovaContaChange}
        placeholder={placeholder}
        variant="box-classic"
        maxLength={maxNumber}
      />
    </GridItem>
  );
};

export default CampoTexto;
