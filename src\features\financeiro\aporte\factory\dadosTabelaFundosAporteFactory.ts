import {
  IDadosFundosExistentesAporte,
  IDadosFundosNovosAporte,
  IDadosTabelaFundos,
  IItensExtrato,
  tryGetValueOrDefault,
} from '@src/features/financeiro/aporte/exports';

interface IDadosTabelaFundosAporteFactorie {
  renderFundos: IDadosFundosExistentesAporte | IDadosFundosNovosAporte;
  itensExtrato: IItensExtrato[];
}

export const dadosTabelaFundosAporteFactory = ({
  renderFundos,
  itensExtrato,
}: IDadosTabelaFundosAporteFactorie): IDadosTabelaFundos => {
  const dadosFundos = renderFundos.dadosFundos.map(fundo => {
    const fundoCorrespondente = itensExtrato.find(
      item => item.fundoId === fundo.fundoId,
    );

    const taxaAdministrativa = tryGetValueOrDefault(
      [fundoCorrespondente?.taxaAdministrativa],
      '-',
    );

    return {
      fundoId: fundo.fundoId,
      reservaId: fundo.reservaId,
      descricaoFundo: fundo.descricaoFundo,
      descPerfilFundo: fundo.descPerfilFundo,
      rentUlt12Meses: fundo.rentUlt12Meses,
      taxaAdministração: taxaAdministrativa,
      saldo: fundo.saldo,
      valorContribuicao: fundo.valorContribuicao,
      tipoFundo: fundo.tipoFundo,
      valorMinimo: fundo.valorMinimo,
      qntMaxFundoPermitida: fundo.qntMaxFundoPermitida,
    };
  });
  return {
    dadosFundos,
    qntMaxFundoPermitida: renderFundos.qntMaxFundoPermitida,
    qntFundoDisponivel: renderFundos.qntFundoDisponivel,
  };
};
