import {
  dadosBancariosAdicionadosFactory,
  EEtapasAporte,
  enumNumeroBancos,
  enumTipoContaBancaria,
  enumValidacaoContaBancaria,
  ICadastroNovaConta,
  useAporteContext,
  useAporteServiceContext,
  useEffect,
} from '@src/features/financeiro/aporte/exports';

type TUseNovaContaAporte = {
  validarContaLoading: boolean;
  setEtapa: React.Dispatch<React.SetStateAction<EEtapasAporte>>;
  handleValidarConta: (values: ICadastroNovaConta) => Promise<void>;
};

export const useNovaContaAporte = (): TUseNovaContaAporte => {
  const { setEtapa, setTipoContaBancaria, setContaBancariaSelecionada } =
    useAporteContext();

  const {
    validarConta: { validarConta, isLoadingValidacaoConta },
  } = useAporteServiceContext();

  const handleValidarConta = async (values: ICadastroNovaConta) => {
    const responseValidacao = await validarConta({
      codigoAgencia: values.agencia,
      codigoOperacao: values.operacao,
      digitoVerificador: values.digito,
      numeroBanco: enumNumeroBancos.CAIXA_ECONOMICA,
      numeroConta: values.contaBancaria,
    });

    const codigoValidacao = responseValidacao?.entidade?.codigoRetorno;

    const contaValidada =
      codigoValidacao ===
      enumValidacaoContaBancaria.CONTA_BANCARIA_CADASTRADA_VALIDADA;

    if (contaValidada) {
      const contaBancariaAdicionadaModificada =
        dadosBancariosAdicionadosFactory(values);

      setContaBancariaSelecionada(contaBancariaAdicionadaModificada);
      setTipoContaBancaria(enumTipoContaBancaria.CONTA_BANCARIA_NOVA);

      setEtapa(EEtapasAporte.ModalConfirmarAporte);
    }
  };

  useEffect(() => {
    setTipoContaBancaria(enumTipoContaBancaria.CONTA_BANCARIA_NOVA);
  }, []);

  return {
    validarContaLoading: isLoadingValidacaoConta,
    setEtapa,
    handleValidarConta,
  };
};
