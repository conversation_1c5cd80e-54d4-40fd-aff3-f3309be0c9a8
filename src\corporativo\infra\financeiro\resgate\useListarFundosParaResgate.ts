import { useContext } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';

import { TEMPO_MINUTOS } from '@src/shared/constants/predefinicoesTempo';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import {
  IListarFundosParaResgatePayload,
  IListarFundosParaResgateResponse,
  IUseListarFundosParaResgateReturn,
} from '@src/corporativo/types/financeiro/resgate/IListarFundosParaResgate';

export const useListarFundosParaResgate =
  (): IUseListarFundosParaResgateReturn => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const codigoCertificado = tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    );

    const {
      response: listaFundosParaResgate,
      loading: isLoadingListaFundosParaResgate,
      invocarApiGatewayCvpComToken: listarFundosParaResgate,
    } = useApiGatewayCvpInvoker<
      IListarFundosParaResgatePayload,
      IListarFundosParaResgateResponse
    >(PECOS.ListarFundosParaResgate, {
      data: { codigoCertificado },
      autoFetch: true,
      cache: true,
      cacheKey: codigoCertificado,
      cacheTime: TEMPO_MINUTOS.UM,
    });

    return {
      listaFundosParaResgate: tryGetValueOrDefault(
        [listaFundosParaResgate?.entidade],
        {} as IListarFundosParaResgateResponse,
      ),
      isLoadingListaFundosParaResgate,
      listarFundosParaResgate,
    };
  };
