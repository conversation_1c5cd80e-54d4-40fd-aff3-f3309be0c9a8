import {
  formatarValorPadraoBrasileiro,
  FUNDOS,
  Grid,
  GridItem,
  Text,
  tryGetValueOrDefault,
} from '@src/features/financeiro/aporte/exports';

interface ISomaEValorRestanteContribuicao {
  valorContribuicao: number;
  somaValorContribuicao: number;
}

const SomaEValorRestanteContribuicao: React.FC<
  ISomaEValorRestanteContribuicao
> = ({ valorContribuicao, somaValorContribuicao }) => {
  return (
    <Grid justify="space-between" margin="0px 7px">
      <GridItem>
        <Text variant="text-standard-400" fontColor="content-neutral-04">
          {FUNDOS.restante}
          <strong>
            {tryGetValueOrDefault(
              [formatarValorPadraoBrasileiro(valorContribuicao)],
              'R$ 0,00',
            )}
          </strong>
        </Text>
      </GridItem>

      <GridItem>
        <Text variant="text-standard-400" fontColor="content-neutral-04">
          {FUNDOS.total}
          <strong>
            {tryGetValueOrDefault(
              [formatarValorPadraoBrasileiro(somaValorContribuicao)],
              'R$ 0,00',
            )}
          </strong>
        </Text>
      </GridItem>
    </Grid>
  );
};

export default SomaEValorRestanteContribuicao;
