import { ConfigApiGatewayConstants } from '@src/corporativo/constants/ConfigApiGatewayConstants';

export const PECOS = {
  AtualizarEmail: 'PECO_AtualizarEmail',
  AtualizarEndereco: 'PECO_AtualizarEndereco',
  AtualizarTelefone: 'PECO_AtualizarTelefone',
  AtualizarDadosParticipante: 'PECO_AtualizarDadosParticipante',
  ListarCertificadosPrevidencia: 'PECO_ListarCertificadosPrevidencia',
  CertificadoPrevidenciaDetalhar: 'PECO_CertificadoPrevidenciaDetalhar',
  ObterDadosOrigemTransferenciaVgbl: 'PECO_ObterDadosOrigemTransferenciaVgbl',
  BuscarEnderecoCep: 'PECO_ObterEnderecoCep',
  ObterValorMinimoContribuicao: 'PECO_ObterValorMinimoContribuicao',
  ValidarValoresMinimosTransferenciaVgbl:
    'PECO_ValidarValoresMinimosTransferenciaVgbl',
  ListarOfertasVgblConjugado: 'PECO_ListarOfertasVgblConjugado',
  ListarDadosBancariosVgbl: 'PECO_ListarDadosBancariosVgbl',
  ListarTiposOperacoesBancariasVgbl: 'PECO_ListarTiposOperacoesBancariasVgbl',
  ListarDiasUteisVgbl: 'PECO_ListarDiasUteisVgbl',
  InserirDadosBancariosTransfVgbl: 'PECO_InserirDadosBancariosTransfVgbl',
  TransferirProdutoVgbl: 'PECO_TransferirProdutoVgbl',
  SolicitarAssinaturaTransfVgbl: 'PECO_SolicitarAssinaturaTransfVgbl',
  CancelarTransferenciaVgblConjugado: 'PECO_CancelarTransferenciaVgblConjugado',
  ValidarCancelamentoPreviencia: 'PECO_ValidarCancelamentoPrevidencia',
  obterEndpointExerciciosDisponivesIR: 'PECO_ObterExercicios',
  RelatorioResgate: 'PECO_RelatorioResgate',
  BeneficioProtecao: 'PECO_BeneficioProtecao',
  FaixaRenda: 'PECO_FaixaRendaBeneficioProtecao',
  PrazosBeneficioProtecao: 'PECO_PrazosBeneficioProtecao',
  CalcularBeneficioProtecao: 'PECO_CalcularBeneficioProtecao',
  ReativarBeneficioProtecao: 'PECO_ReativarBeneficioProtecao',
  IncluirBeneficioProtecao: 'PECO_IncluirBeneficioProtecao',
  ExerciciosDisponivesIR: 'PECO_ObterExercicios',
  ObterBeneficiarios: 'PECO_ObterBeneficiarios',
  SolicitarAlteracaoPerfilTributario: 'PECO_SolicitarAlteracaoPerfilTributario',
  AtualizarDataVencimento: 'PECO_AtualizarDataVencimento',
  RecuperarContribuicoesCertificado: 'PECO_RecuperarContribuicoesCertificado',
  AtivarContribuicao: 'PECO_AtivarContribuicao',
  SuspenderContribuicao: 'PECO_SuspenderContribuicao',
  ObterComprovanteResgate: 'PECO_ObterComprovanteResgate',
  ObterExtratoPdf: 'PECO_ObterExtratoPdf',
  ListarFundos: 'PECO_ListarFundosMultiFundoPj',
  OterInformacoesCertificado: 'PECO_ObterInformacoesCertificado',
  ObterFundosDestinoTransferencia: 'PECO_TransferirEntreFundos',
  DefinirReservaDestino: 'PECO_DefinirReservaDestino',
  RevalidarTransferencia: 'PECO_RevalidarTransferencia',
  ConfirmarTransferencia: 'PECO_ConfirmarTransferencia',
  ConsultarContribuicaoRegular:
    'PECO_ConsultarFundoContribuicaoRegularMultiFundoPj',
  DefinirSaidaMultifundos: 'PECO_DefinirSaidaMultiFundoPj',
  DefinirDestinoMultifundos: 'PECO_DefinirDestinoMultiFundoPj',
  ValidarMultiFundoPj: 'PECO_ValidarMultiFundoPj',
  DefinirRegularMultiFundoPj: 'PECO_DefinirRegularMultiFundoPj',
  IndicarInstituidoMultiFundoPj: 'PECO_IndicarInstituidoMultiFundoPj',
  ConfirmarTransferenciaMultiFundoPj: 'PECO_ConfirmarTransferenciaMultiFundoPj',
  FinalizarAporte: 'PECO_FinalizarAporte',
  EfetuarAporte: 'PECO_EfetuarAporte',
  ObterDatas: 'PECO_ObterDatas',
  ObterDadosBancarios: 'PECO_ObterDadosBancarios',
  ObterFundos: 'PECO_ObterFundos',
  ValidarConta: 'PECO_ValidarConta',
  PermiteBoleto: 'PECO_PermiteBoleto',
  ObterOperacaoBancaria: 'PECO_ObterOperacaoBancaria',
  ObterFormaPagamentoAporte: 'PECO_ObterFormaPagamentoAporte',
  CancelarResgate: 'PECO_CancelarResgate',
  ObterHistoricoResgate: 'PECO_ObterHistoricoResgate',
  ObterCoberturas: 'PECO_Coberturas',
  SolicitarAssinaturaBeneficiariosPrevidencia:
    'PECO_SolicitarAssinaturaBeneficiariosPrevidencia',
  ModificarBeneficiarios: 'PECO_ModificarBeneficiarios',
  ConsultarPermissaoAssinaturaBeneficiario:
    'PECO_ConsultarPermissaoAssinaturaBeneficiario',
  ConsultarDadosExtratoCotas: 'PECO_ConsultarDadosExtratoCotas',
  GerarQuitacaoAnual: 'PECO_GerarQuitacaoAnual',
  ObterDadosParticipante: 'PECO_ObterDadosParticipante',
  ObterExtratoUnificado: 'PECO_ObterExtratoUnificado',
  ObterLaminaRentabilidade: 'PECO_ObterLaminaRentabilidade',
  SolicitarTransferenciaReducaoTaxaFop223: 'PECO_SolicitarTransferenciaFop223',
  ConsultarPainelUnificado: 'PECO_BuscarHierarquiaResgate',
  ConsultarPainelDetalhado: 'PECO_ConsultarPainelGestaoResgateDetalhado',
  CancelarResgateAssinatura: 'PECO_CancelarResgateComAssinatura',
  ValidarCancelamentoResgate: 'PECO_ConsultarPermissaoAssinaturaResgate',
  ConsultarPainelDetalhadoCertificado:
    'PECO_ConsultarPainelGestaoResgateDetalhadoPorCertificado',
  SolicitarCriacaoProtocoloRegistroOcorrenciaFop223:
    'PECO_SolicitarCriacaoProtocoloRegistroOcorrenciaFop223',
  ObterAnoHistorico: 'PECO_ObterAnoHistorico',
  ObterHistorico: 'PECO_ObterHistorico',
  ObterCertificado: 'PECO_ObterCertificado',
  ObterCertificadoPorCpf: 'PECO_ObterCertificadosPorCpf',
  ObterPagamento: 'PECO_ObterPagamento',
  ObterSegundaVia: 'PECO_ObterSegundaVia',
  GerarExtratoCotas: 'PECO_GerarExtratoCotas',
  ConsultarMotivosResgate: 'PECO_ConsultarMotivosResgate',
  SolicitarResgate: 'PECO_SolicitarResgate',
  ImprimirPosicaoConsolidada: 'PECO_ImprimirPosicaoConsolidada',
  ConsultarDocumentoAssinadoFop223: 'PECO_ConsultarDocumentoAssinadoFop223',
  ConsultarEvolucaoPatrimonial: 'PECO_ConsultarEvolucaoPatrimonial',
  ListarContasBancarias: 'PECO_ListarContasBancarias',
  ResponsavelFinanceiro: 'PECO_ResponsavelFinanceiro',
  CriarCanalPagamento: 'PECO_CriarCanalPagamento',
  AtualizarFormaPagamento: 'PECO_AtualizarFormaPagamento',
  ImprimirDadosExtratoCotasPrevdencia:
    'PECO_imprimirDadosExtratoCotasPrevdencia',
  ObterDadosPalno: 'PECO_ObterPlano',
  ListarValores: 'PECO_ListarValores',
  StatusSinistroPrevidencia: 'PECO_StatusSinistroPrevidencia',
  ObterInforme: 'PECO_ObterInforme',
  EnviarEmailPrevidencia: 'PECO_EnviarEmailPrevidencia',
  ConsultarDadosPep: 'PECO_ConsultarDadosPep',
  ValidarAporte: 'PECO_ValidarCampoAporte',
  TiposDePagamento: 'PECO_TiposPagamento',
  SimularRenda: 'PECO_SimularRenda',
  ConsultarHistorico: 'PECO_ConsultarHistorico',
  ObterFiltroTipoSolicitacoes: 'PECO_ObterTiposSolicitacoesHistorico',
  ObterHistoricoSolicitacoes: 'PECO_ObterHistoricoSolicitacoes',
  ListarFundosParaResgate: 'PECO_ListarFundosParaResgate',
  CalcularResgate: 'PECO_CalcularResgate',
  ConsultarResumoAliquota: 'PECO_ConsultarResumoAliquota',
  ConsultarDetalheCalculo: 'PECO_ConsultarDetalheCalculo',
  RecuperarBancos: 'PECO_RecuperarBancos',
  ListarMotivosResgate: 'PECO_ListarMotivosResgate',
  ConsultarTiposPagamento: 'PECO_ConsultarTiposPagamento',
  ConsultarContribuicaoRegularResgate: 'PECO_ConsultarContribuicaoRegular',
  SalvarDadosPagamento: 'PECO_SalvarDadosPagamento',
  CriarMotivoResgate: 'PECO_CriarMotivoResgate',
  ConfirmarResgate: 'PECO_ConfirmarResgate',
  ObterBancos: 'PECO_ObterBancos',
  DefinirContribuicaoRegular: 'PECO_DefinirContribuicaoRegular',
  ConsultarParametrosRegimeTributario:
    'PECO_consultarParametrosRegimeTributario',
  ManterNomeSocial: 'PECO_ManterNomeSocial',
  AssinaturaCaixaConfimarOperacao: 'PECO_AssinaturaCaixaConfimarOperacao',
};

export const URLS_PECOS_MATRIZ = Object.values(PECOS).map(
  PECO => `/${ConfigApiGatewayConstants.configure.operationPath}${PECO}`,
);

export const PECO_MATRIZ = {
  ConsultarMatrizAcessoPrevidencia: 'PECO_ConsultarMatrizAcessoPrevidencia',
};
