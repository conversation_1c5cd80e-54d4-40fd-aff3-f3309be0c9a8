import {
  AlertaPrevidencia,
  BotoesAcaoTabelaBeneficiarios,
  colunasTabelaBeneficiarios,
  dadosBeneficiario,
  FieldArray,
  MultiAccordion,
  TableEdit,
  Text,
  useBeneficiariosForm,
} from '../exports';

type TCoberturaProps = {
  coberturaId: string;
  nomTipoPagamento: string;
  valorBeneficio: string;
};

const formatter = new Intl.NumberFormat('pt-BR', {
  style: 'currency',
  currency: 'BRL',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

export const Cobertura: React.FC<TCoberturaProps> = ({
  coberturaId,
  nomTipoPagamento,
  valorBeneficio,
}) => {
  const { values, verificarCoberturaEditando, cancelarEdicaoCobertura } =
    useBeneficiariosForm();

  return (
    <MultiAccordion.Item value={coberturaId}>
      <MultiAccordion.Trigger
        onClick={() =>
          verificarCoberturaEditando(coberturaId) &&
          cancelarEdicaoCobertura(coberturaId)
        }
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Text variant="heading-tiny-600" lineheight="farther">
            {nomTipoPagamento}
          </Text>
          <div>
            <Text variant="heading-tiny-600">Reserva diponível</Text>
            <Text variant="text-large-400">
              {formatter.format(Number(valorBeneficio))}
            </Text>
          </div>
        </div>
      </MultiAccordion.Trigger>

      <MultiAccordion.Content style={{ width: '100%' }}>
        <FieldArray
          name={`${coberturaId}.beneficiarios`}
          render={beneficiariosHelpes => (
            <>
              <TableEdit
                themeTable="default"
                columns={colunasTabelaBeneficiarios(
                  coberturaId,
                  beneficiariosHelpes.remove,
                )}
                data={dadosBeneficiario(values[coberturaId].beneficiarios)}
              />
              <BotoesAcaoTabelaBeneficiarios
                coberturaId={coberturaId}
                adicionarBeneficiario={(cpf: string, sexo: string) =>
                  beneficiariosHelpes.push({
                    nomeBeneficiario: '',
                    porcentagem: '0',
                    dataNascimento: null,
                    numCpf: cpf,
                    sexo,
                    idBeneficiario: cpf,
                    idParentesco: null,
                    estado: 'novo',
                  })
                }
              />
            </>
          )}
        />

        <AlertaPrevidencia
          identificador={`beneficiarios-cobertura-${coberturaId}`}
        />
      </MultiAccordion.Content>
    </MultiAccordion.Item>
  );
};
