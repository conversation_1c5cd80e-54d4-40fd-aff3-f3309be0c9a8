import * as Transferencia from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const AlertasTransferencia: React.FC = () => {
  const alertRef = Transferencia.useRef<HTMLDivElement | null>(null);
  const { transferenciaRealizada, erro, distribuicaoValores } =
    Transferencia.useTransferenciaContext();

  Transferencia.useEffect(() => {
    setTimeout(() => {
      if (alertRef.current) {
        alertRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center',
        });
      }
    }, 100);
  }, [transferenciaRealizada, erro]);

  return (
    <Transferencia.AlertasTransferenciaCases
      erro={erro}
      distribuicaoValores={distribuicaoValores}
      transferenciaRealizada={transferenciaRealizada}
    />
  );
};
