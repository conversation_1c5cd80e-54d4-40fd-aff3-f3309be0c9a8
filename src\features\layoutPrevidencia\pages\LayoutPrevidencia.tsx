import {
  ModalImprDoc,
  previdencia,
  useContext,
  PrevidenciaContext,
  LayoutPlataforma,
  useNavigate,
  ModalImprimirDocumentosContext,
  ContainerListaCertificados,
  REDIRECT_BUTTONS,
} from '@src/features/layoutPrevidencia/exports';

const LayoutPrevidencia: React.FC = () => {
  const navigate = useNavigate();

  const { modalImprDocDispatch, modalImprDocState } = useContext(
    ModalImprimirDocumentosContext,
  );

  const { statusContratoFilter } = useContext(PrevidenciaContext);

  return (
    <LayoutPlataforma
      primaryTitle={previdencia.primaryTitle}
      buttonsRedirect={REDIRECT_BUTTONS()}
      secondaryTitle={previdencia.secondaryTitle}
      userProfile="OPERADOR"
      profileFilters={statusContratoFilter}
      filterBykey="situacao"
    >
      <ModalImprDoc
        modalImprDocDispatch={modalImprDocDispatch}
        modalImprDocState={modalImprDocState}
        navigate={navigate}
      />
      <ContainerListaCertificados />
    </LayoutPlataforma>
  );
};

export default LayoutPrevidencia;
