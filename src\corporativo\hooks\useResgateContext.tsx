import { useContext } from 'react';

import { ResgateContext } from '@src/corporativo/context/financeiro/resgate/ResgateContext';
import { IResgateContext } from '@src/corporativo/types/financeiro/resgate/IResgateContext';

export const useResgateContext = (): IResgateContext => {
  const context = useContext(ResgateContext);
  if (!context) {
    throw new Error(
      'useResgateContext deve ser usado dentro do ResgateProvider',
    );
  }
  return context;
};
