import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { IObterPayloadCalcularResgateFactoryRetorno } from '@src/corporativo/types/financeiro/resgate/IObterPayloadCalcularResgateFactory';

export interface IUseCalcularResgateRetorno {
  dadosCalculoResgate: ICalcularResgateResponse;
  isLoadingCalculoResgate: boolean;
  calcularResgate: (
    dynamicPayload?: IObterPayloadCalcularResgateFactoryRetorno,
  ) => Promise<IHandleReponseResult<ICalcularResgateResponse> | undefined>;
}

export interface ICalcularResgateDetalhesFundos {
  codigoFundo: string;
  codigoReserva: string;
  valorResgate: string;
}

export interface ICalcularResgatePayload {
  codigoCertificado: string;
  tipoRegimeTributario: string;
  tipoResgate: string;
  valorResgateTotal: string;
  detalhesFundos?: ICalcularResgateDetalhesFundos;
}

export interface ICalcularResgateDadosEncargo {
  codigoEmpresa: string;
  numeroResgate: string;
  codigoOperacao: string;
  valorTotalSolicitacao: string;
  valorCorrecao: string;
  percentualCorrecao: string;
  valorSaida: string;
  percentualSaida: string;
  valorIrSolicitacao: string;
  percentualIrSolicitacao: string;
  valorTaxaSaida: string;
  percentualTaxaSaida: string;
  valorBaseIr: string;
  valorSaldoPrincipal: string;
  valorSaldo: string;
  valorLiquidoSolicitacao: string;
  valorReserva: string;
  nomeParticipante: string;
  codigoCertificado: string;
  valorExcedenteFinanceiro: string;
  aliquotaResgate: string;
}

export interface ICalcularResgateResponse {
  dadosEncargo: ICalcularResgateDadosEncargo;
}
