import { useContext } from 'react';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IConfirmarResgatePayload,
  IConfirmarResgateResponse,
  IUseConfirmarResgateReturn,
} from '@src/corporativo/types/financeiro/resgate/IConfirmarResgate';

export const useConfirmarResgate = (): IUseConfirmarResgateReturn => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const codigoCertificado = tryGetValueOrDefault(
    [certificadoAtivo?.certificadoNumero],
    '',
  );

  const {
    response: dadosConfirmacaoResgate,
    loading: isLoadingConfirmacaoResgate,
    invocarApiGatewayCvpComToken: confirmarResgate,
  } = useApiGatewayCvpInvoker<
    Partial<IConfirmarResgatePayload>,
    IConfirmarResgateResponse
  >(PECOS.ConfirmarResgate, {
    data: { codigoCertificado },
  });

  return {
    dadosConfirmacaoResgate: tryGetValueOrDefault(
      [dadosConfirmacaoResgate?.entidade],
      {} as IConfirmarResgateResponse,
    ),
    isLoadingConfirmacaoResgate,
    confirmarResgate,
  };
};
