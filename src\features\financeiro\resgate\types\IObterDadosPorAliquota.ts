import {
  IMontarDadosTabelaDetalheCalculoFactoryRetorno,
  IMontarDetalhadoAliquotaFactoryRetorno,
  IMontarResumoAliquotaFactoryRetorno,
} from '@src/features/financeiro/resgate/exports';

export interface IObterDadosPorAliquotaRetorno {
  resumo: IMontarResumoAliquotaFactoryRetorno[];
  resumoEstendido: IMontarDetalhadoAliquotaFactoryRetorno[];
  tabelaHistoricoContribuicao: IMontarDadosTabelaDetalheCalculoFactoryRetorno[];
  isExibeTabelaAliquota: boolean;
}
