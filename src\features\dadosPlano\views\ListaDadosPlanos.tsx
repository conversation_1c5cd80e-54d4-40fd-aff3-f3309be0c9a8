import { InfoDadosPlano, DS, UTILS, For } from '../exports';

type TListaDadosPlanosProps = {
  listaInfoDadosPlano: {
    title: string;
    value: string;
  }[];
};

export const ListaDadosPlanos: React.FC<TListaDadosPlanosProps> = ({
  listaInfoDadosPlano,
}) => {
  return (
    <For each={UTILS.tryGetValueOrDefault([listaInfoDadosPlano], [])}>
      {item => (
        <DS.GridItem xs="1" key={item.title}>
          <InfoDadosPlano title={item.title} value={item.value} />
        </DS.GridItem>
      )}
    </For>
  );
};
