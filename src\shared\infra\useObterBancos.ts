import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IObterBancosResponse,
  IUseObterBancosRetorno,
} from '@src/shared/types/IObterBancos';

export const useObterBancos = (): IUseObterBancosRetorno => {
  const {
    response: listaBancos,
    loading: isLoadingListaBancos,
    invocarApiGatewayCvpComToken: obterBancos,
  } = useApiGatewayCvpInvoker<unknown, IObterBancosResponse[]>(
    PECOS.ObterBancos,
    {},
  );

  return {
    listaBancos: tryGetValueOrDefault([listaBancos?.entidade], []),
    isLoadingListaBancos,
    obterBancos,
  };
};
