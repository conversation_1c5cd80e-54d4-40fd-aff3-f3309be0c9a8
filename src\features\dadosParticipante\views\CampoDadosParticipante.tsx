import {
  GridItem,
  IFormDadosParticipante,
  obterErroTextField,
  Text,
  TextField,
  useCampoDadosParticipante,
  Utils,
} from '../exports';

interface ICampoDadosParticipanteProps
  extends Omit<React.ComponentProps<'input'>, 'size'> {
  fracao: string;
  label: string;
  name: string;
  editavel: boolean;
  mask?: (val: string) => string;
}

export const CampoDadosParticipante: React.FC<ICampoDadosParticipanteProps> = ({
  fracao,
  label,
  name,
  maxLength,
  disabled,
  mask = val => val,
}) => {
  const { handleChangeCep, fieldProps, formik } =
    useCampoDadosParticipante(name);

  return (
    <GridItem
      style={{
        overflowWrap: 'break-word',
      }}
      xl={fracao}
      lg={fracao}
      md="1/1"
      sm="1/1"
      xs="1/4"
    >
      <Text variant="text-standard-600" marginBottom="12px">
        {label}
      </Text>

      <TextField
        variant="box-classic"
        placeholder=""
        error={obterErroTextField(formik, name as keyof IFormDadosParticipante)}
        value={mask(fieldProps.value ?? '')}
        name={name}
        onChange={Utils.getTernaryResult(
          !!disabled,
          {} as VoidFunction,
          name === 'cep' ? handleChangeCep : fieldProps.onChange,
        )}
        onBlur={fieldProps.onBlur}
        maxLength={maxLength}
        disabled={disabled}
      />
    </GridItem>
  );
};
