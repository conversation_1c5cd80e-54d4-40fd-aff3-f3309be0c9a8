import {
  ICalcularValorResgateRestanteRetorno,
  IFundosParaResgateFactory,
} from '@src/features/financeiro/resgate/exports';

export interface ITabelaSimulacaoResgateProps {
  fundosParaResgate: IFundosParaResgateFactory[];
  resultadoCalculoResgateRestante: ICalcularValorResgateRestanteRetorno;
  isDisabledBtnSimulacaoResgate: boolean;
  isLoadingConfirmacaoSimulacao: boolean;
  confirmarSimulacao: () => Promise<void>;
  reiniciarSimulacao: () => void;
}
