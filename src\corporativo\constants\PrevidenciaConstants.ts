import { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';

const previdencia = {
  urlRedirect: ROUTES.VENDA,
  primaryTitle: 'Previdência',
  secondaryTitle: 'Certificado do cliente',
  sinistroTitle: 'Histórico de Sinistro',

  userProfile: {
    control: 'select',
    options: ['ANALISTA_POS_VENDA', 'MEDICO', 'OPERADOR'],
  },
  profileFilters: {
    ANALISTA_POS_VENDA: [
      { id: 1, description: 'Vigente' },
      { id: 2, description: 'Cancelada' },
      { id: 3, description: 'Rejeitada' },
    ],
    MEDICO: [
      { id: 1, description: 'Vigente' },
      { id: 2, description: 'Cancelada' },
      { id: 3, description: 'Rejeitada' },
    ],
    OPERADOR: [
      { id: 1, description: 'Vigente' },
      { id: 2, description: 'Cancelada' },
      { id: 3, description: '<PERSON>jeitada' },
    ],
  },
};

export default previdencia;
