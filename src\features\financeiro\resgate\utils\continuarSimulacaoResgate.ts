import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Valida se a simulação de resgate pode prosseguir, com base nos fundos selecionados,
 * nos valores informados e no tipo de resgate (total ou parcial).
 *
 * A validação considera os seguintes critérios:
 * - Deve haver pelo menos um fundo selecionado com valor preenchido e maior que zero.
 * - Todos os fundos selecionados devem conter valores válidos (> 0).
 * - Para resgate total: o valor restante após o resgate deve ser zero.
 * - Para resgate parcial: o valor total resgatado somado à permanência mínima deve ser menor ou igual ao saldo total.
 * - O formulário (Formik) deve estar válido (`formik.isValid`).
 *
 * @param formik - Instância do Formik contendo os valores do formulário.
 * @param saldoTotal - Valor total disponível para resgate.
 * @param valorMinimoPermanencia - Valor mínimo que deve permanecer no plano (em resgates parciais).
 * @param isTipoResgateTotal - Indica se o tipo de resgate é total (`true`) ou parcial (`false`).
 *
 * @returns `true` se todos os critérios forem atendidos; caso contrário, `false`.
 */
export const continuarSimulacaoResgate = ({
  formik,
  saldoTotal,
  valorMinimoPermanencia,
  isTipoResgateTotal,
}: Resgate.IContinuarSimulacaoResgateParams): boolean => {
  const fundos = formik.values.fundosParaResgate;

  const saldoTotalResgate: number = Resgate.tryGetValueOrDefault(
    [saldoTotal],
    0,
  );

  const permanenciaMinimaPlano: number = Resgate.tryGetValueOrDefault(
    [valorMinimoPermanencia],
    0,
  );

  const { calculoRestante, calculoTotal } =
    Resgate.calcularValorResgateRestante(formik, saldoTotalResgate);

  const respeitaValorMinimoParcial: boolean =
    calculoTotal + permanenciaMinimaPlano <= saldoTotalResgate;

  const hasFundoPreenchido: boolean = fundos.some(fundo =>
    Resgate.checkIfAllItemsAreTrue([
      !!fundo.selecionado,
      !!Number(fundo.valorRetirar),
    ]),
  );

  const allSelectedHaveValidValue: boolean = fundos.every(fundo => {
    return Resgate.checkIfSomeItemsAreTrue([
      !fundo.selecionado,
      !!Number(fundo.valorRetirar),
    ]);
  });

  const validacoesFundos: boolean = Resgate.checkIfAllItemsAreTrue([
    hasFundoPreenchido,
    allSelectedHaveValidValue,
  ]);

  const validacaoPorTipoResgate: boolean = Resgate.getTernaryResult(
    isTipoResgateTotal,
    !calculoRestante,
    respeitaValorMinimoParcial,
  );

  return Resgate.checkIfAllItemsAreTrue([
    formik.isValid,
    validacoesFundos,
    validacaoPorTipoResgate,
  ]);
};
