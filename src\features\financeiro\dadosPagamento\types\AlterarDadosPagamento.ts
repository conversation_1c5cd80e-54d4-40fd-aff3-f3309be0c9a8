import {
  IHandleReponseResult,
  IVariantAlert,
  SelectItem,
  TNovaConta,
  IResponseAlteracaoDiaVencimento,
} from '@src/features/financeiro/dadosPagamento/exports';

export type TAlterarDadosPagamentoProps = {
  open?: boolean;
  onClose: () => void;
  onEdit?: () => void;
};

export type TConfigurarMensagemFactory<T> = {
  response: IHandleReponseResult<T>;
  exibirMultiplosAlertas?: boolean;
  codigoRetorno?: string;
  descricaoMensagemSistema?: string;
};

export type TAlertaDadosPagamento = {
  mensagem: string;
  variant: IVariantAlert['variant'];
};

export type TUseAlertaDadosPagamento = {
  alertas: TAlertaDadosPagamento[];
};

export type TResetarContaPorResponseParams = {
  retornoAtualizaDiaVencimento: IHandleReponseResult<IResponseAlteracaoDiaVencimento>;
  retornoAtualizacaoFormaPagamento: IHandleReponseResult<undefined>;
  retornoValidarConta: boolean;
};

export type TUseAlterarConta = {
  adicionarNovaConta: boolean;
  deveValidarConta: boolean;
  contaExistente: string;
  novaConta: TNovaConta;
  setNovaConta: React.Dispatch<React.SetStateAction<TNovaConta>>;
  handleNovaContaChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleAlterarContaExistente: (selectedOption: SelectItem[]) => void;
  resetNovaContaPorSucesso: (params: TResetarContaPorResponseParams) => void;
};

export type TPagamentosHistoricoProps = {
  metodoPagamento: string;
  loading?: boolean;
};
