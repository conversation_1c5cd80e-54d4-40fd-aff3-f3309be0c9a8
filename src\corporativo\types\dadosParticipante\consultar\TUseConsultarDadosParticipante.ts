import { IConsultarDadosParticipanteVidaRequest } from './IConsultarDadosParticipanteVidaRequest';
import { IDadosParticipanteResponse } from './IDadosParticipanteResponse';

export type TUseConsultarDadosParticipante = (
  payload: IConsultarDadosParticipanteVidaRequest,
) => {
  response: IDadosParticipanteResponse;
  loading: boolean;
  invalidateCache: (queryKey?: string) => Promise<void>;
  invocarApiGatewayCvpComToken: () => Promise<void>;
  setResponse: (response: IDadosParticipanteResponse | undefined) => void;
};
