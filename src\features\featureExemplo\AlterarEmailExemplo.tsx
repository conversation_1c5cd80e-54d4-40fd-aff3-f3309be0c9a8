import { Button, ConditionalRender<PERSON>, Grid } from '@cvp/design-system-caixa';
import AlterarEmailAssinaturaExemplo from '@src/features/featureExemplo/AlterarEmailAssinaturaExemplo';
import { useAlterarEmailServicoExemplo } from '@src/features/featureExemplo/hooks/useAlterarEmailServicoExemplo';

export const AlterarEmailExemplo: React.FC = () => {
  const { confirmarAlteracaoEmail, etapa, setEtapa } =
    useAlterarEmailServicoExemplo();

  return (
    <Grid>
      <ConditionalRenderer condition={etapa === 'assinatura'}>
        <AlterarEmailAssinaturaExemplo
          podeAlterarEmailCallback={() => {
            setEtapa('salvar');
          }}
        />
      </ConditionalRenderer>

      <ConditionalRenderer condition={etapa === 'salvar'}>
        <Button
          onClick={() => {
            confirmarAlteracaoEmail('<EMAIL>');
          }}
        >
          Salvar
        </Button>
      </ConditionalRenderer>
    </Grid>
  );
};
