import { GridItem, Text } from '@src/features/layoutPrevidencia/exports';

export interface IContentConfirmarAporteProps {
  label: string;
  value: string | number;
}

const ContentConfirmarAporte: React.FC<IContentConfirmarAporteProps> = ({
  label,
  value,
}) => {
  return (
    <GridItem xs="1/3">
      <Text variant="text-standard-400" fontColor="content-neutral-03">
        {label}
      </Text>
      <Text variant="text-large-400" fontColor="content-neutral-04">
        {value}
      </Text>
    </GridItem>
  );
};

export default ContentConfirmarAporte;
