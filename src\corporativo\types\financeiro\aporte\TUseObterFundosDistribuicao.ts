import {
  IHandleReponseResult,
  IObterFundosResponse,
} from '@src/features/financeiro/aporte/exports';

export type TUseObterFundosDistribuicao = {
  response: IObterFundosResponse;
  loading: boolean;
  invocarApiGatewayCvpComToken: (dynamicPayload?: {
    valorContribuicao: string;
    distribuicaoPersonalizada: string;
  }) => Promise<IHandleReponseResult<IObterFundosResponse> | undefined>;
};
