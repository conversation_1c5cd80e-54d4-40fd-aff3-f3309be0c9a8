import { Text } from '@cvp/design-system-caixa';
import styled from 'styled-components';

export const PerfilDoRiscoContainer = styled.span`
  align-items: center;
  display: flex;
  overflow: initial;
  text-overflow: initial;
  gap: 1px;
`;

export const PerfilDoRiscoItem = styled.span`
  gap: 2px;
  margin: 0px;
  position: relative;
`;

export const PerfilDoRiscoBox = styled.div`
  width: 30.99px;
  height: 13px;
`;

export const PerfilDoRiscoTrianguloContainer = styled.div`
  position: absolute;
  top: 2px;
  left: 5px;
  min-width: 150px;

  svg {
    width: 19.07px;
    height: 16px;
  }
`;

export const PerfilDoRiscoDescricao = styled(Text)`
  margin: 0;
`;
