import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ModalObservacoesSolicitacaoResgate = ({
  isOpenModalObservacoes,
  toggleModalObservacoes,
  prepararSolicitacaoResgate,
  isLoadingPrepararSolicitacao,
}: Resgate.IModalObservacoesSolicitacaoResgateProps): React.ReactElement => {
  const theme = Resgate.useTheme();

  return (
    <Resgate.Dialog
      open={isOpenModalObservacoes}
      onOpenChange={toggleModalObservacoes}
    >
      <Resgate.DialogContent $initialHeight>
        <Resgate.Dialog.Header variant="highlight">
          <Resgate.Text variant="heading-small-600">
            Solicitação de resgate
          </Resgate.Text>
        </Resgate.Dialog.Header>
        <Resgate.Dialog.Body>
          <ul>
            <Resgate.For each={Resgate.OBSERVACOES_SOLICITACAO_RESGATE}>
              {item => (
                <li key={item}>
                  <Resgate.Text variant="text-standard-400">
                    {item}
                  </Resgate.Text>
                </li>
              )}
            </Resgate.For>
          </ul>
        </Resgate.Dialog.Body>
        <Resgate.Dialog.Footer>
          <Resgate.Button
            onClick={toggleModalObservacoes}
            size="standard"
            variant="primary"
          >
            Voltar
          </Resgate.Button>
          <Resgate.Button
            size="standard"
            variant="secondary"
            onClick={prepararSolicitacaoResgate}
            disabled={isLoadingPrepararSolicitacao}
            leftIcon={
              <Resgate.ConditionalRenderer
                condition={isLoadingPrepararSolicitacao}
              >
                <Resgate.LoadingSpinner
                  size="small"
                  color={theme.color.content.neutral['01']}
                />
              </Resgate.ConditionalRenderer>
            }
          >
            Prosseguir
          </Resgate.Button>
        </Resgate.Dialog.Footer>
      </Resgate.DialogContent>
    </Resgate.Dialog>
  );
};
