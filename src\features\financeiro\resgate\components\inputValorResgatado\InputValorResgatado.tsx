import * as Resgate from '@src/features/financeiro/resgate/exports';

export const InputValorResgatado: React.FC<
  Resgate.IInputValorResgatadoProps
> = ({ fundo, onChange, isTipoResgateTotal, valorMinimoResgate }) => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const {
    caminhoFundoResgateValor,
    handleChangeInput,
    isError,
    message,
    colorByErrorState,
  } = Resgate.useInputValorResgatado({
    fundo,
    onChange,
  });

  return (
    <Resgate.ContainerInputValorResgatado>
      <Resgate.InputText
        type="text"
        name={caminhoFundoResgateValor}
        disabled={Resgate.checkIfSomeItemsAreTrue([
          isTipoResgateTotal,
          !fundo.selecionado,
        ])}
        onChange={event => handleChangeInput(event)}
        onBlur={formik.handleBlur}
        value={Resgate.obterValorRetirarPorFundoMascara(
          formik.values,
          fundo.codigoFundo,
        )}
        arialabel="Adicione um valor resgate"
        placeholder="R$ 0,00"
        variant="box-classic"
        size="small"
        error={isError}
        required
      />
      <Resgate.ConditionalRenderer condition={!isError}>
        <Resgate.Text
          marginTop="5px"
          marginBottom="0px"
          variant="caption-standard-400"
          fontColor={colorByErrorState}
        >
          Valor Mínimo{' '}
          {Resgate.tryGetMonetaryValueOrDefault(valorMinimoResgate)}
        </Resgate.Text>
      </Resgate.ConditionalRenderer>
      <Resgate.ConditionalRenderer condition={isError}>
        <Resgate.Text
          marginTop="5px"
          variant="caption-standard-400"
          fontColor={colorByErrorState}
        >
          {message}
        </Resgate.Text>
      </Resgate.ConditionalRenderer>
    </Resgate.ContainerInputValorResgatado>
  );
};
