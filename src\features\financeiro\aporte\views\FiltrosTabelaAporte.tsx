import {
  Alert,
  ALERTS,
  checkIfAllItemsAreTrue,
  DataDebito,
  FILTRO_APORTE,
  FORMA_PAGAMENTO,
  FormaDePagamento,
  Grid,
  GridItem,
  IconInfoRound,
  LOADING,
  LoadingSpinner,
  Match,
  PerfilInvestidor,
  SwitchCase,
  Text,
  useFiltrosTabelaAporte,
  ValorContribuicao,
} from '@src/features/financeiro/aporte/exports';

const FiltrosTabelaAporte: React.FC = () => {
  const { formik, showInputs, filtroTabelaFundos, loadingDatasDebito } =
    useFiltrosTabelaAporte();

  return (
    <Grid margin="18" container>
      <SwitchCase>
        <Match when={loadingDatasDebito}>
          <GridItem xs="1">
            <LoadingSpinner color="#005CA9" size="medium">
              {LOADING.FILTROS_APORTE}
            </LoadingSpinner>
          </GridItem>
        </Match>
        <Match when={!loadingDatasDebito}>
          <GridItem xs="1">
            <Text variant="text-big-400" fontColor="content-neutral-06">
              {FILTRO_APORTE.contribuicoes}
            </Text>
            <SwitchCase>
              <Match
                when={checkIfAllItemsAreTrue([
                  showInputs.open,
                  filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.boleto,
                ])}
              >
                <Alert
                  variant="information-01"
                  icon={<IconInfoRound size="large" color="#038299" />}
                >
                  <Text variant="text-standard-400">
                    {ALERTS.importante_boleto}
                  </Text>
                </Alert>
              </Match>
              <Match
                when={checkIfAllItemsAreTrue([
                  showInputs.open,
                  filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito,
                ])}
              >
                <Alert
                  variant="information-01"
                  icon={<IconInfoRound size="medium" color="#038299" />}
                >
                  <Text variant="text-standard-400">
                    {ALERTS.importante_debito}
                  </Text>
                </Alert>
              </Match>
            </SwitchCase>
          </GridItem>

          <FormaDePagamento formik={formik} />

          <Match when={showInputs.open}>
            <DataDebito formik={formik} showInputs={showInputs} />
            <ValorContribuicao formik={formik} />
            <PerfilInvestidor
              formik={formik}
              perfilInvestidor={filtroTabelaFundos.perfilInvestidor}
            />
          </Match>
        </Match>
      </SwitchCase>
    </Grid>
  );
};

export default FiltrosTabelaAporte;
