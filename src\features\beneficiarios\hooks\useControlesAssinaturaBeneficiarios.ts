import {
  BeneficiariosContext,
  BeneficiariosDispatchContext,
  Blocker,
  checkIfAllItemsAreTrue,
  EBeneficiariosActionKind,
  IAssinaturaResponse,
  setSessionItem,
  TBeneficiariosActions,
  useBeneficiariosForm,
  useBlocker,
  useContext,
  useValidarAssinatura,
} from '../exports';

type TUseControlesAssinaturaBeneficiarios = () => {
  confirmandoEdicao: boolean;
  editando: boolean;
  dispatch: React.Dispatch<TBeneficiariosActions>;
  assinaturaValida: boolean;
  blocker: Blocker;
  assinaturaCallback: (response: IAssinaturaResponse) => void;
  cancelarEdicaoBeneficiarios: VoidFunction;
  confirmarEdicaoBeneficiarios: () => Promise<void>;
  salvarBeneficiarios: VoidFunction;
};

export const useControlesAssinaturaBeneficiarios: TUseControlesAssinaturaBeneficiarios =
  () => {
    const { resetForm, submitForm } = useBeneficiariosForm();

    const { validarAssinatura } = useValidarAssinatura();
    const { confirmandoEdicao, assinaturaValida, editando } =
      useContext(BeneficiariosContext);
    const dispatch = useContext(BeneficiariosDispatchContext);

    const blocker = useBlocker(({ currentLocation, nextLocation }) =>
      checkIfAllItemsAreTrue([
        editando,
        currentLocation.pathname !== nextLocation.pathname,
      ]),
    );

    const confirmarEdicaoBeneficiarios = async () => {
      submitForm();
    };

    const salvarBeneficiarios = () => {
      dispatch({
        type: EBeneficiariosActionKind.CONFIRMAR_EDICAO_BENEFICIARIOS,
      });
    };

    const cancelarEdicaoBeneficiarios = () => {
      resetForm();
      dispatch({
        type: EBeneficiariosActionKind.CANCELAR_EDICAO_BENEFICIARIOS,
      });
    };

    const assinaturaCallback = (response: IAssinaturaResponse) => {
      setSessionItem('assinaturaResponse', JSON.stringify(response));

      const responseAssinaturaValida = validarAssinatura(response);
      dispatch({
        type: EBeneficiariosActionKind.ALTERAR_ASSINATURA_VALIDA,
        assinaturaValida: responseAssinaturaValida,
      });
    };

    return {
      confirmandoEdicao,
      editando,
      dispatch,
      assinaturaValida,
      blocker,
      assinaturaCallback,
      cancelarEdicaoBeneficiarios,
      confirmarEdicaoBeneficiarios,
      salvarBeneficiarios,
    };
  };
