import React, { useEffect } from 'react';

import '@cvp/design-system-caixa/dist/fonts/fontConfig.css';
import { RouterProvider } from 'react-router-dom';
import { setSessionItem } from '@cvp/utils';
import api from '@src/corporativo/infra/config/api/axiosConfig';
import { registerMarcadorControleInterceptors } from '@src/corporativo/infra/config/interceptors/registerMarcadorControleInterceptors';
import router from '@src/corporativo/routes/Router';
import { registerTokenOperadorInterceptor } from '@src/corporativo/infra/config/interceptors/registerTokenOperadorInterceptor';
import { TOKEN_TEMPORARIO } from '@src/corporativo/constants/TokenTemporario';
import AppProviders from '@src/corporativo/context/AppProviders';
import { registerLogProvider } from '@src/corporativo/infra/config/registerLogProvider';

const App: React.FC = () => {
  registerTokenOperadorInterceptor();
  registerMarcadorControleInterceptors(api);
  const setupLogger = registerLogProvider();

  useEffect(() => {
    setSessionItem('marcadorControle', TOKEN_TEMPORARIO);

    return () => {
      setupLogger.destroy();
    };
  }, []);

  return (
    <AppProviders>
      <React.StrictMode>
        <RouterProvider router={router} />
      </React.StrictMode>
    </AppProviders>
  );
};

export default App;
