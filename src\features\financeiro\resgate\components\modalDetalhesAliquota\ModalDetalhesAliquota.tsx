import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ModalDetalhesAliquota = ({
  objetoEmail,
  isOpenModalDetalhamento,
  toggleModalDetalhamento,
  obterDadosPorAliquota,
  customizarLinhasTabela,
}: Resgate.IModalDetalhesAliquotaProps): React.ReactElement => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <Resgate.Dialog
      open={isOpenModalDetalhamento}
      onOpenChange={toggleModalDetalhamento}
    >
      <Resgate.DialogContent>
        <Resgate.Dialog.Header variant="highlight">
          <Resgate.Text variant="heading-small-600">
            {Resgate.TEXTOS_MODAL_DETALHES.TITULO}{' '}
            {Resgate.obterDescricaoAliquota(
              formik.values.aliquotaParaResgateSelecionada,
            )}
          </Resgate.Text>
        </Resgate.Dialog.Header>
        <Resgate.Dialog.Body>
          <Resgate.ContainerResumoDetalhado>
            <Resgate.For each={obterDadosPorAliquota().resumoEstendido}>
              {item => (
                <Resgate.ItemResumoCliente key={item.label}>
                  <Resgate.Text variant="text-standard-600">
                    <strong>{item.label}</strong>
                  </Resgate.Text>
                  <Resgate.Text variant="text-standard-600">
                    {Resgate.getTernaryResult(
                      !!item.mask,
                      Resgate.tryGetMonetaryValueOrDefault([item.value]),
                      item.value,
                    )}
                  </Resgate.Text>
                </Resgate.ItemResumoCliente>
              )}
            </Resgate.For>
          </Resgate.ContainerResumoDetalhado>

          <Resgate.Text variant="text-big-600">
            {Resgate.TEXTOS_MODAL_DETALHES.DETALHES_DESCONTOS}
          </Resgate.Text>

          <Resgate.ResgateTable
            themeTable="cvp-05"
            highlightOnHover
            striped
            responsive
            columns={Resgate.COLUNAS_VALORES_DETALHADOS_ALIQUOTA}
            data={obterDadosPorAliquota().tabelaHistoricoContribuicao}
            noDataComponent={Resgate.TABELA_SEM_DADOS}
            conditionalRowStyles={Resgate.CONDITIONAL_ROW_STYLES_TABELA_VALORES_DETALHADOS_ALIQUOTA.map(
              customizarLinhasTabela,
            )}
            pagination
            paginationPerPage={
              Resgate.TABELA_PAGINACAO_PADRAO.QTD_ITENS_POR_PAGINA
            }
            paginationComponentOptions={{
              rowsPerPageText: Resgate.TABELA_PAGINACAO_PADRAO.TEXTO_POR_PAGINA,
              rangeSeparatorText:
                Resgate.TABELA_PAGINACAO_PADRAO.TEXTO_SEPARADOR,
            }}
          />

          <Resgate.AlertaDetalhesAliquota />
        </Resgate.Dialog.Body>
        <Resgate.Dialog.Footer>
          <Resgate.ModalEnvioEmail objetoEmail={objetoEmail} />
          <Resgate.Button
            onClick={window.print}
            size="standard"
            variant="secondary"
          >
            {Resgate.TEXTOS_MODAL_DETALHES.BOTOES.IMPRIMIR}
          </Resgate.Button>
        </Resgate.Dialog.Footer>
      </Resgate.DialogContent>
    </Resgate.Dialog>
  );
};
