export { default as React } from 'react';
export {
  For,
  Match,
  SwitchCase,
  type IHandleReponseResult,
} from '@cvp/componentes-posvenda';
export {
  Button,
  Checkbox,
  ConditionalRenderer,
  Grid,
  GridItem,
  IconCancelOutlinedSharp,
  IconCheckCircleRound,
  IconEditOutlinedSharp,
  IconWarningRound,
  LoadingSpinner,
  Text,
  TextField,
} from '@cvp/design-system-caixa';

export * as Utils from '@cvp/utils';

export {
  useFormik,
  type FieldInputProps,
  type FormikProps,
  type FormikContextType,
  Form,
  Formik,
  useFormikContext,
} from 'formik';
export * as Yup from 'yup';

export { default as AlertaPrevidencia } from '@src/corporativo/components/AlertaPrevidencia/AlertaPrevidencia';
export { default as Assinatura } from '@src/corporativo/components/Assinatura/Assinatura';
export { Divider } from '@src/corporativo/components/divider';
export { default as MatrizAcessoRenderizadorTodasPermissoes } from '@src/corporativo/components/MatrizAcessoRenderizadorTodasPermissoes';
export { default as IconSave } from '@src/corporativo/components/SaveIcon';

export { CONSENTIMENTO_CONSTANTES } from '@src/corporativo/constants/dadosParticipante/consentimento';
export { ALTERACOES_COMPONENTES_PERMISSOES } from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';
export { useAtualizarNomeSocial } from '@src/corporativo/infra/dadosParticipante/UseAtualizarNomeSocial';
export { obterPayloadNomeSocial } from '@src/features/dadosParticipante/factory/obterPayloadNomeSocial';

export type { IAlertaIdentificado } from '@src/corporativo/context/AlertasContext';
export { MatrizAcessoContext } from '@src/corporativo/context/MatrizAcessoContext';
export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';

export { useRegistrarTokenAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useRegistrarTokenAssinaturaCaixa';
export { useAtualizarConsentimento } from '@src/corporativo/infra/dadosParticipante/UseAtualizarConsentimento';
export { useAtualizarEmail } from '@src/corporativo/infra/dadosParticipante/UseAtualizarEmail';
export { useAtualizarEndereco } from '@src/corporativo/infra/dadosParticipante/UseAtualizarEndereco';
export { useAtualizarTelefone } from '@src/corporativo/infra/dadosParticipante/UseAtualizarTelefone';
export { useBuscarEnderecoCep } from '@src/corporativo/infra/dadosParticipante/UseBuscarEnderecoCep';
export { useObterConsentimento } from '@src/corporativo/infra/dadosParticipante/UseObterConsentimentos';
export type { IDadosParticipanteEntidade } from '@src/corporativo/infra/dadosParticipante/UseObterDadosDoParticipante';

export { useAlertaEdicao } from '@src/shared/hooks/useAlerta';

export {
  type IAlteracaoBase,
  useAlterarDadosParticipante,
} from '@src/corporativo/infra/dadosParticipante/UseAlterarDadosParticipante';

export {
  useObterDadosDoParticipante,
  type ITelefone,
} from '@src/corporativo/infra/dadosParticipante/UseObterDadosDoParticipante';
export type { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
export type {
  IFormDadosParticipante,
  IControlesLGPD,
  IDadosBuscarEnderecoCepResponse,
  IEndereco,
  ITiposConsentimento,
} from '@src/corporativo/types/dadosParticipante';

export { default as useValidarAssinatura } from '@src/shared/hooks/useValidarAssinatura';
export { obterErroTextField } from '@src/shared/utils/form/ObterErroTextField';

export { ALERTAS } from '../constants/alertasDadosParticipante';
export {
  ACEITAR_SMS,
  CODIGO_PAIS,
  DEFAULT_EMPTY_API_PARTICIPANTE,
  EXTENSAO_NUMERO,
  LOCAL_TELEFONE,
  QTD_MAXIMA_DIGITOS_CEP,
  TEXTO_LOADING,
  TIPO_TELEFONE,
  TIPO_EMAIL,
  ERRO_ATUALIZAR_DADOS_PARTICIPANTE,
  EDadosParticipanteActionKind,
} from '../constants/DadosParticipanteRoot';
export {
  DISCLAIMER_CONSENTIMENTO,
  LABEL_CONSENTIMENTO_DADOS_CAIXA,
  LABEL_CONSENTIMENTO_DADOS_PARCEIROS,
  MENSAGEM_ERRO,
  MENSAGEM_SUCESSO,
  VALIDACOES,
} from '../constants/FormDadosParticipante';

export {
  criarStateInicialDadosParticipante,
  dadosParticipanteInitialState,
} from '../utils/criarStateInicialDadosParticipante';

export {
  DadosParticipanteContext,
  DadosParticipanteDispatchContext,
  DadosParticipanteProvider,
} from '../context/dadosParticipanteContext';

export { alertaErroEdicaoDadosParticipante } from '../factory/alertaErroEdicaoDadosParticipante';
export { camposDadosParticipante } from '../factory/camposDadosParticipante';
export { montarDadosEndereco } from '../factory/montarDadosEndereco';
export { montarDadosTelefone } from '../factory/montarDadosTelefone';
export { montarPayloadAtualizarConsentimento } from '../factory/montarPayloadAtualizarDadosConsentimento';

export { dadosParticipanteReducer } from '../hooks/dadosParticipanteReducer';
export { useAtualizarDadosParticipante } from '../hooks/useAtualizarDadosParticipante';
export { useCampoDadosParticipante } from '../hooks/useCampoDadosParticipante';
export { useFormDadosParticipante } from '../hooks/useFormDadosParticipante';

export {
  dadosParticipanteValidationSchema,
  type TFormDadosParticipanteSchema,
} from '../validators/dadosParticipanteValidationSchema';

export * as S from '../styles/styles';

export type {
  IDadosParticipanteFormEntidade,
  IDadosParticipanteState,
} from '../types/IDadosParticipanteState';
export type { TActionsDadosParticipante } from '../types/TActionsDadosParticipante';

export { alterarValorCheckbox } from '../utils/alterarValorCheckbox';
export { checkErroSalvarDadosParticipante } from '../utils/checkSalvarDadosParticipante';

export { obterCallbackComContadorExecucao } from '../utils/obterCallbackComContadorExecucao';
export { validarEnderecoEditado } from '../utils/validarEnderecoEditado';

export { AssinaturaDadosParticipante } from '../views/AssinaturaDadosParticipante';
export { CampoDadosParticipante } from '../views/CampoDadosParticipante';
export { ControlesDadosParticipante } from '../views/ControlesDadosParticipante';
export { FormDadosParticipante } from '../views/FormDadosParticipante';

export { CampoTextoDadosParticipante } from '../views/CampoTextoDadosParticipante';
export { CamposEditaveis } from '../views/CamposEditaveis';
export { ConsentimentoDadosParticipante } from '../views/ConsentimentoDadosParticipante';
