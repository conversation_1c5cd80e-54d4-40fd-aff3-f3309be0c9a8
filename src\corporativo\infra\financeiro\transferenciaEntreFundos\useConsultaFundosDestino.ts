import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { TReservasOrigem } from '@src/corporativo/types/transferencias';
import { IResponseTransferenciaEntreFundos } from '@src/shared/types/ITransferenciaEntreFundoResponse';

const RETORNO_VAZIO = {} as IResponseTransferenciaEntreFundos;

export type TUseConsultaFundosTransferenciaDestino = {
  loading: boolean;
  response: IResponseTransferenciaEntreFundos;
  fetchData: (
    numCertificado: string,
    reservasOrigem: TReservasOrigem[],
    erroCallback?: VoidFunction,
  ) => Promise<void>;
};

type TPayload = {
  cpf: string | null;
};

export function useConsultaFundosTransferenciaDestino(): TUseConsultaFundosTransferenciaDestino {
  const payload = {
    cpf: getSessionItem('cpfCnpj'),
  };

  const { invocarApiGatewayCvpComToken, response, loading } =
    useApiGatewayCvpInvoker<TPayload, IResponseTransferenciaEntreFundos>(
      'PECO_TransferirEntreFundos',
      {
        data: { cpf: String(payload.cpf) },
        autoFetch: false,
      },
    );

  const consultaFundos = async (
    numCertificado: string,
    reservasOrigem: TReservasOrigem[],
  ) => {
    const result = await invocarApiGatewayCvpComToken({
      codContaOrigem: numCertificado,
      codContaDestino: numCertificado,
      reservasOrigem,
    });

    if (!result?.entidade?.reservasDestino?.length)
      throw new Error('Nenhuma reserva encontrada');
  };

  return {
    response: tryGetValueOrDefault([response?.entidade], RETORNO_VAZIO),
    loading,
    fetchData: consultaFundos,
  };
}
export default useConsultaFundosTransferenciaDestino;
