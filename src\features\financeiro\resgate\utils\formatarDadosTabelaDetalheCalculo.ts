import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Formata os dados da tabela de detalhe de cálculo para exibição na interface
 *
 * Esta função recebe um objeto parcial de dados e o transforma em um objeto completo,
 * aplicando formatação monetária aos valores, formatação de data, e preenchendo
 * valores padrão para dados ausentes.
 *
 * @param {Partial<IMontarDadosTabelaDetalheCalculoFactoryRetorno>} item - Objeto parcial com os dados a serem formatados
 * @returns {IMontarDadosTabelaDetalheCalculoFactoryRetorno} Objeto completo com todos os dados formatados para exibição
 */
export const formatarDadosTabelaDetalheCalculo = (
  item: Partial<Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno>,
): Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno => ({
  ...item,
  data: Resgate.getTernaryResult(
    item.data === Resgate.TIPOS_RESGATE.TOTAL.value,
    Resgate.TIPOS_RESGATE.TOTAL.value,
    Resgate.formatarDataHoraAmigavel(item.data),
  ),
  rendimento: Resgate.tryGetMonetaryValueOrDefault(item.rendimento),
  carregamentoSaida: Resgate.tryGetMonetaryValueOrDefault(
    item.carregamentoSaida,
  ),
  baseIrrf: Resgate.tryGetMonetaryValueOrDefault(item.baseIrrf),
  valorIrrf: Resgate.tryGetMonetaryValueOrDefault(item.valorIrrf),
  taxaSaida: Resgate.tryGetMonetaryValueOrDefault(item.taxaSaida),
  aliquotaIrrf: Resgate.tryGetValueOrDefault([item.aliquotaIrrf], ''),
  saldoAporte: Resgate.tryGetMonetaryValueOrDefault(item.saldoAporte),
  saldoPrincipal: Resgate.tryGetMonetaryValueOrDefault(item.saldoPrincipal),
  rendimentos: Resgate.tryGetMonetaryValueOrDefault(item.rendimentos),
  valorCorrecao: Resgate.tryGetMonetaryValueOrDefault(item.valorCorrecao),
  valorLiquido: Resgate.tryGetMonetaryValueOrDefault(item.valorLiquido),
  valorPrincipal: Resgate.tryGetMonetaryValueOrDefault(item.valorPrincipal),
  valorSolicitado: Resgate.tryGetMonetaryValueOrDefault(item.valorSolicitado),
  numeroResgate: Resgate.tryGetValueOrDefault([item.numeroResgate], ''),
  codigoEmpresa: '',
  codigoConta: '',
  codigoFundo: '',
  descricaoFundo: '',
  id: '',
});
