import React from 'react';
import { SwitchCase, Match } from '@cvp/componentes-posvenda';
import { useTransferenciaServicosContext } from '@src/corporativo/context/financeiro/transferencias';
import { Alerta } from '@src/corporativo/components/Alerta';
import { EErroTransferencia } from '@src/corporativo/types/transferencias';
import { TAlertasTransferenciaCases } from '@src/features/financeiro/transferencias/transferenciaEntreFundos/types/TAlertasTransferenciaCases';
import * as CONSTANTES from '@src/features/financeiro/transferencias/constants';
import { formatarValorPadraoBrasileiro } from '@cvp/utils';

export const AlertasTransferenciaCases: React.FC<
  TAlertasTransferenciaCases
> = ({ erro, distribuicaoValores, transferenciaRealizada }) => {
  const { consultaOrigem, mensagemErro } = useTransferenciaServicosContext();

  return (
    <SwitchCase fallback={undefined}>
      <Match when={erro === EErroTransferencia.FluxoInvalido}>
        <Alerta tipo="erro">{mensagemErro}</Alerta>
      </Match>

      <Match when={erro === EErroTransferencia.SemFundosDestino}>
        <Alerta tipo="erro">
          {CONSTANTES.ALERTAS.MENSAGEM_ERRO_SEM_FUNDOS_DESTINO}
        </Alerta>
      </Match>

      <Match when={erro === EErroTransferencia.CodeDestino}>
        <Alerta tipo="erro">
          {CONSTANTES.ALERTAS.MENSAGEM_ERRO_COD_DESTINO}
        </Alerta>
      </Match>

      <Match when={erro === EErroTransferencia.ValorMin}>
        <Alerta tipo="erro">
          O valor mínimo de transferência deve ser{' '}
          {formatarValorPadraoBrasileiro(
            consultaOrigem?.vlrMinTransferencia ?? '0',
          )}
        </Alerta>
      </Match>

      <Match when={erro === EErroTransferencia.ValorRestante}>
        <Alerta tipo="erro">
          {CONSTANTES.ALERTAS.MENSAGEM_ERRO_DISTRIBUICAO.DEVE_SER}
          {formatarValorPadraoBrasileiro(
            distribuicaoValores.somaIntencaoDistribuicaoOrigens,
          )}
          {CONSTANTES.ALERTAS.MENSAGEM_ERRO_DISTRIBUICAO.VOCE_POSSUI}
          {formatarValorPadraoBrasileiro(
            distribuicaoValores.distribuicaoRestante ?? 0.0,
          )}
        </Alerta>
      </Match>

      <Match when={!transferenciaRealizada}>
        <Alerta tipo="atencao">
          <strong>{CONSTANTES.ALERTAS.MENSAGEM_DESTAQUE_ATENCAO}</strong>
          <br />
          {CONSTANTES.ALERTAS.MENSAGEM_ATENCAO}
          <br />
          {CONSTANTES.VALOR_MINIMO_PERMANENCIA(
            consultaOrigem?.response?.retornoCertificado
              ?.vlrMinPermancenciaFundo,
          )}
          <br />
          {CONSTANTES.VALOR_MINIMO_TRANSFERENCIA(
            consultaOrigem?.response?.retornoCertificado?.vlrMinNovoFundo,
          )}
        </Alerta>
      </Match>

      <Match when={erro === EErroTransferencia.ComprovanteTransferencia}>
        <Alerta tipo="erro">
          {CONSTANTES.ALERTAS.MENSAGEM_ERRO_COMPROVANTE_TRANSFERENCIA}
        </Alerta>
      </Match>

      <Match when={transferenciaRealizada}>
        <Alerta tipo="sucesso">
          {CONSTANTES.ALERTAS.MENSAGEM_TRANSFERENCIA_SUCESSO}
        </Alerta>
      </Match>
    </SwitchCase>
  );
};
