import { getSessionItem } from '@cvp/utils';
import { TOKEN_PARSED } from '@src/corporativo/constants/logProvider';

type TContextoAutenticacao = {
  userId: string;
  sessionId: string;
  sessionNavigationId: string;
};

export const obterContextoAutenticacao = (): TContextoAutenticacao => {
  const token = getSessionItem<Record<string, string>>(TOKEN_PARSED);

  if (token) {
    return {
      sessionId: token?.session_state,
      userId: token?.preferred_username,
      sessionNavigationId: '',
    };
  }

  return {} as TContextoAutenticacao;
};
