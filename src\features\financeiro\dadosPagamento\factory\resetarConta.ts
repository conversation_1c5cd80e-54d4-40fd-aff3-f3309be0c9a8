import {
  CONTA_INICIAL,
  TNovaConta,
  TResetarContaPorResponseParams,
} from '@src/features/financeiro/dadosPagamento/exports';

export const resetarContaPorResponse = (
  params: TResetarContaPorResponseParams,
): Partial<TNovaConta> => {
  return {
    ...(params.retornoAtualizaDiaVencimento?.sucessoBFF &&
      params.retornoAtualizaDiaVencimento?.sucessoGI && {
        diaVencimento: '',
      }),
    ...(params.retornoAtualizacaoFormaPagamento?.sucessoBFF &&
      params.retornoAtualizacaoFormaPagamento?.sucessoGI && {
        formaPagamento: '',
      }),
    ...(params.retornoValidarConta && {
      ...CONTA_INICIAL,
    }),
  };
};
