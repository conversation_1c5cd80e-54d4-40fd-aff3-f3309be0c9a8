import { TTiposDePerfis } from '@src/shared/types/TTiposDePerfis';

export interface IListarFundosParaResgateFundosDisponiveis {
  permiteResgate: boolean;
  codigoReserva: string;
  descricaoReserva: string;
  codigoFundo: string;
  descricaoFundo: string;
  saldoTotal: number;
  saldoDisponivel: number;
  saldoBloqueado: number;
  valorMinimoPermanencia: number;
  valorRentabilidadeDozeMeses: number;
  perfilFundo: TTiposDePerfis;
  riscoFundo: string;
  exibeMensagem: boolean;
  mensagensFundo: IListarFundosParaResgateFundosDisponiveisMensagensFundo[];
  carencia: null;
  valorRetirar?: string;
  selecionado?: boolean;
  tipoResgate?: string;
}

export interface IListarFundosParaResgateFundosDisponiveisMensagensFundo {
  tipo: string;
  codigo: string;
  descricao: string;
}

export interface IFundosParaResgateFactory {
  permiteResgate: boolean;
  codigoReserva: string;
  descricaoReserva: string;
  codigoFundo: string;
  descricaoFundo: string;
  saldoTotal: number;
  saldoDisponivel: string;
  saldoBloqueado: number;
  valorMinimoPermanencia: number;
  valorRentabilidadeDozeMeses: string;
  perfilFundo: React.ReactElement;
  riscoFundo: string;
  exibeMensagem: boolean;
  mensagensFundo: IListarFundosParaResgateFundosDisponiveisMensagensFundo[];
  carencia: null;
  valorRetirar?: string;
  selecionado?: boolean;
  tipoResgate?: string;
  taxaAdm: string;
  selecionar: React.ReactElement;
  valorResgatado: React.ReactElement;
}
