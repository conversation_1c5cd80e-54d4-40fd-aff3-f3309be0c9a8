import { getTernaryResult } from '@cvp/utils';
import {
  ENVIRONMENTS_NAME,
  For,
  MultiAccordion,
  Outlet,
  Text,
  useAccordionRouter,
} from '@src/corporativo/exports';
import { TRoute } from '@src/corporativo/routes/types/RoutesTypes';

interface ILayoutFuncionalidadesAgrupadasProps {
  routesLayout: TRoute[];
}

export const LayoutFuncionalidadesAgrupadas: React.FC<
  ILayoutFuncionalidadesAgrupadasProps
> = ({ routesLayout }) => {
  const { onChangeRoute } = useAccordionRouter();

  const filteredRoutes = getTernaryResult(
    AppConfig?.MFE_ENV !== ENVIRONMENTS_NAME.DEVELOPMENT,
    [routesLayout[0]],
    routesLayout,
  );

  return (
    <MultiAccordion.Root>
      <For each={filteredRoutes}>
        {route => (
          <MultiAccordion.Item value={route.path} key={route.path}>
            <MultiAccordion.Trigger
              onClick={() => {
                onChangeRoute(route.path);
              }}
              style={{
                justifyContent: 'space-between',
              }}
            >
              <Text variant="text-big-600" lineheight="farther">
                {route.name}
              </Text>
            </MultiAccordion.Trigger>

            <MultiAccordion.Content style={{ width: '100%' }}>
              <Outlet />
            </MultiAccordion.Content>
          </MultiAccordion.Item>
        )}
      </For>
    </MultiAccordion.Root>
  );
};
