import {
  GridItem,
  Match,
  ModalNaoInformarRecursoAporte,
  ORIGEM_RECURSOS,
  ORIGEM_RECURSOS_DECLARACAO,
  RadioGroup,
  RadioItem,
  RadioLabel,
  ScrollArea,
  Text,
  TextArea,
} from '@src/features/financeiro/aporte/exports';

interface IOpcoesOrigemRecursos {
  openModal: boolean;
  openTextArea: boolean;
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleOrigemRecursoEditada: (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => void;
  handleOrigemRecurso: (valueRadio: string) => void;
}

const OpcoesOrigemRecursos: React.FC<IOpcoesOrigemRecursos> = ({
  openModal,
  openTextArea,
  setOpenModal,
  handleOrigemRecursoEditada,
  handleOrigemRecurso,
}) => {
  return (
    <>
      <Text variant="text-large-400" fontColor="content-neutral-04">
        {ORIGEM_RECURSOS_DECLARACAO.declaracao}
      </Text>
      <Text variant="text-large-600" fontColor="content-neutral-05">
        {ORIGEM_RECURSOS_DECLARACAO.declaracaoOrigem}
      </Text>
      <GridItem>
        <ScrollArea height="calc(60vh - 200px)">
          <RadioGroup
            defaultValue="primeiro"
            direction="column"
            name="groupExemple"
            onValueChange={handleOrigemRecurso}
          >
            {ORIGEM_RECURSOS.map(item => {
              if (item.descricao === 'Não desejo informar') {
                return (
                  <ModalNaoInformarRecursoAporte
                    open={openModal}
                    toggle={() => setOpenModal(toggle => !toggle)}
                  >
                    <RadioItem
                      id={item.id}
                      rightLabel={
                        <RadioLabel htmlFor={item.id} size="standard">
                          {item.descricao}
                        </RadioLabel>
                      }
                      value={item.descricao}
                      variant="highlight"
                    />
                  </ModalNaoInformarRecursoAporte>
                );
              }
              return (
                <RadioItem
                  id={item.id}
                  rightLabel={
                    <RadioLabel htmlFor={item.id} size="standard">
                      {item.descricao}
                    </RadioLabel>
                  }
                  value={item.descricao}
                  variant="highlight"
                />
              );
            })}

            <Match when={openTextArea}>
              <TextArea
                id="story"
                name="story"
                rows={5}
                onChange={handleOrigemRecursoEditada}
                cols={33}
                label="Digite ou a origem do recurso no campo abaixo:"
                maxLength={50}
                size="standard"
              />
            </Match>
          </RadioGroup>
        </ScrollArea>
      </GridItem>
    </>
  );
};

export default OpcoesOrigemRecursos;
