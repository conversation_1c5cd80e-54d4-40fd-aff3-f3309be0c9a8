import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';
import { IUseCertificadosPrevidenciaDetalhes } from '@src/corporativo/types/consultaCertificado/IUseCertificadosPrevidenciaDetalhes';
import { IUseCertificadosDetalhesRequest } from '@src/corporativo/types/consultaCertificado/IUseCertificadosPrevidenciaDetalhesRequest';
import { ICertificadoPrevidenciaDetalhesResponse } from '@src/shared/types/ICertificadoPrevidenciaDetalhesResponse';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const ARRAY_VAZIO = [] as ICertificadoPrevidenciaDetalhesResponse[];

export const useCertificadoPrevidenciaDetalhe: IUseCertificadosPrevidenciaDetalhes =
  payload => {
    const { loading, response } = useApiGatewayCvpInvoker<
      IUseCertificadosDetalhesRequest,
      ICertificadoPrevidenciaDetalhesResponse[]
    >(PECOS.CertificadoPrevidenciaDetalhar, {
      data: {
        cpf: tryGetValueOrDefault([payload?.cpf], '0'),
      },
      autoFetch: true,
    });

    return {
      loading,
      response: tryGetValueOrDefault([response?.entidade], ARRAY_VAZIO),
    };
  };
