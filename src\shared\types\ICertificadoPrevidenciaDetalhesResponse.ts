import { IProdutoPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IProdutoPrevidencia';
import { IFundo } from '@src/corporativo/types/consultaCertificado/Response/IFundo';
import { IPessoa } from '@src/corporativo/types/consultaCertificado/Response/IPessoa';

export interface ICertificadoPrevidenciaDetalhesResponse {
  codConta: string;
  pessoa: IPessoa;
  codEmpresa: string;
  produto: IProdutoPrevidencia;
  tributacao: string;
  status: string;
  fundo: IFundo;
  dthEmissao: Date;
  dthAposentadoria: Date;
  indTitular: IIndicador;
  indResposavelFinanceiro: IIndicador;
  indPermiteAporte: IIndicador;
  fundos: IFundo[];
}

export interface IIndicador {
  N: 'N';
  S: 'S';
}
