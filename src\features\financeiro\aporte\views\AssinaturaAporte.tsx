import {
  Assinatura,
  ASSINATURA,
  ASSINATURA_SESSION_KEY,
  checkIfAllItemsAreTrue,
  getSessionItem,
  Grid,
  GridItemContainer,
  IAssinaturaResponse,
  PrevidenciaContext,
  setSessionItem,
  Text,
  tryGetValueOrDefault,
  useAporteContext,
  useContext,
  useRegistrarTokenAssinaturaCaixa,
  useValidarAssinatura,
  ConditionalRenderer,
} from '@src/features/financeiro/aporte/exports';

const AssinaturaAporte: React.FC = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const cpfCnpj = String(getSessionItem('cpfCnpj'));

  const { setAssinatura } = useAporteContext();

  const { assinaturaValida, validarAssinatura } = useValidarAssinatura();
  const { invocarApiGatewayCvpComToken } = useRegistrarTokenAssinaturaCaixa();

  const assinaturaCallback = async (
    response: IAssinaturaResponse,
  ): Promise<void> => {
    await invocarApiGatewayCvpComToken(response);
    const resultadoValidarAssinatura = validarAssinatura(response);
    setAssinatura(resultadoValidarAssinatura);
    setSessionItem(ASSINATURA_SESSION_KEY, response);
  };

  return (
    <Grid>
      <GridItemContainer xs="1/2">
        <Text
          variant="heading-tiny-600"
          fontColor="content-neutral-05"
          marginTop="26.5px"
          marginBottom="26.5px"
        >
          {ASSINATURA.AUTENTICACAO}
        </Text>
        <ConditionalRenderer
          condition={checkIfAllItemsAreTrue([
            !assinaturaValida,
            !!cpfCnpj,
            !!certificadoAtivo?.numeroApolice,
          ])}
        >
          {ASSINATURA.INVALIDA}
        </ConditionalRenderer>
        <Assinatura
          dados={{
            cpfCnpj: tryGetValueOrDefault([cpfCnpj], ''),
            numeroCertificado: tryGetValueOrDefault(
              [certificadoAtivo?.certificadoNumero],
              '',
            ),
          }}
          callback={assinaturaCallback}
        />
      </GridItemContainer>
    </Grid>
  );
};

export default AssinaturaAporte;
