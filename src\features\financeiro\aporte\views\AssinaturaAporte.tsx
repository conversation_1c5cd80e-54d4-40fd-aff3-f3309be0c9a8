import {
  Assinatura,
  ASSINATURA,
  checkIfAllItemsAreTrue,
  getSessionItem,
  Grid,
  GridItemContainer,
  IAssinaturaResponse,
  Match,
  PrevidenciaContext,
  SwitchCase,
  Text,
  tryGetValueOrDefault,
  useAporteContext,
  useContext,
  useRegistrarTokenAssinaturaCaixa,
  useValidarAssinatura,
} from '@src/features/financeiro/aporte/exports';

const AssinaturaAporte: React.FC = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const cpfCnpj = String(getSessionItem('cpfCnpj'));

  const { setAssinatura } = useAporteContext();

  const { assinaturaValida, validarAssinatura } = useValidarAssinatura();
  const { invocarApiGatewayCvpComToken } = useRegistrarTokenAssinaturaCaixa();

  const assinaturaCallback = (response: IAssinaturaResponse) => {
    invocarApiGatewayCvpComToken(response);
    validarAssinatura(response);
    setAssinatura(assinaturaValida);
  };
  return (
    <Grid>
      <GridItemContainer xs="1/2">
        <Text
          variant="heading-tiny-600"
          fontColor="content-neutral-05"
          marginTop="26,5px"
          marginBottom="26,5px"
        >
          {ASSINATURA.AUTENTICACAO}
        </Text>
        <SwitchCase fallback={undefined}>
          <Match
            when={
              !assinaturaValida &&
              checkIfAllItemsAreTrue([
                !!cpfCnpj,
                !!certificadoAtivo?.numeroApolice,
              ])
            }
          >
            {ASSINATURA.INVALIDA}
          </Match>
        </SwitchCase>
        <Assinatura
          dados={{
            cpfCnpj: tryGetValueOrDefault([cpfCnpj], ''),
            numeroCertificado: tryGetValueOrDefault(
              [certificadoAtivo?.certificadoNumero],
              '',
            ),
          }}
          callback={assinaturaCallback}
        />
      </GridItemContainer>
    </Grid>
  );
};

export default AssinaturaAporte;
