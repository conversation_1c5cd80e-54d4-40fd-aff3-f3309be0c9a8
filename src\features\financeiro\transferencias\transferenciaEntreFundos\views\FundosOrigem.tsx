import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const TransferenciaEntreFundosOrigem: React.FC = () => {
  const { consultaOrigem } =
    TransferenciaEntreFundos.useTransferenciaServicosContext();
  const transferenciaOrigem = TransferenciaEntreFundos.useTransferenciaOrigem();
  const transferenciaContext =
    TransferenciaEntreFundos.useTransferenciaContext();
  const data = TransferenciaEntreFundos.transferenciaEntreFundosOrigemData(
    consultaOrigem.fundos,
    transferenciaOrigem,
    transferenciaContext,
  );

  return (
    <TransferenciaEntreFundos.SwitchCase fallback={undefined}>
      <TransferenciaEntreFundos.Match when={!consultaOrigem.fundos?.length}>
        <TransferenciaEntreFundos.Text
          variant="text-standard-700"
          fontColor="content-neutral-04"
          textAlign="center"
          marginTop="40px"
        >
          {TransferenciaEntreFundos.CONSTANTES.MENSAGEM_FUNDO_INDISPONIVEL}
        </TransferenciaEntreFundos.Text>
      </TransferenciaEntreFundos.Match>
      <TransferenciaEntreFundos.Match
        when={TransferenciaEntreFundos.checkIfAllItemsAreTrue([
          !consultaOrigem.loading,
          !!consultaOrigem.response,
        ])}
      >
        <TransferenciaEntreFundos.Text
          variant="text-standard-700"
          fontColor="content-neutral-04"
          marginBottom="1rem"
        >
          {TransferenciaEntreFundos.CONSTANTES.SESSOES.ORIGEM_TITULO}
        </TransferenciaEntreFundos.Text>

        <TransferenciaEntreFundos.TableTranferenciaEntreFundos
          themeTable="cvp-05"
          columns={TransferenciaEntreFundos.trasferenciaEntreFundosColunas}
          data={data}
        />
      </TransferenciaEntreFundos.Match>
    </TransferenciaEntreFundos.SwitchCase>
  );
};
