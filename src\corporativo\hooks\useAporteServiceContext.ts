import { useContext } from 'react';
import AporteServiceContext from '../context/financeiro/aporte/AporteServiceContext';
import { TAporteServiceContext } from '../types/financeiro/aporte/TAporteServiceContext';

export const useAporteServiceContext = (): TAporteServiceContext => {
  const context = useContext(AporteServiceContext);
  if (!context) {
    throw new Error(
      'useAporteServiceContext deve ser usado dentro do AporteServiceProvider',
    );
  }
  return context;
};
