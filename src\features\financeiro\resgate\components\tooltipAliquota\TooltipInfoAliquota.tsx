import * as Resgate from '@src/features/financeiro/resgate/exports';

export const TooltipInfoAliquota = ({
  tipoAliquota,
}: Resgate.ITooltipInfoAliquotaProps): React.ReactElement => {
  return (
    <Resgate.ToolTip
      text={
        Resgate.obterConteudoTooltip(
          Resgate.tryGetValueOrDefault([tipoAliquota], ''),
        ) as Resgate.ReactNode as string
      }
    >
      <Resgate.IconInfoOutlined size="small" />
    </Resgate.ToolTip>
  );
};
