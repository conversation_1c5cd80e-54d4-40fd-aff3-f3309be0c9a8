import { IMatrizAcessoRenderizadorTodasPermissoes } from '@src/corporativo/types/matrizAcesso/IMatrizAcessoRenderizadorTodasPermissoes';
import { verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias } from '@src/corporativo/utils/matrizAcesso';

const MatrizAcessoRenderizadorTodasPermissoes: React.FC<
  IMatrizAcessoRenderizadorTodasPermissoes
> = ({ children, permissoesComponente, matrizAcesso }) => {
  const componentePossuiPermissao =
    verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      matrizAcesso,
      permissoesComponente,
    );

  if (!componentePossuiPermissao) return null;

  return children;
};

export default MatrizAcessoRenderizadorTodasPermissoes;
