import * as Resgate from '@src/features/financeiro/resgate/exports';

export const TabelasEscolhaAliquotas = ({
  aliquota,
  obterDadosPorAliquota,
  validarAliquotaDesabilitada,
  selecionarOpcaoAliquota,
}: Resgate.ITabelasEscolhaAliquotasProps): React.ReactElement => {
  return (
    <Resgate.Grid style={{ width: '100%' }}>
      <Resgate.For
        each={Resgate.tryGetValueOrDefault([aliquota?.opcoesAliquotas], [])}
      >
        {tipoAliquota => (
          <Resgate.ConditionalRenderer
            condition={
              obterDadosPorAliquota(tipoAliquota.codigoAliquota)
                .isExibeTabelaAliquota
            }
          >
            <Resgate.GridItem xs="1" lg="1 / 2">
              <Resgate.CabecalhoEscolhaTabelaAliquota
                tipoAliquota={tipoAliquota}
                aliquota={aliquota}
                selecionarOpcaoAliquota={selecionarOpcaoAliquota}
                validarAliquotaDesabilitada={validarAliquotaDesabilitada}
              />

              <div style={{ width: '100%' }}>
                <Resgate.TabelaResumoSimulacao
                  dadosResumo={
                    obterDadosPorAliquota(tipoAliquota.codigoAliquota).resumo
                  }
                  $disabled={validarAliquotaDesabilitada(
                    tipoAliquota.codigoAliquota,
                  )}
                />
              </div>
            </Resgate.GridItem>
          </Resgate.ConditionalRenderer>
        )}
      </Resgate.For>
    </Resgate.Grid>
  );
};
