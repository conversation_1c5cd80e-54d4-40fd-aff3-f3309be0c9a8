import { Table, Dialog, styled, Button } from '../exports';

export const TableTranferenciaEntreFundos = styled(Table)`
  border-radius: 0;

  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: #ebf1f2;
      border: none;

      .rdt_TableCol_Sortable {
        font-weight: 400;
        color: #64747a;
      }
    }
  }

  .rdt_TableRow {
    min-height: 90px;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: #d0e0e3;
    margin-bottom: 0;
  }

  .rdt_TableCell > div {
    color: #404b52;
    text-overflow: initial;
    white-space: normal;
    overflow: initial;
  }
`;

export const FooterModal = styled(Dialog.Footer)`
  border-radius: 0;
  padding: 0 16px 16px 16px;
  button {
    width: 65px;
  }
`;

export const Modal: React.JSX.Element = styled(Dialog)`
  max-height: 249px;
  border-radius: 0;
`;

export const HeaderModal = styled(Dialog.Header)`
  background-color: #005ca9 !important;
  color: #ffffff;
  padding: 5px;
  padding-left: 20px;
  height: 62px;
  border-radius: 0;
  box-shadow: none;
  border-bottom: none;

  button {
    background-color: #005ca9;
    padding: 5px;
    padding-left: 20px;
    svg {
      width: 21px;
      color: #ffffff;
    }
  }
`;

export const DestinoAcoesButton = styled(Button)(({ theme }) => ({
  border: 'none',
  color: `${theme.color.palette.primary['90']}`,
}));
