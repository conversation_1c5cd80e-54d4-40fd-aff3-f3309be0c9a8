import * as Resgate from '@src/features/financeiro/resgate/exports';

export const CabecalhoEscolhaTabelaAliquota = ({
  tipoAliquota,
  aliquota,
  selecionarOpcaoAliquota,
  validarAliquotaDesabilitada,
}: Resgate.ICabecalhoEscolhaTabelaAliquotaProps): React.ReactElement => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <Resgate.HeadingCardSimulacao
      variant="heading-tiny-600"
      $disabled={validarAliquotaDesabilitada(tipoAliquota.codigoAliquota)}
    >
      <Resgate.ConditionalRenderer
        condition={aliquota?.indicadorPermiteEditarAliquota}
      >
        <Resgate.RadioGroup
          direction="row"
          name="tiposAliquota"
          value={formik.values.aliquotaParaResgateSelecionada}
          onValueChange={selecionarOpcaoAliquota}
        >
          <Resgate.RadioItem
            variant="highlight"
            id={tipoAliquota.codigoAliquota}
            value={tipoAliquota.codigoAliquota}
          />
        </Resgate.RadioGroup>
      </Resgate.ConditionalRenderer>

      <span className="Heading__Aliquota">
        Alíquota {tipoAliquota.descricaoAliquota}{' '}
      </span>

      <Resgate.TooltipInfoAliquota tipoAliquota={tipoAliquota.codigoAliquota} />
    </Resgate.HeadingCardSimulacao>
  );
};
