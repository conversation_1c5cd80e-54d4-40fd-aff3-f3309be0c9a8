import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import {
  checkIfSomeItemsAreTrue,
  getSessionItem,
  tryGetValueOrDefault,
} from '@cvp/utils';
import { IResponseRevalidarTransferenciaEntidade } from '@src/corporativo/types/consultaCertificado/Response/IRevalidarTransferencia';

type TPayload = {
  cpf: string;
};

type TRequestRevalidarTransferencia = {
  numTransferencia: string;
};

export type TUseRevalidarTransferencia = {
  loading: boolean;
  response:
    | IHandleReponseResult<IResponseRevalidarTransferenciaEntidade>
    | undefined;
  fetchData: (
    request: TRequestRevalidarTransferencia,
  ) => Promise<
    IHandleReponseResult<IResponseRevalidarTransferenciaEntidade> | undefined
  >;
};

function useRevalidarTransferencia(): TUseRevalidarTransferencia {
  const payload = {
    cpf: getSessionItem('cpfCnpj'),
  };

  const { response, invocarApiGatewayCvpComToken, loading } =
    useApiGatewayCvpInvoker<TPayload, IResponseRevalidarTransferenciaEntidade>(
      'PECO_RevalidarTransferencia',
      {
        data: { cpf: String(payload.cpf) },
        autoFetch: false,
      },
    );

  async function revalidarTransferencia(
    parametros: TRequestRevalidarTransferencia,
  ) {
    const result:
      | IHandleReponseResult<IResponseRevalidarTransferenciaEntidade>
      | undefined = await invocarApiGatewayCvpComToken(parametros);

    if (
      checkIfSomeItemsAreTrue([
        !result?.sucessoBFF,
        !result?.sucessoGI,
        tryGetValueOrDefault(
          [result?.entidade?.statusRevalidarTransferencia],
          '',
        ) === 'PND',
      ])
    ) {
      const [pendencia] = tryGetValueOrDefault(
        [result?.entidade?.pendenciasRevalidarTransferencia],
        [
          {
            descricaoPendencia: 'Erro ao revalidar transferência',
            codigoErro: '',
          },
        ],
      );

      throw new Error(pendencia.descricaoPendencia);
    }
    return result;
  }

  return {
    response,
    fetchData: revalidarTransferencia,
    loading,
  };
}

export default useRevalidarTransferencia;
