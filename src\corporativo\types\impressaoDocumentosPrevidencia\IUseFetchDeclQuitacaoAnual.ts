import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IUseFetchDeclaracaoQuitacaoAnualPayload {
  ano: string;
  numeroCertificado: string;
  cpfCnpj: string;
}

export interface IUseFetchDeclaracaoQuitacaoAnualResponse {
  isDeclaracaoQuitacaoAnualLoading: boolean;
  fetchDeclaracaoQuitacaoAnual: (
    dynamicPayload?: IUseFetchDeclaracaoQuitacaoAnualPayload,
  ) => Promise<IHandleReponseResult<string> | undefined>;
}
