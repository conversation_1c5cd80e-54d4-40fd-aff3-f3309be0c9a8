import {
  converterBase64,
  EEtapasAporte,
  ENVIO_IMPRESSAO,
  FILTRO_TABELA_INITIAL_STATE,
  getSessionItem,
  getTernaryResult,
  IDadosTabelaFundos,
  IFiltroTabelaAporte,
  PrevidenciaContext,
  TIPO_FUNDO,
  tipoEmailConstants,
  tryGetValueOrDefault,
  useAporteContext,
  useAporteServiceContext,
  useContext,
  useNavigate,
} from '@src/features/financeiro/aporte/exports';
import { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';

type TUseComprovanteAporte = () => {
  filtroTabelaFundos: IFiltroTabelaAporte;
  fundosExistentesSelecionados: IDadosTabelaFundos;
  fundosNovosSelecionados: IDadosTabelaFundos;
  handleImprimirBoleto: () => void;
  handleImprimirComprovanteAporte: () => Promise<void>;
  handleFinalizarAtendimento: () => void;
};

export const useComprovanteAporte: TUseComprovanteAporte = () => {
  const {
    fundos,
    fundoSelecionado,
    formikFiltrosTabelaAporte,
    formikValorDistribuido,
    filtroTabelaFundos,
    aporte,
    setFiltroTabelaAporte,
    setEtapa,
  } = useAporteContext();

  const {
    obterComprovanteResgate: { obterComprovanteResgate },
  } = useAporteServiceContext();

  const cpfCnpj = String(getSessionItem('cpfCnpj'));

  const navigate = useNavigate();

  const { certificadoAtivo, setImpressao, setParametrosScroll } =
    useContext(PrevidenciaContext);

  const imprimirBoletoEComprovante = (
    base64: string,
    tipoDocumento: string,
  ) => {
    const urlPdf = URL.createObjectURL(converterBase64(base64));
    const rota = window.location.pathname;
    const valorScroll = window.scrollY;

    setImpressao({
      tipoDocumento: getTernaryResult(
        tipoDocumento === ENVIO_IMPRESSAO.boleto,
        ENVIO_IMPRESSAO.boleto,
        ENVIO_IMPRESSAO.comprovante,
      ),
      tipoEmail: tipoEmailConstants.COMPROVANTE_APORTE_PREVIDENCIA,
      urlPdf,
      parametrosEnvio: {
        NumeroCobranca: aporte?.trabalhoId,
        NumeroSolicitacao: aporte?.numeroSolicitacao,
        TipoPagamento: filtroTabelaFundos.formaPagamento?.toUpperCase(),
        codigoRequisicao: ENVIO_IMPRESSAO.codigoRequisicao,
        cpfCnpj,
        numeroCertificado: certificadoAtivo?.certificadoNumero,
      },
      base64: [base64],
    });

    setParametrosScroll({
      rota,
      valorScroll,
    });

    setParametrosScroll({
      rota,
      valorScroll,
    });

    navigate(ROUTES.IMPRIMIR);
  };

  const handleImprimirBoleto = () => {
    imprimirBoletoEComprovante(
      tryGetValueOrDefault([aporte?.boletoPdf], ''),
      ENVIO_IMPRESSAO.boleto,
    );
  };

  const handleImprimirComprovanteAporte = async () => {
    const response = await obterComprovanteResgate({
      codigoRequisicao: ENVIO_IMPRESSAO.codigoRequisicao,
      idRequisicao: tryGetValueOrDefault([aporte?.numeroSolicitacao], ''),
    });

    imprimirBoletoEComprovante(
      tryGetValueOrDefault([response?.entidade?.comprovante], ''),
      ENVIO_IMPRESSAO.comprovante,
    );
  };

  const handleFinalizarAtendimento = () => {
    setFiltroTabelaAporte(FILTRO_TABELA_INITIAL_STATE);
    formikFiltrosTabelaAporte.resetForm();
    formikValorDistribuido.resetForm();
    setEtapa(EEtapasAporte.FiltrosTabelaAporte);
  };

  const fundosExistentes = fundos?.dadosFundosExistentes;

  const fundosExistentesSelecionados = {
    ...fundosExistentes,
    dadosFundos: fundoSelecionado.filter(
      fundo => fundo.tipoFundo === TIPO_FUNDO.existente,
    ),
  };

  const fundosNovos = fundos?.dadosFundosNovos;

  const fundosNovosSelecionados = {
    ...fundosNovos,
    dadosFundos: fundoSelecionado.filter(
      fundo => fundo.tipoFundo === TIPO_FUNDO.novo,
    ),
  };

  return {
    filtroTabelaFundos,
    fundosExistentesSelecionados,
    fundosNovosSelecionados,
    handleImprimirBoleto,
    handleImprimirComprovanteAporte,
    handleFinalizarAtendimento,
  };
};
