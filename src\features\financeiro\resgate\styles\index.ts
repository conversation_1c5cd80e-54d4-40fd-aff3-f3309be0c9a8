import {
  Dialog,
  Table,
  Text,
  styled,
  Grid,
} from '@src/features/financeiro/resgate/exports';

export const ResgateTable = styled(Table)`
  border-radius: 0;

  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: ${({ theme }) => theme.color.background.neutral['03']};
      border: none;

      .rdt_TableCol_Sortable {
        font-weight: 400;
        color: ${({ theme }) => theme.color.content.neutral['03']};
        overflow: initial;
        text-align: center;

        div {
          overflow: initial;
          white-space: initial;
          text-overflow: initial;
        }
      }
    }
  }

  .rdt_TableRow,
  .rdt_TableRow:first-child,
  .rdt_TableRow:last-child {
    min-height: 100px;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-bottom-color: ${({ theme }) => theme.color.border.neutral['03']};
    margin-bottom: 0;
    margin-top: 0;
  }

  .rdt_TableCell > div {
    text-overflow: initial;
    white-space: normal;
    overflow: initial;
  }
`;

export const ContainerResumoResgate = styled.div(() => ({
  display: 'flex',
  justifyContent: 'space-between',
  flexWrap: 'wrap',
  padding: '20px 10px',

  'div:last-child': {
    textAlign: 'right',

    button: {
      marginTop: 20,
    },
  },
}));

export const ContainerEscolhaTipoResgate = styled.div(() => ({
  display: 'flex',
  flexDirection: 'column',

  'div:first-child': {
    flex: 1,
  },

  'div:last-child': {
    flex: 2,
  },
}));

export const ContainerInputValorResgatado = styled.div(() => ({
  display: 'flex',
  flexDirection: 'column',
}));

export const ContainerCardAliquota = styled.div`
  #valoresDetalhados {
    margin: 20px;
  }
`;

export const ContainerSelecaoAliquota = styled.div<{ $disabled?: boolean }>(
  ({ $disabled, theme }) => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '20px',
    borderTopLeftRadius: '16px',
    borderTopRightRadius: '16px',
    gap: '0.4rem',
    color: theme.color.brandPrimary.light,

    '& label': {
      marginRight: '5px',
    },

    'button[role=radio]': {
      borderColor: theme.color.brandPrimary.light,
      ...($disabled && {
        cursor: 'default',
        opacity: 0.5,
      }),
    },

    'button[data-state=checked]': {
      '& span': {
        height: '12px',
        width: '12px',
      },
    },

    ...($disabled && {
      '.Heading__Aliquota': {
        opacity: 0.5,
      },
      svg: {
        opacity: 0.5,
      },
    }),
  }),
);

export const HeadingCardSimulacao = styled(Text)<{ $disabled?: boolean }>(
  ({ theme, $disabled }) => ({
    display: 'flex',
    columnGap: '10px',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0,
    height: '50px',
    backgroundColor: `${theme.color.background.neutral['02']}`,

    ...(!!$disabled && {
      opacity: 0.4,
    }),
  }),
);

export const TooltipContainer = styled.div`
  width: 15px;
  svg {
    width: 22px;
    height: 22px;
  }
`;

export const ContainerTableAliquota = styled.div<{ $disabled?: boolean }>(
  ({ $disabled, theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    borderBottom: `1px solid ${theme.color.border.neutral['03']}`,
    padding: '8px 20px',
    transition: '200ms',

    '&:nth-child(even)': {
      backgroundColor: `${theme.color.background.neutral['02']}`,
    },

    ...(!$disabled && {
      '&:hover': {
        backgroundColor: `${theme.color.background.neutral['04']}`,
        transition: '200ms',
      },
    }),

    ...(!!$disabled && {
      opacity: 0.4,
    }),
  }),
);

export const ContainerTabelaResumoResgate = styled.div(({ theme }) => ({
  '& > :first-child': {
    padding: '8px 20px',
    backgroundColor: `${theme.color.background.neutral['04']}`,
    marginBottom: '10px',
    display: 'flex',
    gridGap: '10px',
    alignItems: 'center',
  },
  '& > :nth-child(2)': {
    padding: '8px 20px',
    backgroundColor: `${theme.color.background.neutral['04']}`,
    margin: 0,
  },
}));

export const ContainerButtons = styled.div(() => ({
  display: 'flex',
  justifyContent: 'flex-end',
  columnGap: '10px',
}));

export const ItemResumoCliente = styled.div(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  padding: '10px',
  borderBottom: `1px solid ${theme.color.border.neutral['03']}`,
  textAlign: 'left',
  transition: '200ms',

  '&:hover': {
    backgroundColor: theme.color.background.neutral['04'],
    transition: '200ms',
  },
}));

export const DialogContent = styled(Dialog.Content)<{
  $initialHeight?: boolean;
}>(({ $initialHeight }) => ({
  maxWidth: 'initial',
  maxHeight: 'initial',
  overflow: 'auto',
  height: $initialHeight ? 'initial' : '95vh',

  '& ul': {
    margin: '0 20px',
  },

  '& ul > li': {
    listStyle: 'disc',
    marginBottom: '10px',
  },
}));

export const ContainerResumoDetalhado = styled.div(() => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(2, 1fr)',
  gridColumnGap: '20px',
  marginBottom: '30px',
}));

export const ContainerTooltipRegressiva = styled.div`
  width: 400px;
  padding: 5px;
  color: ${({ theme }) => theme.color.content.neutral['05']};

  p {
    text-align: justify;
    font-size: 14px;
    line-height: 1.6;
  }

  table {
    margin-top: 1rem;
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      border: 1px solid ${({ theme }) => theme.color.border.neutral['03']};
      padding: 8px 12px;
      text-align: left;
      font-size: 14px;
    }

    th {
      background-color: ${({ theme }) => theme.color.background.neutral['03']};
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: ${({ theme }) => theme.color.background.neutral['02']};
    }
  }
`;

export const ContainerTooltipProgressiva = styled.div`
  width: 300px;
  padding: 5px;
  color: ${({ theme }) => theme.color.content.neutral['05']};

  p {
    text-align: justify;
    font-size: 14px;
    line-height: 1.6;
  }
`;

export const ContainerFormNovaConta = styled.div(() => ({
  display: 'flex',
  flexWrap: 'wrap',
}));

export const ContainerSelectFormBancarioCustom = styled.div(() => ({
  button: {
    minWidth: '100%',
  },
}));

export const ContainerInputFormBancarioCustom = styled.div(() => ({
  input: {
    height: '40px',
  },
}));

export const GridContaBancariaCustom = styled(Grid)(() => ({
  height: 'initial',
}));
