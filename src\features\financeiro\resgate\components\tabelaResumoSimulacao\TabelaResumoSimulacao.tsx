import * as Resgate from '@src/features/financeiro/resgate/exports';

export const TabelaResumoSimulacao = ({
  dadosResumo,
  $disabled,
}: Resgate.ITabelaResumoSimulacaoProps): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  const resumo: Resgate.IMontarResumoAliquotaFactoryRetorno[] =
    Resgate.tryGetValueOrDefault(
      [dadosResumo, resgateFeatureData.resumoAliquotaSelecionada?.resumo],
      [],
    );

  return (
    <Resgate.For each={resumo}>
      {item => (
        <Resgate.ContainerTableAliquota key={item.label} $disabled={$disabled}>
          <Resgate.Text variant="text-standard-400">{item.label}</Resgate.Text>
          <Resgate.Text variant="text-standard-400">
            {Resgate.getTernaryResult(
              !!item.mask,
              Resgate.tryGetMonetaryValueOrDefault([
                item.value?.replace(',', '.'),
              ]),
              item.value,
            )}
          </Resgate.Text>
        </Resgate.ContainerTableAliquota>
      )}
    </Resgate.For>
  );
};
