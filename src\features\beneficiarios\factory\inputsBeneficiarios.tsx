import {
  ComponentProps,
  DateField,
  FieldInputProps,
  InputText,
  S,
  Select,
  TIPOS_INPUT_CELULA,
  TIPOS_PARENTESCO,
  useBeneficiariosForm,
  validatePercentage,
} from '../exports';

type TInputBeneficiariosProps = {
  field: FieldInputProps<string>;
  rest: ComponentProps<'input'>;
  coberturaId: string;
};

type TInputsBeneficiarios = Record<string, React.FC<TInputBeneficiariosProps>>;

export const InputsBeneficiarios: TInputsBeneficiarios = {
  [TIPOS_INPUT_CELULA.TEXTO]: ({ field, rest }) => (
    <InputText
      arialabel="Aria Label"
      required={rest.required}
      placeholder={rest.placeholder ?? ''}
      type="text"
      variant="box-classic"
      {...field}
    />
  ),
  [TIPOS_INPUT_CELULA.DATA]: ({ field, rest }) => (
    <DateField required={rest.required} variant="box-classic" {...field} />
  ),
  [TIPOS_INPUT_CELULA.SELECT]: ({ field, rest }) => {
    const { setFieldValue } = useBeneficiariosForm();
    return (
      <>
        <Select
          placeholder={rest.placeholder}
          options={TIPOS_PARENTESCO}
          selectedValues={[field.value]}
          onChange={val => setFieldValue(field.name, val[0].value)}
          variant="box-classic"
          size="standard"
          sizeWidth="standard"
        />

        <input
          readOnly
          required={rest.required}
          style={{ display: 'none' }}
          name={field.name}
          value={field.value}
        />
      </>
    );
  },
  [TIPOS_INPUT_CELULA.PORCENTAGEM]: ({ field, rest, coberturaId }) => {
    const { somarPorcentagemCobertura } = useBeneficiariosForm();
    return (
      <S.ContainerInputPorcentagem>
        <InputText
          arialabel="Aria Label"
          required={rest.required}
          placeholder={rest.placeholder ?? ''}
          name={field.name}
          type="text"
          error={somarPorcentagemCobertura(coberturaId) > 100}
          value={field.value}
          onChange={e => {
            e.target.value = validatePercentage(e.target.value);
            field.onChange(e);
          }}
          variant="box-classic"
        />
        %
      </S.ContainerInputPorcentagem>
    );
  },
};
