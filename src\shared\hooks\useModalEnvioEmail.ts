import React, { useEffect, useState } from 'react';
import { checkIfSomeItemsAreTrue, tryGetValueOrDefault } from '@cvp/utils';

import { useObterDadosDoParticipante } from '@src/corporativo/infra/dadosParticipante/UseObterDadosDoParticipante';
import { useMensagensTemporarias } from '@src/shared/hooks/useMensagensTemporarias';
import { MODAL_ENVIO_EMAIL } from '@src/shared/constants/modalEnvioEmail';
import {
  IModalEnvioEmailProps,
  IUseModalEnvioEmail,
} from '@src/shared/types/IModalEnvioEmailProps';
import { useEnviarEmail } from '@src/shared/hooks/useEnviarEmail';

const useModalEnvioEmail = ({
  objetoEmail,
  onEmailSent,
  emailDefault,
}: IModalEnvioEmailProps): IUseModalEnvioEmail => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [email, setEmail] = useState<string | undefined>(emailDefault);
  const [canSend, setCanSend] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { mensagens, configurarMensagem } = useMensagensTemporarias();

  const { response, loading: isLoadingDadosParticipante } =
    useObterDadosDoParticipante();

  const { enviarEmail, loading: isLoadingEnvioEmail } =
    useEnviarEmail(objetoEmail);

  const isLoading: boolean = checkIfSomeItemsAreTrue([
    isLoadingDadosParticipante,
    loading,
    isLoadingEnvioEmail,
  ]);

  const isDisabled: boolean = checkIfSomeItemsAreTrue([!canSend, isLoading]);

  const setDefaultValues = (): void => {
    if (isOpen) {
      setEmail(tryGetValueOrDefault([response?.email], emailDefault));
      setCanSend(checkIfSomeItemsAreTrue([!!response?.email, !!emailDefault]));
    }
  };

  const toggleModal = (): void => {
    setIsOpen(prevState => !prevState);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const valor = e.target.value;
    setEmail(valor);
    setCanSend(!!valor);
  };

  const handleSend = async (): Promise<void> => {
    const emailValido = tryGetValueOrDefault([email], '');

    setLoading(true);
    const retornoEnvioEmail = await enviarEmail(emailValido);
    setLoading(false);

    if (retornoEnvioEmail?.sucesso) {
      configurarMensagem(
        MODAL_ENVIO_EMAIL.CHAVE_MSG_TEMPORARIA_SUCESSO,
        retornoEnvioEmail?.mensagem,
      );
      onEmailSent?.(emailValido);
    } else {
      configurarMensagem(
        MODAL_ENVIO_EMAIL.CHAVE_MSG_TEMPORARIA_ERRO,
        tryGetValueOrDefault(
          [retornoEnvioEmail?.mensagem],
          MODAL_ENVIO_EMAIL.MSG_TEMPORARIA_DEFAULT_ERRO,
        ),
      );
    }
  };

  useEffect(setDefaultValues, [isOpen]);

  return {
    toggleModal,
    handleChange,
    handleSend,
    isOpen,
    email,
    mensagens,
    isLoading,
    isDisabled,
  };
};

export default useModalEnvioEmail;
