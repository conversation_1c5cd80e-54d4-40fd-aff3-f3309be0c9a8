import {
  getSessionItem,
  IResponseHistoricoSolicitacoes,
  PrevidenciaContext,
  ROUTES,
  tipoEmailConstants,
  tryGetValueOrDefault,
  useContext,
  useNavigate,
  useObterComprovante,
  useState,
  IObterComprovanteResgateResponse,
  IHandleReponseResult,
} from '@src/features/consulta/historicoSolicitacoes/exports';

type TUseComprovanteSolicitacao = {
  loadingComprovantes: string[];
  error: string;
  handleObterComprovante: ({
    tipoRequisicao,
    idRequisicao,
  }: IResponseHistoricoSolicitacoes) => Promise<void>;
};

export const useComprovanteSolicitacao = (): TUseComprovanteSolicitacao => {
  const { invocarApiGatewayCvpComToken } = useObterComprovante();
  const { certificadoAtivo, setImpressao, setParametrosScroll } =
    useContext(PrevidenciaContext);
  const navigate = useNavigate();
  const [error, setError] = useState('');
  const [loadingComprovantes, setLoadingComprovantes] = useState<string[]>([]);

  const prepararComprovante = (
    resultado:
      | IHandleReponseResult<IObterComprovanteResgateResponse>
      | undefined,
  ) => {
    setImpressao({
      tipoDocumento: 'Comprovante',
      base64: resultado?.entidade?.comprovante
        ? [resultado?.entidade?.comprovante]
        : undefined,
      tipoEmail: tipoEmailConstants.COMPROVANTE_RESGATE,
      parametrosEnvio: {
        cpfCnpj: String(getSessionItem('cpfCnpj')),
        numeroCertificado: certificadoAtivo?.certificadoNumero,
      },
    });

    setParametrosScroll({
      rota: window.location.pathname,
      valorScroll: window.scrollY,
    });

    navigate(ROUTES.IMPRIMIR);
  };

  const handleRemoveLoadingComprovante = (idRequisicao: string) => {
    setLoadingComprovantes(prev => prev.filter(item => item !== idRequisicao));
  };

  const handleObterComprovante = async ({
    tipoRequisicao,
    idRequisicao,
  }: IResponseHistoricoSolicitacoes) => {
    setLoadingComprovantes(prev => [...prev, idRequisicao]);

    const resultado = await invocarApiGatewayCvpComToken({
      codigoRequisicao: tipoRequisicao,
      idRequisicao,
    }).finally(() => {
      handleRemoveLoadingComprovante(idRequisicao);
    });

    if (resultado?.sucessoBFF && resultado.sucessoGI) {
      prepararComprovante(resultado);
      return;
    }

    setError(tryGetValueOrDefault([resultado?.mensagens?.[0]?.descricao], ''));

    setTimeout(() => {
      setError('');
    }, 5000);
  };

  return {
    handleObterComprovante,
    error,
    loadingComprovantes,
  };
};
