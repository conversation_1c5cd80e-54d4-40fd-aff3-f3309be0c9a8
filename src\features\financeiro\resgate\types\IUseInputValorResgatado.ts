import {
  IListarFundosParaResgateFundosDisponiveis,
  IVariantsTypography,
} from '@src/features/financeiro/resgate/exports';

export interface IUseInputValorResgatado {
  fundo: IListarFundosParaResgateFundosDisponiveis;
  onChange: (codigoFundo: string, valorRetirar: string) => void;
}

export interface IUseInputValorResgatadoReturn {
  caminhoFundoResgateValor: string;
  handleChangeInput: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isError: boolean;
  message: string | null;
  colorByErrorState: IVariantsTypography['fontColor'];
}
