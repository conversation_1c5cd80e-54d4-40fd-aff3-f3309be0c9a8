import {
  getTernaryResult,
  IDadosCliente,
  IDadosPreenchidos,
  tryGetValueOrDefault,
} from '@src/features/financeiro/aporte/exports';

export const validarCamposAporte = (
  dadosPreenchidos: IDadosPreenchidos,
  dadosCliente: IDadosCliente,
): IDadosPreenchidos => {
  const primeiroNome = dadosCliente.nome.split(' ')[0].toLowerCase();
  const finalCpfCnpj = dadosCliente.cpfCnpj.slice(-4);

  const primeiroNomePreenchido = dadosPreenchidos.primeiroNome
    .toLowerCase()
    .trim();
  const finalCpfCnpjPreenchido = dadosPreenchidos.finalCpfCnpj;

  const comparacaoNome =
    primeiroNomePreenchido &&
    getTernaryResult(
      primeiroNomePreenchido !== primeiroNome,
      'Nome informado inválido!',
      undefined,
    );

  const comparacaoCpfCnpj =
    finalCpfCnpjPreenchido &&
    getTernaryResult(
      finalCpfCnpjPreenchido !== finalCpfCnpj,
      'CPF/CNPJ informado inválido!',
      undefined,
    );

  return {
    primeiroNome: tryGetValueOrDefault([comparacaoNome], ''),
    finalCpfCnpj: tryGetValueOrDefault([comparacaoCpfCnpj], ''),
  };
};
