import {
  useState,
  TSelectOption,
} from '@src/features/financeiro/transferencias/exports';

export type TUseInputCelulaBeneficiario = () => {
  value: string;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange: (option: Array<{ text: string; value: string }>) => void;
};

const useInputTipoTransferencia: TUseInputCelulaBeneficiario = () => {
  const [value, setValue] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  };

  const handleSelectChange = (options: Array<TSelectOption>) => {
    if (options && options.length > 0) {
      setValue(options[0].value);
    }
  };

  return {
    handleSelectChange,
    handleChange,
    value,
  };
};

export default useInputTipoTransferencia;
