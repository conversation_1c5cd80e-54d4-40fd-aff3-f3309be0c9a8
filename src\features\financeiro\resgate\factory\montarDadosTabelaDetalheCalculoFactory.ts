import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Montar os dados da tabela de detalhes de cálculo de resgate por alíquota
 *
 * Esta função processa os detalhes de cálculo da alíquota selecionada (progressiva ou regressiva)
 * e monta uma estrutura de dados formatada para exibição na tabela de detalhes, incluindo
 * tanto os detalhes por contribuição quanto o total consolidado.
 *
 * @param {Object} params - Parâmetros para montar os dados da tabela
 * @param {string} params.tipoAliquota - Tipo de alíquota (progressiva ou regressiva)
 * @param {Object} params.dadosSelecaoAliquota - Dados completos da seleção de alíquota
 * @returns {IMontarDadosTabelaDetalheCalculoFactoryRetorno[]} Array de dados formatados para a tabela de detalhes,
 * incluindo detalhes por contribuição e total consolidado
 */
export const montarDadosTabelaDetalheCalculoFactory = ({
  tipoAliquota,
  dadosSelecaoAliquota,
}: Resgate.IMontarDadosTabelaDetalheCalculoFactory): Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno[] => {
  const isAliquotaProgressiva: boolean =
    tipoAliquota === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO;

  const {
    detalhado,
    calculo,
  }: Resgate.IConsultarDetalhesDaAliquotaFactoryRetorno =
    Resgate.mapearDadosPorAliquotaFactory(
      isAliquotaProgressiva,
      dadosSelecaoAliquota,
    );

  const detalhesPorContribuicao: Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno[] =
    Resgate.tryGetValueOrDefault([detalhado?.detalhes], []).map(
      Resgate.formatarDadosTabelaDetalheCalculo,
    );

  const totalContribuicaoAgrupado: Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno =
    Resgate.formatarDadosTabelaDetalheCalculo({
      data: Resgate.TIPOS_RESGATE.TOTAL.value,
      rendimento: detalhado?.totalRendimento,
      carregamentoSaida: detalhado?.totalCarregamentoSaida,
      baseIrrf: detalhado?.totalBaseIrrf,
      valorIrrf: detalhado?.totalValorIrrf,
      taxaSaida: detalhado?.totalTaxaSaida,
      aliquotaIrrf: detalhado?.totalAliquotaIrrf,
      numeroResgate: calculo?.numeroResgate,
      saldoAporte: detalhado?.totalValorSolicitado,
      saldoPrincipal: detalhado?.totalSaldoPrincipal,
      rendimentos: detalhado?.totalRendimentosAporte,
      valorCorrecao: detalhado?.totalValorCorrecao,
      valorLiquido: detalhado?.totalValorLiquido,
      valorPrincipal: detalhado?.totalValorPrincipalAporte,
      valorSolicitado: detalhado?.totalValorSolicitado,
    });

  return [...detalhesPorContribuicao, totalContribuicaoAgrupado];
};
