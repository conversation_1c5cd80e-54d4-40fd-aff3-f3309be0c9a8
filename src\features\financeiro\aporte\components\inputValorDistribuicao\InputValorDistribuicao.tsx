import {
  IFundosAporte,
  InputText,
  useAporteContext,
  Text,
  FUNDOS,
  IValorContribuicao,
  findFundoId,
  valoresMonetarios,
  tryGetValueOrDefault,
  formatarValorPadraoBrasileiro,
  Match,
  FormikProps,
} from '@src/features/financeiro/aporte/exports';

interface IInputValorDistribuicao {
  fundo: IFundosAporte;
  formik: FormikProps<IValorContribuicao>;
  onChange: (value: string) => void;
}

const InputValorDistribuicao: React.FC<IInputValorDistribuicao> = ({
  fundo,
  formik,
  onChange,
}) => {
  const { fundoSelecionado } = useAporteContext();
  const isSelected = findFundoId({
    fundoSelecionado,
    id: fundo.fundoId,
  });

  if (!formik.values.valoresMinimos[fundo.fundoId]) {
    formik.setFieldValue(`valoresMinimos.${fundo.fundoId}`, fundo.valorMinimo);
  }

  return (
    <Match when={isSelected}>
      <InputText
        type="text"
        name={`valorDistribuido.${fundo.fundoId}`}
        onChange={event => {
          formik.setFieldValue(
            `valorDistribuido.${fundo.fundoId}`,
            parseFloat(valoresMonetarios.unmask(event.target.value)) / 100,
          );

          onChange(
            String(
              parseFloat(valoresMonetarios.unmask(event.target.value)) / 100,
            ),
          );
        }}
        value={valoresMonetarios.mask(
          tryGetValueOrDefault(
            [formik.values.valorDistribuido[fundo.fundoId]],
            '',
          ),
        )}
        arialabel={FUNDOS.distribuicao}
        placeholder={FUNDOS.distribuicaoPlaceholder}
        variant="box-classic"
        size="small"
        error={
          formik.values.valorDistribuido[fundo.fundoId] < fundo.valorMinimo
        }
        required
      />
      <Text variant="caption-standard-400" fontColor="content-neutral-03">
        Valor Mínimo {formatarValorPadraoBrasileiro(fundo.valorMinimo)}
      </Text>
    </Match>
  );
};

export default InputValorDistribuicao;
