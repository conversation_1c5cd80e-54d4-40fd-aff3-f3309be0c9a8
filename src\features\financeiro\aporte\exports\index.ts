export { createContext, useContext, useEffect, useMemo, useState } from 'react';
export type { ReactElement } from 'react';

export { default as React } from 'react';

export { styled } from 'styled-components';

export { Formik, useFormik } from 'formik';

export { useNavigate } from 'react-router-dom';

export * as Yup from 'yup';

export {
  capitalize,
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  converterBase64,
  formatarDataHoraAmigavel,
  formatarValorPadraoBrasileiro,
  getSessionItem,
  getTernaryResult,
  porcentagem,
  setSessionItem,
  tryGetValueOrDefault,
  valoresMonetarios,
} from '@cvp/utils';

export { Match, SwitchCase } from '@cvp/componentes-posvenda';

export type { IHandleReponseResult } from '@cvp/componentes-posvenda';

export {
  Alert,
  Button,
  Checkbox,
  Dialog,
  DialogInfo,
  Grid,
  GridItem,
  IconInfoRound,
  InputText,
  LoadingSpinner,
  RadioGroup,
  RadioItem,
  RadioLabel,
  ScrollArea,
  Select,
  Table,
  Text,
  TextArea,
  ConditionalRenderer,
} from '@cvp/design-system-caixa';

export type { FormikProps } from 'formik';

export {
  ButtonContainer,
  Container,
  customStyles,
  GridColumnContainer,
  GridItemContainer,
} from '@src/features/financeiro/aporte/styles/styles';
export * as S from '@src/corporativo/components/PerfilDoRisco/PerfilDoRisco.style';

export type { IContentModalConfirmarAporteFactory } from '@src/features/financeiro/aporte/types/IContentModalConfirmarAporteFactory';
export { contentModalConfirmarAporteFactory } from '@src/features/financeiro/aporte/factory/contentModalConfirmarAporteFactory';
export { default as ContentConfirmarAporte } from '@src/features/financeiro/aporte/components/contentConfirmarAporte/ContentConfirmarAporte';

export { ASSINATURA_SESSION_KEY } from '@src/corporativo/constants/assinatura/assinaturaSession';
export { default as Assinatura } from '@src/corporativo/components/Assinatura/Assinatura';
export { default as PerfilDoRisco } from '@src/corporativo/components/PerfilDoRisco/PerfilDoRisco';
export { default as AporteContext } from '@src/corporativo/context/financeiro/aporte/AporteContext';
export { default as AporteProvider } from '@src/corporativo/context/financeiro/aporte/AporteProvider';
export { default as AporteServiceContext } from '@src/corporativo/context/financeiro/aporte/AporteServiceContext';
export { default as AporteServiceProvider } from '@src/corporativo/context/financeiro/aporte/AporteServiceProvider';
export { default as ComprovanteAporteFundos } from '@src/features/financeiro/aporte/components/comprovanteAporteFundos/ComprovanteAporteFundos';
export { default as ContaBancariaSelecionadaAporte } from '@src/features/financeiro/aporte/components/dadosComprovanteAporte/contaBancariaSelecionadaAporte/ContaBancariaSelecionadaAporte';
export { default as ComprovanteAporteDadosAporte } from '@src/features/financeiro/aporte/components/dadosComprovanteAporte/DadosComprovanteAporte';
export { default as FiltroSelecionadoTabelaFundos } from '@src/features/financeiro/aporte/components/dadosComprovanteAporte/filtroSelecionadoTabelaFundos/FiltroSelecionadoTabelaFundos';
export { default as DadosModalConfirmarAporte } from '@src/features/financeiro/aporte/components/dadosModalConfirmarAporte/DadosModalConfirmarAporte';
export { default as DataDebito } from '@src/features/financeiro/aporte/components/dataDebito/DataDebito';
export { default as FormaDePagamento } from '@src/features/financeiro/aporte/components/formaDePagamento/FormaDePagamento';
export { default as InputValorDistribuicao } from '@src/features/financeiro/aporte/components/inputValorDistribuicao/InputValorDistribuicao';
export { default as ModalConfirmarAporte } from '@src/features/financeiro/aporte/components/modalConfirmarAporte/ModalConfirmarAporte';
export { default as ModalNaoInformarRecursoAporte } from '@src/features/financeiro/aporte/components/modalNaoInformarRecursoAporte/ModalNaoInformarRecursoAporte';
export { default as NovaContaDebitoAporte } from '@src/features/financeiro/aporte/components/novaContaDebitoAporte/NovaContaDebitoAporte';
export { default as OpcoesOrigemRecursos } from '@src/features/financeiro/aporte/components/opcoesOrigemRecursos/OpcoesOrigemRecursos';
export { default as OrigemRecursosAporte } from '@src/features/financeiro/aporte/components/origemRecursosAporte/OrigemRecursosAporte';
export { default as PerfilInvestidor } from '@src/features/financeiro/aporte/components/perfilInvestidor/PerfilInvestidor';
export { default as SelectContasBancarias } from '@src/features/financeiro/aporte/components/selectContasBancarias/SelectContasBancarias';
export { default as SomaEValorRestanteContribuicao } from '@src/features/financeiro/aporte/components/somaEValorRestanteContribuicao/SomaEValorRestanteContribuicao';
export { default as TabelaFundosAporte } from '@src/features/financeiro/aporte/components/tabelaFundosAporte/TabelaFundosAporte';
export { default as TabelaFundosDistribuicaoExistentes } from '@src/features/financeiro/aporte/components/tabelasFundosDistribuicao/TabelaFundosDistribuicaoExistentes';
export { default as TabelaFundosDistribuicaoNovos } from '@src/features/financeiro/aporte/components/tabelasFundosDistribuicao/TabelaFundosDistribuicaoNovos';
export { default as ValidacaoAporte } from '@src/features/financeiro/aporte/components/validacaoAporte/ValidacaoAporte';
export { default as ValorContribuicao } from '@src/features/financeiro/aporte/components/valorContribuicao/ValorContribuicao';
export { default as AssinaturaAporte } from '@src/features/financeiro/aporte/views/AssinaturaAporte';
export { default as ComprovanteAporte } from '@src/features/financeiro/aporte/views/ComprovanteAporte';
export { default as ContaDebitoAporte } from '@src/features/financeiro/aporte/views/ContaDebitoAporte';
export { default as FiltrosTabelaAporte } from '@src/features/financeiro/aporte/views/FiltrosTabelaAporte';
export { default as FluxoTelasAporte } from '@src/features/financeiro/aporte/views/FluxoTelasAporte';
export { default as FormularioNovaContaBancariaAporte } from '@src/features/financeiro/aporte/views/FormularioNovaContaBancariaAporte';
export { default as FundosDistribuicaoAporte } from '@src/features/financeiro/aporte/views/FundosDistribuicaoAporte';
export { default as ModalVerificarCamposAporte } from '@src/features/financeiro/aporte/views/ModalVerificarCamposAporte';

export { PerfilDoRiscoTriangulo } from '@src/corporativo/components/PerfilDoRisco/PerfilDoRiscoTriangulo';

export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
export { useAporteContext } from '@src/corporativo/hooks/useAporteContext';
export { useAporteServiceContext } from '@src/corporativo/hooks/useAporteServiceContext';
export { useRegistrarTokenAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useRegistrarTokenAssinaturaCaixa';
export { useComprovanteAporte } from '@src/features/financeiro/aporte/hooks/comprovanteAporte/useComprovanteAporte';
export { useDadosComprovanteAporte } from '@src/features/financeiro/aporte/hooks/comprovanteAporte/useDadosComprovanteAporte';
export { useContaDebitoAporte } from '@src/features/financeiro/aporte/hooks/contaDebitoAporte/useContaDebitoAporte';
export { useDataDebito } from '@src/features/financeiro/aporte/hooks/filtrosTabelaAporte/useDataDebito';
export { useFiltrosTabelaAporte } from '@src/features/financeiro/aporte/hooks/filtrosTabelaAporte/useFiltrosTabelaAporte';
export { usePerfilInvestidor } from '@src/features/financeiro/aporte/hooks/filtrosTabelaAporte/usePerfilInvestidor';
export { useValorContribuicao } from '@src/features/financeiro/aporte/hooks/filtrosTabelaAporte/useValorContribuicao';
export { default as useDesabilitarFundo } from '@src/features/financeiro/aporte/hooks/fundosDistribuicaoAporte/useDesabilitarFundo';
export { useFundosDistribuicao } from '@src/features/financeiro/aporte/hooks/fundosDistribuicaoAporte/useFundosDistribuicao';
export { default as useRenderTabelaFundos } from '@src/features/financeiro/aporte/hooks/fundosDistribuicaoAporte/useRenderTabelaFundos';
export { useModalConfirmarAporte } from '@src/features/financeiro/aporte/hooks/modalConfirmarAporte/useModalConfirmarAporte';
export { useModalVerificarCamposAporte } from '@src/features/financeiro/aporte/hooks/modalVerificarCamposAporte/useModalVerificarCamposAporte';
export { useValidarAporteFormik } from '@src/features/financeiro/aporte/hooks/modalVerificarCamposAporte/useValidarAporteFormik';
export { useNovaContaAporte } from '@src/features/financeiro/aporte/hooks/novaContaDebitoAporte/useNovaContaAporte';
export { useOrigemRecursosAporte } from '@src/features/financeiro/aporte/hooks/origemRecursosAporte/useOrigemRecursosAporte';
export { useFluxoTelasAporte } from '@src/features/financeiro/aporte/hooks/useFluxoTelasAporte';
export { default as useValidarAssinatura } from '@src/shared/hooks/useValidarAssinatura';

export { useConsultarDadosPep } from '@src/corporativo/infra/financeiro/aporte/useConsultarDadosPep';
export { useEfetuarAporte } from '@src/corporativo/infra/financeiro/aporte/useEfetuarAporte';
export { useObterDadosBancarios } from '@src/corporativo/infra/financeiro/aporte/useObterDadosBancarios';
export { useObterDatas } from '@src/corporativo/infra/financeiro/aporte/useObterDatas';
export { useValidarCampoAporte } from '@src/corporativo/infra/financeiro/aporte/useValidarCampoAporte';
export { useObterComprovante } from '@src/shared/infra/useObterComprovante';
export { useObterFundosDistribuicao } from '@src/shared/infra/financeiro/useObterFundosDistribuicao';
export { useValidarConta } from '@src/shared/infra/useValidarConta';

export { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';

export { AlertaModal } from '@src/corporativo/components/AlertaModal';

export * from '@src/features/financeiro/aporte/constants/constants';

export { ThemeProvider } from '@cvp/design-system-caixa';

export { default as useCertificadosPrevidencia } from '@src/corporativo/infra/consultaCertificado/useCertificadosPrevidencia';

export { LISTA_DE_PERFIS } from '@src/corporativo/constants/PerfilDoRisco';

export { EEtapasAporte } from '@src/corporativo/context/financeiro/aporte/AporteProvider';

export type { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
export type { TColumn } from '@cvp/design-system-caixa/dist/atoms/Table/Table.types';
export type { TAporteContext } from '@src/corporativo/types/aporte/TAporteContext';
export type { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
export type { ICertificadoPrevidenciaResponse } from '@src/shared/types/ICertificadoPrevidenciaResponse';
export type { IItensExtrato } from '@src/corporativo/types/consultaCertificado/Response/IItensExtrato';
export type {
  IHandleChange,
  TSetFieldValue,
} from '@src/corporativo/types/financeiro/aporte/FormikNovaContaBancariaAporte';
export type { ICadastroNovaConta } from '@src/corporativo/types/financeiro/aporte/ICadastrarNovaConta';
export type {
  IDadosCliente,
  IDadosPreenchidos,
} from '@src/corporativo/types/financeiro/aporte/IDadosClienteAporte';
export type { IDadosNovaContaBancariaAporte } from '@src/corporativo/types/financeiro/aporte/IDadosNovaContaBancariaAporte';
export type {
  IDadosTabelaFundos,
  IFundosAporte,
} from '@src/corporativo/types/financeiro/aporte/IDadosTabelaFundos';
export type { IEfetuarAportePayload } from '@src/corporativo/types/financeiro/aporte/IEfetuarAportePayload';
export type { IFiltroTabelaAporte } from '@src/corporativo/types/financeiro/aporte/IFiltroTabelaFundos';
export type { IObterDadosBancariosPayload } from '@src/corporativo/types/financeiro/aporte/IObterDadosBancariosPayload';
export type { IObterDadosPepPayload } from '@src/corporativo/types/financeiro/aporte/IObterDadosPepPayload';
export type { IObterDatasPayload } from '@src/corporativo/types/financeiro/aporte/IObterDatasPayload';
export type { IPagamento } from '@src/corporativo/types/financeiro/aporte/IPagamento';
export type { IShowInputs } from '@src/corporativo/types/financeiro/aporte/IShowInputs';
export type { ITextoDadosBancarios } from '@src/corporativo/types/financeiro/aporte/ITextoDadosBancarios';
export type { IValidarCampoAportePayload } from '@src/corporativo/types/financeiro/aporte/IValidarCampoAportePayload';
export type { IDadosFundosAporte } from '@src/corporativo/types/financeiro/aporte/Response/IDadosFundosAporte';
export type { IDadosFundosExistentesAporte } from '@src/corporativo/types/financeiro/aporte/Response/IDadosFundosExistentesAporte';
export type { IDadosFundosNovosAporte } from '@src/corporativo/types/financeiro/aporte/Response/IDadosFundosNovosAporte';
export type { IEfetuarAporteResponse } from '@src/corporativo/types/financeiro/aporte/Response/IEfetuarAporteResponse';
export type {
  IDadosBancariosAporte,
  IObterDadosBancariosResponse,
} from '@src/corporativo/types/financeiro/aporte/Response/IObterDadosBancariosResponse';
export type { IObterDadosPepResponse } from '@src/corporativo/types/financeiro/aporte/Response/IObterDadosPepResponse';
export type { IObterFundosResponse } from '@src/corporativo/types/financeiro/aporte/Response/IObterFundosResponse';
export type { IValidarCampoAporteResponse } from '@src/corporativo/types/financeiro/aporte/Response/IValidarCampoAporteResponse';
export type { TAporteServiceContext } from '@src/corporativo/types/financeiro/aporte/TAporteServiceContext';
export type { TUseConsultarDadosPep } from '@src/corporativo/types/financeiro/aporte/TUseConsultarDadosPep';
export type { TUseEfetuarAporte } from '@src/corporativo/types/financeiro/aporte/TUseEfetuarAporte';
export type { TUseObterDadosBancarios } from '@src/corporativo/types/financeiro/aporte/TUseObterDadosBancarios';
export type { TUseObterDatas } from '@src/corporativo/types/financeiro/aporte/TUseObterDatas';
export type { TUseObterFundosDistribuicao } from '@src/corporativo/types/financeiro/aporte/TUseObterFundosDistribuicao';
export type { TUseRenderTabelaFundos } from '@src/corporativo/types/financeiro/aporte/TUseRenderTabelaFundos';
export type { TUseValidarCampoAporte } from '@src/corporativo/types/financeiro/aporte/TUseValidarCampoAporte';
export type { IValorContribuicao } from '@src/features/financeiro/aporte/hooks/fundosDistribuicaoAporte/useRenderTabelaFundos';
export type { TTiposDePerfis } from '@src/shared/types/TTiposDePerfis';
export type { TUseObterExtratoUnificado } from '@src/shared/types/ObterExtratoUnificado/TUseObterExtratoUnificado';
export type {
  FormikErrors as TFormikErrors,
  FormikProps as TFormikProps,
} from 'formik';

export {
  dadosBancariosAdicionadosFactory,
  dadosBancariosResponseFactory,
  dadosBancariosTextFactory,
} from '@src/features/financeiro/aporte/factory/dadosBancariosFactory';

export { dadosTabelaFundosAporteFactory } from '@src/features/financeiro/aporte/factory/dadosTabelaFundosAporteFactory';
export { efetuarAporteRequestFactory } from '@src/features/financeiro/aporte/factory/efetuarAporteRequestFactory';
export { validarCamposAporte } from '@src/features/financeiro/aporte/factory/validarCamposAporte';
export { validarInputValorDistruibuicao } from '@src/features/financeiro/aporte/factory/validarInputValorDistruibuicao';
export {
  findFundoId,
  obterNumeroContaSemOperacao,
  obterNumeroOperacao,
  obterOperacao,
  ultimoDiaDoMes,
  validacaoCadastrarNovaContaBancaria,
  validationSchemaFiltrosTabelaAporte,
} from '@src/features/financeiro/aporte/utils/utils';

export { tipoEmailConstants } from '@src/corporativo/infra/email/tipoEmail';

export { PECOS } from '@src/corporativo/infra/config/api/endpoints';

export { columnsTabelaFundosDistribuicaoFactory } from '@src/features/financeiro/aporte/factory/columnsTabelaFundosDistribuicaoFactory';

export type {
  IValidarContaPayload,
  IValidarContaResponse,
  IUseValidarContaRetorno,
} from '@src/shared/types/IUseValidarConta';

export type { IUseObterComprovanteResgate } from '@src/shared/types/ObterComprovanteResgate/IObterComprovanteResgate';

export { FORMA_PAGAMENTO_MODAL } from '@src/features/financeiro/aporte/constants/constants';
export { OPERACOES_PREVIDENCIA } from '@src/corporativo/constants/OperacoesPrevidencia';
export { useConfirmarOperacaoAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useConfirmarOperacaoAssinaturaCaixa';
