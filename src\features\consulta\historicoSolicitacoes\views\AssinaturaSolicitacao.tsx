import {
  Assinatura,
  getSessionItem,
  IAssinaturaResponse,
  PrevidenciaContext,
  ConditionalRenderer,
  useContext,
} from '@src/features/consulta/historicoSolicitacoes/exports';

type TAssinaturaSolicitacaoProps = {
  open: boolean;
  assinaturaSucessoCallback: VoidFunction;
};

export const AssinaturaSolicitacao: React.FC<TAssinaturaSolicitacaoProps> = ({
  open,
  assinaturaSucessoCallback,
}) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const callbackAssinatura = (eventCallback: IAssinaturaResponse): void => {
    if (eventCallback.resposta.success) {
      assinaturaSucessoCallback();
    }
  };

  return (
    <ConditionalRenderer condition={!!open}>
      <Assinatura
        dados={{
          cpfCnpj: getSessionItem('cpfCnpj') ?? '',
          numeroCertificado: String(certificadoAtivo?.numeroCertificado),
        }}
        callback={callbackAssinatura}
      />
    </ConditionalRenderer>
  );
};
