import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const TransferenciaEntreFundosFooter: React.FC = () => {
  const { transferenciaRealizada, etapa } =
    TransferenciaEntreFundos.useTransferenciaContext();

  const { carregando } =
    TransferenciaEntreFundos.useTransferenciaServicosContext();

  return (
    <TransferenciaEntreFundos.SwitchCase fallback={undefined}>
      <TransferenciaEntreFundos.Match when={transferenciaRealizada}>
        <TransferenciaEntreFundos.SucessoAcoes />
      </TransferenciaEntreFundos.Match>
      <TransferenciaEntreFundos.Match when={carregando}>
        <TransferenciaEntreFundos.Grid margin="0.5rem" justify="center">
          <TransferenciaEntreFundos.Carregando>
            <TransferenciaEntreFundos.Text variant="text-standard-400">
              <strong style={{ color: '#005CA9' }}>{carregando}</strong>
            </TransferenciaEntreFundos.Text>
          </TransferenciaEntreFundos.Carregando>
        </TransferenciaEntreFundos.Grid>
      </TransferenciaEntreFundos.Match>
      <TransferenciaEntreFundos.Match
        when={
          etapa ===
          TransferenciaEntreFundos.EEtapasTranferencia.DefinirFundosOrigem
        }
      >
        <TransferenciaEntreFundos.OrigemAcoes />
      </TransferenciaEntreFundos.Match>
      <TransferenciaEntreFundos.Match
        when={
          etapa ===
          TransferenciaEntreFundos.EEtapasTranferencia.DefinirFundosDestino
        }
      >
        <TransferenciaEntreFundos.FundoDestinoAcoes />
      </TransferenciaEntreFundos.Match>
      <TransferenciaEntreFundos.Match
        when={
          etapa === TransferenciaEntreFundos.EEtapasTranferencia.ValidarOperacao
        }
      >
        <TransferenciaEntreFundos.ValidacaoAcoes />
      </TransferenciaEntreFundos.Match>
    </TransferenciaEntreFundos.SwitchCase>
  );
};
