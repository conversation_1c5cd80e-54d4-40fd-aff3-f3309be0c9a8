import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IRecuperarBancosPayload,
  IRecuperarBancosResponse,
  IUseRecuperarBancosRetorno,
} from '@src/corporativo/types/financeiro/resgate/IRecuperarBancos';

export const useRecuperarBancos = (): IUseRecuperarBancosRetorno => {
  const {
    response: dadosBancarios,
    loading: isLoadingDadosBancarios,
    invocarApiGatewayCvpComToken: recuperarBancos,
  } = useApiGatewayCvpInvoker<
    IRecuperarBancosPayload,
    IRecuperarBancosResponse[]
  >(PECOS.RecuperarBancos, {
    data: { cpfCnpj: getSessionItem('cpfCnpj') ?? '' },
  });

  return {
    dadosBancarios: tryGetValueOrDefault([dadosBancarios?.entidade], []),
    isLoadingDadosBancarios,
    recuperarBancos,
  };
};
