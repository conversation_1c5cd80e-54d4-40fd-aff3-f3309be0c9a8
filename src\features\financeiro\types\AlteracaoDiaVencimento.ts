type TPagamentoRegular = {
  codTipoPagamento: string;
  codCanalRecuperacao: string;
  diaPagamento: string;
  metodoPagamento: string;
};

export interface IResponseAlteracaoDiaVencimento {
  idManutencaoCertificado: string | null;
  status: string;
  pagamentoRegular: TPagamentoRegular | undefined;
}

export interface IRequestAtualizarDiaVencimento {
  numeroCertificado: string;
  cpfCnpj: string;
  dataVencimento: string;
}
