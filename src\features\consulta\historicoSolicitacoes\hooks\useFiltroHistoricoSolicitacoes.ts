import React, { useState } from 'react';

import * as HistoricoSolicitacoes from '@src/features/consulta/historicoSolicitacoes/exports';

export const useFiltroHistoricoSolicitacao =
  (): HistoricoSolicitacoes.TUseFiltroHistoricoSolicitacoes => {
    const [filtros, setFiltros] = useState<HistoricoSolicitacoes.TFiltros>(
      {} as HistoricoSolicitacoes.TFiltros,
    );
    const [erroPeriodo, setErroPeriodo] =
      HistoricoSolicitacoes.useMensagemTemporaria();
    const [periodoPersonalizado, setPeriodoPersonalizado] = useState(
      HistoricoSolicitacoes.FILTRO_PERIODO_PERSONALIZADO,
    );

    const selecionarFiltro = (
      chave: keyof HistoricoSolicitacoes.TFiltros,
      item: HistoricoSolicitacoes.TSelectItem,
    ) => {
      setFiltros(prev => ({
        ...prev,
        [chave]: item[0],
        ...(chave === HistoricoSolicitacoes.TIPO_SOLICITACAO && {
          filtroAtivo: {
            ...HistoricoSolicitacoes.tryGetValueOrDefault(
              [prev.filtroAtivo],
              {} as HistoricoSolicitacoes.TConsultaFiltros,
            ),
            tipoSolicitadoValor: item[0].value,
          },
        }),
      }));
    };

    const selecionarPeriodoPersonalizado = (
      e: React.ChangeEvent<HTMLInputElement>,
    ) => {
      setPeriodoPersonalizado(prev => ({
        ...prev,
        [e.target.name]: e.target.value,
      }));
    };

    const validarErroDatas = (
      novosFiltros: HistoricoSolicitacoes.TConsultaFiltros,
    ) => {
      const erroDatas = HistoricoSolicitacoes.verificaErroPeriodoData(
        novosFiltros?.dataInicial,
        novosFiltros?.dataFinal,
      );

      if (
        erroDatas &&
        filtros?.periodoValor?.value ===
          HistoricoSolicitacoes.VALORES_PERIODOS.PERIODO_PERSONALIZADO
      ) {
        setErroPeriodo(HistoricoSolicitacoes.TEXTOS.ERROS.DATA_INVALIDA);

        return true;
      }

      return false;
    };

    const onClickConsultar = (
      onSubmitCallback: (
        periodo: HistoricoSolicitacoes.TConsultaFiltros,
      ) => void,
    ) => {
      const novosFiltros = {
        ...filtros,
        filtroAtivo: {
          ...HistoricoSolicitacoes.definirPeriodo(
            filtros?.periodoValor?.value,
            periodoPersonalizado,
          ),
          tipoSolicitadoValor: filtros?.tipoSolicitacao?.value,
        },
      };

      const erroDatas = validarErroDatas(novosFiltros.filtroAtivo);

      if (!erroDatas) {
        setFiltros(novosFiltros);
        onSubmitCallback(novosFiltros.filtroAtivo);
      }
    };

    return {
      filtros,
      selecionarFiltro,
      periodoPersonalizado,
      selecionarPeriodoPersonalizado,
      onClickConsultar,
      erroPeriodo,
    };
  };
