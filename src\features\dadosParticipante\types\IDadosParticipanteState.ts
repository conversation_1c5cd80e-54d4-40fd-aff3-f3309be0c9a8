import { IControlesLGPD, IDadosParticipanteEntidade } from '../exports';

export interface IDadosParticipanteFormEntidade
  extends IDadosParticipanteEntidade {
  telefone: string;
  celular: string;
}

export interface IDadosParticipanteState {
  editando: boolean;
  salvando: boolean;
  buscandoCep: boolean;
  assinaturaValida: boolean;
  dadosParticipante: IDadosParticipanteFormEntidade;
  controlesLGPD: IControlesLGPD;
  consetimentoOriginal: IControlesLGPD;
  idCidade: string;
}
