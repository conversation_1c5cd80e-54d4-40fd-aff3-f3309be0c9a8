import * as AtivacaoSuspensao from '../../exports';

interface IDadosCertificadoProps {
  dadosCertificado:
    | AtivacaoSuspensao.IRecuperarContribuicoesCertificadoResponse
    | null
    | undefined;
}

export const DadosCertificado: React.FC<IDadosCertificadoProps> = ({
  dadosCertificado,
}) => {
  if (!dadosCertificado) return null;

  return (
    <AtivacaoSuspensao.Grid>
      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Text
          variant="text-large-700"
          fontColor="content-neutral-05"
          marginBottom="16px"
        >
          Dados do certificado
        </AtivacaoSuspensao.Text>

        <AtivacaoSuspensao.Grid>
          <AtivacaoSuspensao.GridItem xs="1/2">
            <AtivacaoSuspensao.Text
              variant="text-standard-400"
              fontColor="content-neutral-04"
            >
              CPF:
            </AtivacaoSuspensao.Text>
            <AtivacaoSuspensao.Text
              variant="text-standard-700"
              fontColor="content-neutral-05"
            >
              {dadosCertificado.cpfPessoaCertificado}
            </AtivacaoSuspensao.Text>
          </AtivacaoSuspensao.GridItem>

          <AtivacaoSuspensao.GridItem xs="1/2">
            <AtivacaoSuspensao.Text
              variant="text-standard-400"
              fontColor="content-neutral-04"
            >
              Produto:
            </AtivacaoSuspensao.Text>
            <AtivacaoSuspensao.Text
              variant="text-standard-700"
              fontColor="content-neutral-05"
            >
              {dadosCertificado.descricaoProduto}
            </AtivacaoSuspensao.Text>
          </AtivacaoSuspensao.GridItem>

          <AtivacaoSuspensao.GridItem xs="1">
            <AtivacaoSuspensao.Text
              variant="text-standard-400"
              fontColor="content-neutral-04"
            >
              Certificado:
            </AtivacaoSuspensao.Text>
            <AtivacaoSuspensao.Text
              variant="text-standard-700"
              fontColor="content-neutral-05"
            >
              {dadosCertificado.contaId}
            </AtivacaoSuspensao.Text>
          </AtivacaoSuspensao.GridItem>
        </AtivacaoSuspensao.Grid>
      </AtivacaoSuspensao.GridItem>
    </AtivacaoSuspensao.Grid>
  );
};
