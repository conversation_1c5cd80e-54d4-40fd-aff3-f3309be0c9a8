import { useLocation, useNavigate } from 'react-router-dom';
import { useContext } from 'react';
import { MatrizAcessoContext } from '../exports';
import { TUseTabsFromRouterReturn } from '../types/tabsTypes';

const useTabsFromRouter = (): TUseTabsFromRouterReturn => {
  const { certificadoTabs } = useContext(MatrizAcessoContext);
  const navigate = useNavigate();
  const location = useLocation();

  const activeTab =
    certificadoTabs.find(tab => location.pathname.includes(tab.value))?.value ||
    certificadoTabs[0].value;

  const handleTabChange = (newValue: string) => {
    const selectedTab = certificadoTabs.find(tab => tab.value === newValue);
    if (selectedTab) navigate(selectedTab.value);
  };

  return {
    activeTab,
    handleTabChange,
  };
};

export default useTabsFromRouter;
