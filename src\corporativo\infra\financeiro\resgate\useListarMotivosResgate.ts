import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IListarMotivosResgateResponse,
  IUseListarMotivosResgateReturn,
} from '@src/corporativo/types/financeiro/resgate/IListarMotivosResgate';

export const useListarMotivosResgate = (): IUseListarMotivosResgateReturn => {
  const {
    response: listaMotivosResgate,
    loading: isLoadingListaMotivosResgate,
    invocarApiGatewayCvpComToken: listarMotivosResgate,
  } = useApiGatewayCvpInvoker<unknown, IListarMotivosResgateResponse[]>(
    PECOS.ListarMotivosResgate,
    {},
  );

  return {
    listaMotivosResgate: tryGetValueOrDefault(
      [listaMotivosResgate?.entidade],
      [] as IListarMotivosResgateResponse[],
    ),
    isLoadingListaMotivosResgate,
    listarMotivosResgate,
  };
};
