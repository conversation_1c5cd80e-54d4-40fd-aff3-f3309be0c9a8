import * as Origem from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export type TUseProsseguirOrigem = {
  finalizarEtapa: () => Promise<void>;
  podeProsseguir: boolean;
};

export const useProsseguirOrigem = (): TUseProsseguirOrigem => {
  const { certificadoAtivo } = Origem.useContext(Origem.PrevidenciaContext);
  const { reservasOrigem, setErro, setEtapa, distribuicaoValores } =
    Origem.useTransferenciaContext();
  const { consultaOrigem, consultaDestino } =
    Origem.useTransferenciaServicosContext();

  const podeProsseguir = reservasOrigem.some(reserva =>
    Origem.checkIfAllItemsAreTrue([
      !!reserva.staTipRetirada,
      !!reserva.valorSolicitado,
    ]),
  );

  const verificaErroValorMin = () => {
    if (!consultaOrigem.vlrMinTransferencia) return false;

    return (
      distribuicaoValores.somaIntencaoDistribuicaoOrigens <
        consultaOrigem.vlrMinTransferencia &&
      !consultaDestino.response?.reservasDestino?.length &&
      !consultaDestino.loading
    );
  };

  const finalizarEtapa = async () => {
    const erroValorMin = verificaErroValorMin();

    if (erroValorMin) {
      setErro(Origem.EErroTransferencia.ValorMin);
      return;
    }

    try {
      await consultaDestino.fetchData(
        Origem.tryGetValueOrDefault([certificadoAtivo?.certificadoNumero], ''),
        reservasOrigem,
      );
      setEtapa(Origem.EEtapasTranferencia.DefinirFundosDestino);
      setErro();
    } catch (err) {
      setEtapa(Origem.EEtapasTranferencia.DefinirFundosOrigem);
      setErro(Origem.EErroTransferencia.SemFundosDestino);
      if (!certificadoAtivo?.certificadoNumero) {
        setErro(Origem.EErroTransferencia.CodeDestino);
      }
    }
  };

  return {
    finalizarEtapa,
    podeProsseguir,
  };
};
