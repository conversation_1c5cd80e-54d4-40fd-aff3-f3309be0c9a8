import {
  IUseVerUltimasSimulacoesDeRendaReturn,
  IUseVerUltimasSolicitacoesResponse,
  useState,
  useVerUltimasSimulacoes,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports';

export const useVerUltimasSimulacoesDeRenda =
  (): IUseVerUltimasSimulacoesDeRendaReturn => {
    const { invocarApiGatewayCvpComToken, loading } = useVerUltimasSimulacoes();
    const [dataUltimasSimulacoes, setDataUltimasSimulacoes] =
      useState<IUseVerUltimasSolicitacoesResponse[]>();

    const handleVerUltimasSimulacoes = async (): Promise<void> => {
      try {
        const response = await invocarApiGatewayCvpComToken();
        const { entidade } = response!;

        setDataUltimasSimulacoes(entidade);
      } catch (error) {
        setDataUltimasSimulacoes([]);
      }
    };

    const handleClearUltimasSolicitacoes = () => {
      setDataUltimasSimulacoes([]);
    };

    return {
      loading,
      dataUltimasSimulacoes,
      handleVerUltimasSimulacoes,
      setDataUltimasSimulacoes,
      handleClearUltimasSolicitacoes,
    };
  };
