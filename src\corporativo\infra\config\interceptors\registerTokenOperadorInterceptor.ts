import { tryGetValueOrDefault } from '@cvp/utils';
import { InternalAxiosRequestConfig } from 'axios';
import { obterTokenOperador } from '../../../../shared/infra/consultarMatrizAcessoPrevidencia.ts/obterTokenOperador';
import api from '../api/axiosConfig';
import { URLS_PECOS_MATRIZ } from '../api/endpoints';

interface IMapInterceptor {
  (request: InternalAxiosRequestConfig): Promise<
    InternalAxiosRequestConfig<unknown>
  >;
}

export const mapInterceptor: IMapInterceptor = async (
  request: InternalAxiosRequestConfig,
) => {
  const urlMatch = URLS_PECOS_MATRIZ.some(url =>
    url.includes(tryGetValueOrDefault([request.url], '')),
  );

  if (urlMatch) {
    const token = await obterTokenOperador();

    if (!(request.data instanceof FormData)) {
      request.data = {
        ...request.data,
        xToken: token,
      };
    }
    if (request.data instanceof FormData) {
      request.data.append('xToken', token as string);
    }
  }

  return request;
};

export const registerTokenOperadorInterceptor = (): void => {
  api.interceptors.request.use(async request => {
    await mapInterceptor(request);
    return request;
  });
};
