import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useAssinaturaResgate = (
  temContaPreenchida: boolean,
): Resgate.IUseAssinaturaResgate => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();
  const { assinaturaValida, validarAssinatura } =
    Resgate.useValidarAssinatura();
  const { invocarApiGatewayCvpComToken } =
    Resgate.useRegistrarTokenAssinaturaCaixa();

  const obterAssinatura = (response: Resgate.IAssinaturaResponse): void => {
    Resgate.setSessionItem('assinaturaResponse', JSON.stringify(response));
    invocarApiGatewayCvpComToken(response);
    validarAssinatura(response);
  };

  const podeExibirAssinatura: boolean = Resgate.checkIfAllItemsAreTrue([
    !!formik.values.motivoResgate,
    temContaPreenchida,
  ]);

  return {
    obterAssinatura,
    podeExibirAssinatura,
    assinaturaValida,
  };
};
