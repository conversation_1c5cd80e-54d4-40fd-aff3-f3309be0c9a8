import {
  TableColumn,
  Types,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

export type TExplicacaoTipos = 'REAJUSTE' | 'REENQUADRAMENTO';

export type TExplicacaoConteudoProps = {
  tipo: TExplicacaoTipos;
};

export type TObterColunasHistoricoAtualizacoes = (
  consultarDetalhes: (row: Types.IResponseHistoricoAtualizacoes) => void,
) => TableColumn<Types.IResponseHistoricoAtualizacoes>[];

export type TObterColunasHistoricoPorAno =
  () => TableColumn<Types.IAnoHistorico>[];

export type TPeriodoExibidoProps = {
  data: Types.IAnoHistorico[];
  onClickVoltar?: VoidFunction;
};

export type TFiltroPeriodoProps = {
  ano: string;
  atualizacoes: Types.IResponseHistoricoAtualizacoes[];
  selecionarFiltro: (ano: string) => void;
};

export type TUsePeriodoHistorico = {
  response: Types.IAnoHistorico[];
  loading: boolean;
  anoAtivo: string;
  alterarAnoAtivo: (ano: string) => void;
  voltarEtapa: () => void;
};
