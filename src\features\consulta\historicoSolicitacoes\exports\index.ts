export { useState, useContext } from 'react';

export {
  Grid,
  LoadingSpinner,
  Text,
  ConditionalRenderer,
  Button,
  Select,
  DateField,
  Table,
  Alert,
  IconWarningRound,
  IconChevronLeftSharp,
  IconChevronRightSharp,
  IconFirstPageSharp,
  IconLastPageSharp,
  Paginator,
  useTheme,
} from '@cvp/design-system-caixa';

export { getSessionItem } from '@cvp/utils';
export {
  usePaginatedData,
  usePaginator,
  Match,
  SwitchCase,
} from '@cvp/componentes-posvenda';

export * from '@cvp/utils';
export * from '@src/corporativo/infra/consulta/historicoSolicitacao';
export * from '@src/features/consulta/historicoSolicitacoes/constants';
export * from '@src/features/consulta/historicoSolicitacoes/factory';
export * from '@src/features/consulta/historicoSolicitacoes/hooks';
export * from '@src/features/consulta/historicoSolicitacoes/types';
export * from '@src/features/consulta/historicoSolicitacoes/utils';
export * from '@src/shared/types/TSelectItem';

export * as Styles from '@src/features/consulta/historicoSolicitacoes/styles';

export * from '@src/corporativo/types/consulta/historicoSolicitacao';
export { useObterComprovante } from '@src/shared/infra/financeiro/useObterComprovante';
export { useObterHistoricoSolicitacao } from '@src/corporativo/infra/consulta/historicoSolicitacao/useObterHistoricoSolicitacao';

export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
export { tipoEmailConstants } from '@src/corporativo/infra/email/tipoEmail';
export { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';

export { useNavigate } from 'react-router-dom';

export { Divider } from '@src/corporativo/components/divider';

export { FiltroHistoricoSolicitacoes } from '@src/features/consulta/historicoSolicitacoes/views/FiltroHistoricoSolicitacoes';
export { ListaHistoricoSolicitacoes } from '@src/features/consulta/historicoSolicitacoes/views/ListaHistoricoSolicitacoes';
export { FiltroPeriodo } from '@src/features/consulta/historicoSolicitacoes/views/FiltroPeriodo';
export { ListaHistoricoPaginacao } from '@src/features/consulta/historicoSolicitacoes/views/ListaHistoricoPaginacao';

export { default as IconPDF } from '@src/corporativo/components/PDFIcon';
export { type TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';
export { type IHandleReponseResult } from '@cvp/componentes-posvenda';

export { type IObterComprovanteResgateResponse } from '@src/shared/types/ObterComprovanteResgate/IObterComprovanteResgate';
export { useMensagemTemporaria } from '@src/shared/hooks/useMensagemTemporaria';

export { AssinaturaSolicitacao } from '@src/features/consulta/historicoSolicitacoes/views/AssinaturaSolicitacao';

export { default as Assinatura } from '@src/corporativo/components/Assinatura/Assinatura';
export type { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
