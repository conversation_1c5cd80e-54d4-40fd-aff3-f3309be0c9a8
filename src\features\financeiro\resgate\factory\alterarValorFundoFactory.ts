import {
  getTernaryResult,
  IListarFundosParaResgateFundosDisponiveis,
  IAlterarValorFundoFactory,
} from '@src/features/financeiro/resgate/exports';

/**
 * Alterar o valor de retirada de um fundo específico.
 *
 * @param {Object} params - Os parâmetros necessários para alterar o valor do fundo
 * @param {IListarFundosParaResgateFundosDisponiveis[]} params.fundos - Lista de fundos disponíveis para resgate
 * @param {string} params.codigoFundo - Código do fundo que terá o valor alterado
 * @param {number} params.valorRetirar - Novo valor a ser retirado do fundo
 * @returns {IListarFundosParaResgateFundosDisponiveis[]} Lista atualizada de fundos com o novo valor para o fundo especificado
 */
export const alterarValorFundoFactory = ({
  fundos,
  codigoFundo,
  valorRetirar,
}: IAlterarValorFundoFactory): IListarFundosParaResgateFundosDisponiveis[] => {
  return fundos.map(fundo => {
    const isFundoSelecionado: boolean = fundo.codigoFundo === codigoFundo;

    return getTernaryResult(
      isFundoSelecionado,
      { ...fundo, valorRetirar },
      fundo,
    );
  });
};
