import { useContext } from 'react';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { TUseListarValoresResponse, useListarValores, UTILS } from '../exports';

export interface IObterDados {
  loadingFundos: boolean;
  responseFundos: TUseListarValoresResponse;
}

export function useObterFundos(): IObterDados {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { loading, response } = useListarValores({
    cpf: UTILS.tryGetValueOrDefault(
      [String(UTILS.getSessionItem('cpfCnpj'))],
      '',
    ),
    codConta: UTILS.tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    ),
    valor: '0',
  });

  return {
    loadingFundos: loading,
    responseFundos: response,
  };
}
