import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export const ModalTransferencia: React.FC<
  TransferenciaEntreFundos.TModalTransferenciaProps
> = ({ onClickConfirmar, children }) => {
  const { modalProsseguir, setModalProsseguir } =
    TransferenciaEntreFundos.useTransferenciaContext();

  return (
    <TransferenciaEntreFundos.Dialog
      open={modalProsseguir}
      onOpenChange={setModalProsseguir}
    >
      <TransferenciaEntreFundos.Dialog.Content maxWidth="474px">
        <TransferenciaEntreFundos.HeaderModal style={{ borderBottom: 'none' }}>
          <TransferenciaEntreFundos.Text
            display="block"
            lineheight="distant"
            textAlign="left"
            variant="text-big-400"
          >
            {
              TransferenciaEntreFundos.CONSTANTES.ALERTAS
                .MENSAGEM_DESTAQUE_ATENCAO
            }
          </TransferenciaEntreFundos.Text>
        </TransferenciaEntreFundos.HeaderModal>
        <TransferenciaEntreFundos.Dialog.Body style={{ paddingBottom: 0 }}>
          <TransferenciaEntreFundos.Grid gap="10px" margin="0">
            {children}
          </TransferenciaEntreFundos.Grid>
        </TransferenciaEntreFundos.Dialog.Body>

        <TransferenciaEntreFundos.FooterModal>
          <TransferenciaEntreFundos.Dialog.Cancel
            style={{ border: 'none', padding: 0 }}
          >
            <TransferenciaEntreFundos.Button
              variant="secondary-outlined"
              size="small"
            >
              {TransferenciaEntreFundos.CONSTANTES.ALERTAS.BOTAO_VOLTAR}
            </TransferenciaEntreFundos.Button>
          </TransferenciaEntreFundos.Dialog.Cancel>
          <TransferenciaEntreFundos.Button
            variant="primary"
            size="small"
            onClick={onClickConfirmar}
          >
            {TransferenciaEntreFundos.CONSTANTES.ALERTAS.BOTAO_SIM}
          </TransferenciaEntreFundos.Button>
        </TransferenciaEntreFundos.FooterModal>
      </TransferenciaEntreFundos.Dialog.Content>
    </TransferenciaEntreFundos.Dialog>
  );
};
