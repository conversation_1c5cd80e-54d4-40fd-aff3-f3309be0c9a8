import {
  getTernaryResult,
  IConsultarContribuicaoRegularFundos,
  IAlterarSelecaoFundoContribuicaoFactory,
  tryGetValueOrDefault,
} from '@src/features/financeiro/resgate/exports';

/**
 * Alterar a seleção de um fundo de contribuição regular no contexto de resgate
 *
 * Esta função recebe uma lista de fundos de contribuição regular e altera o estado de seleção
 * de um fundo específico, definindo seu valor de contribuição quando selecionado ou zerando-o
 * quando não selecionado. Para todos os outros fundos, o estado de seleção é definido como falso e
 * seus valores de contribuição são zerados.
 *
 * @param {Object} params - Parâmetros para alteração da seleção
 * @param {IConsultarContribuicaoRegularFundos[]} params.fundos - Lista de fundos de contribuição regular
 * @param {string} params.codigoFundo - Código do fundo que terá seu estado de seleção alterado
 * @param {number} params.valorContribuicaoRegularlAtual - Valor de contribuição a ser aplicado quando o fundo for selecionado
 * @returns {IConsultarContribuicaoRegularFundos[]} Lista atualizada de fundos com o estado de seleção modificado
 */
export const alterarSelecaoFundoContribuicaoFactory = ({
  fundos,
  codigoFundo,
  valorContribuicaoRegularlAtual,
}: IAlterarSelecaoFundoContribuicaoFactory): IConsultarContribuicaoRegularFundos[] => {
  const fundosContribuicaoRegular = tryGetValueOrDefault([fundos], []);

  return fundosContribuicaoRegular.map(fundo => {
    const isFundoSelecionado: boolean = fundo.codigoFundo === codigoFundo;

    return getTernaryResult(
      isFundoSelecionado,
      {
        ...fundo,
        selecionado: !fundo.selecionado,
        valorContribuicao: tryGetValueOrDefault(
          [valorContribuicaoRegularlAtual],
          0,
        ),
      },
      { ...fundo, selecionado: false, valorContribuicao: 0 },
    );
  });
};
