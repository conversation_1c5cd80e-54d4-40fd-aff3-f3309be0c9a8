import { TableEdit } from '@cvp/design-system-caixa';
import { colunasTabelaDadosPlanos } from '@src/features/dadosPlano/factory/colunaTabelaDadosPlanos';
import { useContext } from 'react';

import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import {
  DS as Ds,
  InfoDadosPlano,
  ListaDadosPlanos,
  LOADING,
  Match,
  SwitchCase,
  useGetCoberturas,
  useObterDadosPlano,
  useObterFundos,
  UTILS,
} from '../exports';

const DadosPlanoPage = (): React.JSX.Element => {
  const { response, calcularDuracao, loading } = useObterDadosPlano();
  const { responseFundos, loadingFundos } = useObterFundos();
  const { responseCobertura, loadingCobertura } = useGetCoberturas();

  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const fundosExistentes = UTILS.tryGetValueOrDefault(
    [responseFundos?.fundosExistentes?.fundos],
    [],
  ).map(item => ({
    title: `Fundo de investimento: ${item.nomeFundo}`,
    value: UTILS.valoresMonetarios.mask(item.saldoAtual),
  }));

  const beneficios = UTILS.tryGetValueOrDefault(
    [responseCobertura?.beneficios],
    [],
  ).map(item => ({
    title: `Cobertura: ${item.nomTipoPagamento}`,
    value: '-',
  }));

  return (
    <Ds.Grid justify="center">
      <SwitchCase>
        <Match when={!!loading}>
          <Ds.Grid margin="30px">
            <Ds.LoadingSpinner size="medium">{LOADING}</Ds.LoadingSpinner>
          </Ds.Grid>
        </Match>
        <Match when={!loading}>
          <Ds.GridItem xs="1">
            <SwitchCase fallback={<div />}>
              <Match when={!loading}>
                <InfoDadosPlano
                  title="Duração do plano"
                  value={calcularDuracao(
                    response?.return?.dhtFimPlano,
                    response?.return?.dhtIniVigencia,
                  )}
                />
              </Match>
            </SwitchCase>
          </Ds.GridItem>

          <Ds.Separator orientation="horizontal" size="4" bgColor="gray-08" />
          <SwitchCase fallback={<div />}>
            <Match when={!loadingFundos}>
              <ListaDadosPlanos listaInfoDadosPlano={fundosExistentes} />
            </Match>
          </SwitchCase>

          <Ds.Separator orientation="horizontal" size="4" bgColor="gray-08" />
          <SwitchCase fallback={<div />}>
            <Match when={!loadingCobertura}>
              <ListaDadosPlanos listaInfoDadosPlano={beneficios} />
            </Match>
          </SwitchCase>
          <Ds.Separator orientation="horizontal" size="4" bgColor="gray-08" />
          <SwitchCase fallback={<div />}>
            <Match when={!loading}>
              <TableEdit
                themeTable="default"
                columns={colunasTabelaDadosPlanos()}
                data={certificadoAtivo?.certificadoBeneficios}
              />
            </Match>
          </SwitchCase>
        </Match>
      </SwitchCase>
    </Ds.Grid>
  );
};

export default DadosPlanoPage;
