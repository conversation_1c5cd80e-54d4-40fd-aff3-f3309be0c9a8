import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IUseVerUltimasSimulacoesBody,
  IUseVerUltimasSolicitacoes,
  IUseVerUltimasSolicitacoesResponse,
} from '@src/corporativo/types/consulta/IVerUltimasSolicitacoes';
import { useContext } from 'react';

const useVerUltimasSimulacoes = (): IUseVerUltimasSolicitacoes => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    IUseVerUltimasSimulacoesBody,
    IUseVerUltimasSolicitacoesResponse[]
  >(PECOS.ConsultarHistorico, {
    data: {
      Cpf: String(getSessionItem('cpfCnpj')),
      NumeroCertificado: certificadoAtivo?.certificadoNumero,
    },
  });

  return { loading, invocarApiGatewayCvpComToken };
};

export default useVerUltimasSimulacoes;
