import { IBeneficiarios } from './IBeneficiarios';

export interface IBeneficios {
  planoId: string;
  coberturaId: string;
  empresaId: string;
  descricaoCobertura: string;
  tipoBeneficio: string;
  situacao: string;
  subSituacao: string;
  valorBeneficio: string;
  valorContribuicao: string;
  numCnpjFundo: string;
  certificado: {
    empresaId: string;
    numCertificado: string;
  };
  termoDesejado: string;
  codTipoPagamentoOriginal: string;
  nomTipoPagamento: string;
  fatorRenda: string;
  vlrReversao: string;
  descBeneficiarioRecebeOriginal: string;
  beneficiarios: IBeneficiarios[] | undefined;
  tipoCobertura: string;
  descPeridoBeneficiarioRecebe: string;
}
