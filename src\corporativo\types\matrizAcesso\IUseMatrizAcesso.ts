import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
import { ICertificadoPrevidenciaResponse } from '@src/features/layoutPrevidencia/exports';
import { TTabItem } from '@src/features/layoutPrevidencia/types/tabsTypes';

export interface IUserPermissions {
  id: string;
  operation: string;
  service: string;
}

export interface ITokenOperadorMatrizAcessoCliente {
  tokenOperador: string;
  matrizAcessoCliente?: IMatrizAcessoCliente;
}

interface IMatrizCertificado {
  certificado: string;
  certificadoStatus: string;
  usuario: string;
  xPrevToken: string;
  xPrevTokenExpiration: string;
  lstServicesAvailable: IUserPermissions[];
}
export interface IMatrizAcessoCliente {
  nome: string;
  lstWebSession: IMatrizCertificado[];
}

export type TMatrizAcessoGIResponse =
  | IHandleReponseResult<ITokenOperadorMatrizAcessoCliente>
  | undefined;

export interface IMatrizAcessoProvider {
  certificadoTabs: TTabItem[];
  permissoesMatrizAcesso: string[];
  modalImprimirDocumentosPrimeiroSelectOpcoes: SelectItem[];
  isMatrizAcessoLoading: boolean;
}

export type TConsultasComponentesPermissoes = Record<
  string,
  { path: string; permissions: string[] }
>;

export type TImprimirDocumentosPermissoes = Record<
  string,
  { value: string; permissions: string[] }
>;

export interface IMatrizAcessoReturn {
  isMatrizAcessoLoading: boolean;
  fetchMatrizAcesso: () => Promise<TMatrizAcessoGIResponse>;
  setPermissoesUsuarioContexto: (
    matrizAcesso: TMatrizAcessoGIResponse,
    certificadoAtivo: ICertificadoPrevidenciaResponse,
    setMatrizAcessoPermissions: (value: React.SetStateAction<string[]>) => void,
    setModalImprimirDocumentosPrimeiroSelectOpcoes: (
      value: React.SetStateAction<SelectItem[]>,
    ) => void,
    setCertificadoTabs: React.Dispatch<React.SetStateAction<TTabItem[]>>,
  ) => void;
}
