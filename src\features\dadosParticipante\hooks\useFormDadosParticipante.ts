import * as DadosParticipante from '../exports';

type TUseFormDadosParticipante = (obterDadosParticipante: VoidFunction) => {
  dadosParticipante: DadosParticipante.IDadosParticipanteFormEntidade;
  editando: boolean;
  lgpdControls: DadosParticipante.IControlesLGPD;
  setLgpdControls: DadosParticipante.React.Dispatch<
    DadosParticipante.React.SetStateAction<DadosParticipante.IControlesLGPD>
  >;
  atualizarDadosParticipante: (
    values: DadosParticipante.IFormDadosParticipante,
  ) => Promise<void>;
};

export const useFormDadosParticipante: TUseFormDadosParticipante =
  obterDadosParticipante => {
    const { dadosParticipante, editando } = DadosParticipante.React.useContext(
      DadosParticipante.DadosParticipanteContext,
    );

    const { certificadoAtivo, nomeSocial } = DadosParticipante.React.useContext(
      DadosParticipante.PrevidenciaContext,
    );
    const { response: responseConsentimento } =
      DadosParticipante.useObterConsentimento(
        certificadoAtivo.certificadoNumero,
      );

    const [lgpdControls, setLgpdControls] =
      DadosParticipante.React.useState<DadosParticipante.IControlesLGPD>({
        dadosPessoaisOutrosProdutos: false,
        dadosPessoaisParceiros: false,
      });

    const { atualizarDadosParticipante } =
      DadosParticipante.useAtualizarDadosParticipante(
        lgpdControls,
        obterDadosParticipante,
      );

    const permitirConsentimento = (
      dados: DadosParticipante.ITiposConsentimento[],
      subprocesso: string,
    ): boolean => {
      const tipoConsentimento = dados.find(
        consentimento =>
          consentimento.subProcessoNegocio.codigo === Number(subprocesso),
      );

      return (
        tipoConsentimento?.consentimento.tipo ===
        Number(
          DadosParticipante.CONSENTIMENTO_CONSTANTES.SUB_PROCESSO_PARCEIROS,
        )
      );
    };

    const handleConsultarConsentimento = async () => {
      const responseConsetimentoTratado =
        DadosParticipante.Utils.tryGetValueOrDefault(
          [responseConsentimento?.entidade?.consentimentos],
          [],
        );

      const consentimentos = {
        dadosPessoaisOutrosProdutos: permitirConsentimento(
          responseConsetimentoTratado,
          DadosParticipante.CONSENTIMENTO_CONSTANTES.SUB_PROCESSO_PARCEIROS,
        ),
        dadosPessoaisParceiros: permitirConsentimento(
          responseConsetimentoTratado,
          DadosParticipante.CONSENTIMENTO_CONSTANTES.SUB_PROCESSO_CAIXA,
        ),
      };

      setLgpdControls(consentimentos);
    };

    DadosParticipante.React.useEffect(() => {
      if (
        DadosParticipante.Utils.checkIfAllItemsAreTrue([
          !!certificadoAtivo.certificadoNumero,
          !!responseConsentimento,
        ])
      ) {
        handleConsultarConsentimento();
      }
    }, [responseConsentimento]);

    return {
      dadosParticipante: { ...dadosParticipante, nomeSocial },
      editando,
      lgpdControls,
      setLgpdControls,
      atualizarDadosParticipante,
    };
  };
