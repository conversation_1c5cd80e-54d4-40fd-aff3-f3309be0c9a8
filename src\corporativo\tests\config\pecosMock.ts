import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { IBeneficios } from '@src/features/beneficiarios/exports';
import { mockObterCoberturas } from '@src/features/beneficiarios/mocks/respostaObterCoberturas';

function successAutoFetch<T>(response: T) {
  return {
    response: {
      sucesso: true,
      entidade: {
        retorno: [response],
      },
    },
    loading: false,
    invocarApiGatewayCvpComToken: jest.fn(),
  };
}

export const PecosMock = {
  [PECOS.ObterCoberturas]: successAutoFetch<IBeneficios>(mockObterCoberturas),
};
