import React from 'react';
import * as HistoricoSolicitacoes from '@src/features/consulta/historicoSolicitacoes/exports';

type TFiltroHistoricoSolicitacoesProps = {
  tiposSolicitacoes: HistoricoSolicitacoes.TSelectItem;
  controll: HistoricoSolicitacoes.TUseFiltroHistoricoSolicitacoes;
  onSubmit: (filtros: HistoricoSolicitacoes.TConsultaFiltros) => void;
  loading: boolean;
};

export const FiltroHistoricoSolicitacoes: React.FC<
  TFiltroHistoricoSolicitacoesProps
> = ({ tiposSolicitacoes, onSubmit, controll, loading }) => {
  const { selecionarFiltro, onClickConsultar, filtros } = controll;

  return (
    <>
      <HistoricoSolicitacoes.Grid margin="0" gap="2.5rem">
        <HistoricoSolicitacoes.Styles.ItemFiltroContainer>
          <HistoricoSolicitacoes.Select
            label={HistoricoSolicitacoes.TEXTOS.TIPO_SOLICITACAO}
            options={tiposSolicitacoes}
            variant="box-classic"
            size="standard"
            sizeWidth="standard"
            placeholder="Todos"
            onChange={option =>
              selecionarFiltro(HistoricoSolicitacoes.TIPO_SOLICITACAO, option)
            }
          />
        </HistoricoSolicitacoes.Styles.ItemFiltroContainer>

        <HistoricoSolicitacoes.FiltroPeriodo controll={controll} />

        <HistoricoSolicitacoes.Styles.ItemFiltroSubmitContainer>
          <HistoricoSolicitacoes.Button
            disabled={HistoricoSolicitacoes.checkIfSomeItemsAreTrue([
              !filtros.periodoValor,
              !!loading,
            ])}
            variant="primary"
            onClick={() => {
              onClickConsultar(filtroAtivo => onSubmit(filtroAtivo));
            }}
          >
            {HistoricoSolicitacoes.TEXTOS.CONSULTAR}
          </HistoricoSolicitacoes.Button>
        </HistoricoSolicitacoes.Styles.ItemFiltroSubmitContainer>
      </HistoricoSolicitacoes.Grid>

      <HistoricoSolicitacoes.ConditionalRenderer
        condition={HistoricoSolicitacoes.checkIfAllItemsAreTrue([
          !!filtros.filtroAtivo,
          !!filtros.periodoValor,
        ])}
      >
        <HistoricoSolicitacoes.Text
          variant="text-standard-400"
          marginTop="1.5rem"
          marginBottom="1.5rem"
        >
          {HistoricoSolicitacoes.TEXTOS.EXIBICAO_PERIODO}
          {HistoricoSolicitacoes.obterLabelFiltroAplicado(
            filtros.filtroAtivo,
            filtros.periodoValor?.value ===
              HistoricoSolicitacoes.VALORES_PERIODOS.PERIODO_PERSONALIZADO,
          )}
        </HistoricoSolicitacoes.Text>
      </HistoricoSolicitacoes.ConditionalRenderer>
    </>
  );
};
