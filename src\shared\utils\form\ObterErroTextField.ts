import * as UTILS from '@cvp/utils';
import { TError } from '@src/shared/types/textField/TError';
import { FormikErrors, FormikProps, FormikTouched } from 'formik';

/**
 * Verifica existencia de erros de validacao no TextField do pacote @cvp/design-system-caixa.
 *
 * Retorna obtero TError para em um campo de formulário integrado com Formik.
 *
 * @template T - O tipo genérico que representa os dados do formulário.
 *
 * @param {FormikProps<T>} formik - As propriedades do Formik associadas ao formulário.
 * @param {keyof FormikTouched<T> | keyof FormikErrors<T>} nomePropriedade - O nome da propriedade do campo que será validado.
 *
 * @returns {TError} - Retorna um objeto contendo a propriedade `error` com um booleano indicando se há erro e uma mensagem de erro.
 *
 * @example
    ```typescriptreact
      <TextField
        variant="box-classic"
        placeholder=""
        error={obterErroTextField(
          formik,
          name as keyof IDadosSeguradoFormatado,
        )}
      />
      ```
 */
export const obterErroTextField = <T>(
  formik: FormikProps<T>,
  nomePropriedade: keyof FormikTouched<T> | keyof FormikErrors<T>,
): TError => {
  const usuarioErrou = UTILS.checkIfAllItemsAreTrue([
    !!formik.touched[nomePropriedade],
    !!formik.errors[nomePropriedade],
  ]);

  const usuarioNaoViu = UTILS.checkIfAllItemsAreTrue([
    !!formik.errors[nomePropriedade],
    formik.submitCount > 0,
  ]);

  const exibirErro = UTILS.checkIfSomeItemsAreTrue([
    usuarioErrou,
    usuarioNaoViu,
  ]);

  return {
    error: exibirErro,
    message: String(formik.errors[nomePropriedade]),
  };
};
