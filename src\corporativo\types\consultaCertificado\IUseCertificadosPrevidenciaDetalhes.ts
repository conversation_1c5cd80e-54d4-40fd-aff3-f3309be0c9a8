import { IUseCertificadosDetalhesRequest } from '@src/corporativo/types/consultaCertificado/IUseCertificadosPrevidenciaDetalhesRequest';
import { ICertificadoPrevidenciaDetalhesResponse } from '@src/shared/types/ICertificadoPrevidenciaDetalhesResponse';

export interface IUseCertificadosPrevidenciaDetalhes {
  (payload: IUseCertificadosDetalhesRequest): {
    loading: boolean;
    response: ICertificadoPrevidenciaDetalhesResponse[];
  };
}
