import {
  verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias,
  TIPO_TRANSFERENCIA_OPCOES_PERMISSOES,
  OpcoesPrimeiroSelect,
  TSelectOption,
} from '@src/features/financeiro/transferencias/exports';

export const useFiltraTipoTransferencia = (
  userPermissions: string[],
): TSelectOption[] => {
  return OpcoesPrimeiroSelect.filter(opcaoSelect =>
    verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      userPermissions,
      TIPO_TRANSFERENCIA_OPCOES_PERMISSOES[opcaoSelect.value].permissions,
    ),
  );
};
