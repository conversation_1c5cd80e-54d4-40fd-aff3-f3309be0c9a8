import {
  getTernaryResult,
  IDadosBancariosAporte,
  IDadosTabelaFundos,
  IFiltroTabelaAporte,
  tryGetValueOrDefault,
  useAporteContext,
} from '@src/features/financeiro/aporte/exports';

type TUseDadosComprovanteAporte = (
  fundosExistentes: IDadosTabelaFundos,
  fundosNovos: IDadosTabelaFundos,
) => {
  filtroTabelaFundos: IFiltroTabelaAporte;
  contaBancariaSelecionada: IDadosBancariosAporte;
  perfilInvestidorConcat: string;
};

export const useDadosComprovanteAporte: TUseDadosComprovanteAporte = (
  fundosExistentes,
  fundosNovos,
) => {
  const { filtroTabelaFundos, contaBancariaSelecionada } = useAporteContext();

  const perfilInvestidorSelecionadoExistente = tryGetValueOrDefault(
    [fundosExistentes.dadosFundos?.map(fundo => fundo.descPerfilFundo)],
    [],
  );

  const perfilInvestidorSelecionadoNovo = tryGetValueOrDefault(
    [fundosNovos.dadosFundos?.map(fundo => fundo.descPerfilFundo)],
    [],
  );

  const perfilInvestidor = [
    ...new Set([
      ...perfilInvestidorSelecionadoExistente,
      ...perfilInvestidorSelecionadoNovo,
    ]),
  ];

  const perfilInvestidorConcat = getTernaryResult(
    perfilInvestidor.length > 1,
    `${perfilInvestidor.slice(0, -1).join(', ')} e ${perfilInvestidor.slice(
      -1,
    )}`,
    perfilInvestidor[0],
  );
  return {
    filtroTabelaFundos,
    contaBancariaSelecionada,
    perfilInvestidorConcat,
  };
};
