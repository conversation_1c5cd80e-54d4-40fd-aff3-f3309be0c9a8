import {
  Grid,
  GridItem,
  IDadosNovaContaBancariaAporte,
  IHandleChange,
  InputText,
  NOVA_CONTA_DEBITO,
  Select,
  SelectItem,
  TIPO_OPERACAO,
  TSetFieldValue,
  Text,
} from '@src/features/financeiro/aporte/exports';

interface IFormularioNovaContaBancariaAporte {
  handleChange: IHandleChange;
  values: IDadosNovaContaBancariaAporte;
  setFieldValue: TSetFieldValue;
}

const FormularioNovaContaBancariaAporte: React.FC<
  IFormularioNovaContaBancariaAporte
> = ({ handleChange, values, setFieldValue }) => {
  return (
    <Grid>
      <GridItem xs="1/3">
        <Text fontColor="content-neutral-05" variant="text-large-600">
          {NOVA_CONTA_DEBITO.tipoOperacao}
        </Text>
        <Select
          options={TIPO_OPERACAO.map(item => ({
            text: `${item.id} - ${item.valor}`,
            value: item.id,
          }))}
          onChange={(value: SelectItem[]) => {
            const operacao = value[0].value;
            setFieldValue('operacao', operacao);
          }}
          id="operacao"
          placeholder={NOVA_CONTA_DEBITO.escolha}
          size="large"
          sizeWidth={undefined}
          variant="box-classic"
        />
      </GridItem>
      <GridItem xs="1/5">
        <Text fontColor="content-neutral-05" variant="text-large-600">
          {NOVA_CONTA_DEBITO.agencia}
        </Text>
        <InputText
          type="text"
          name="agencia"
          variant="box-classic"
          onChange={handleChange}
          arialabel={NOVA_CONTA_DEBITO.agencia}
          value={values.agencia}
          placeholder="0000"
          maxLength={4}
          required
        />
      </GridItem>
      <GridItem xs="1/4">
        <Text fontColor="content-neutral-05" variant="text-large-600">
          {NOVA_CONTA_DEBITO.contaBancaria}
        </Text>
        <InputText
          name="contaBancaria"
          type="text"
          variant="box-classic"
          onChange={handleChange}
          arialabel={NOVA_CONTA_DEBITO.contaBancaria}
          value={values.contaBancaria}
          placeholder="00000000"
          maxLength={9}
          required
        />
      </GridItem>
      <GridItem xs="1/5">
        <Text fontColor="content-neutral-05" variant="text-large-600">
          {NOVA_CONTA_DEBITO.digito}
        </Text>
        <InputText
          name="digito"
          type="text"
          variant="box-classic"
          onChange={handleChange}
          arialabel={NOVA_CONTA_DEBITO.digito}
          value={values.digito}
          placeholder="0"
          maxLength={1}
          required
        />
      </GridItem>
    </Grid>
  );
};

export default FormularioNovaContaBancariaAporte;
