import { useEffect } from 'react';
import { Alert } from '@cvp/design-system-caixa';
import { IAlertaTimeoutProps } from '@src/corporativo/types/alerta/IAlerta';
import * as S from './styles';

const AlertaTimeout: React.FC<IAlertaTimeoutProps> = ({
  alerta,
  callbackTimeout,
  duration = 7000,
  withoutTimeout = false,
}) => {
  useEffect(() => {
    if (withoutTimeout) return;
    setTimeout(() => {
      callbackTimeout();
    }, duration);
  }, [callbackTimeout, duration, withoutTimeout]);

  return (
    <S.AlertaApoliceContainer>
      <Alert icon={alerta?.icon} variant={alerta.variant}>
        {alerta?.message}
      </Alert>
    </S.AlertaApoliceContainer>
  );
};

export default AlertaTimeout;
