import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { IObterComprovanteResgateResponse } from '@src/shared/types/ObterComprovanteResgate/IObterComprovanteResgate';
import { useContext } from 'react';

export const useObterComprovante = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload = {
    cpfCnpj,
    numeroCertificado: certificadoAtivo?.certificadoNumero,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<typeof payload, IObterComprovanteResgateResponse>(
      PECOS.ObterComprovanteResgate,
      {
        data: payload,
        autoFetch: false,
      },
    );

  return {
    loading,
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as IObterComprovanteResgateResponse,
    ),
    invocarApiGatewayCvpComToken,
  };
};
