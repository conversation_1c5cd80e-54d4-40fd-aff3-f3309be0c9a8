import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Montar o detalhamento completo de uma alíquota de resgate
 *
 * Esta função constrói uma lista estruturada com todos os detalhes da simulação de resgate
 * para exibição na interface, incluindo dados do certificado, valores, datas e taxas.
 *
 * @param {Object} params - Parâmetros para montagem do detalhamento
 * @param {string} params.tipoAliquota - Tipo de alíquota (progressiva ou regressiva)
 * @param {Object} params.dadosSelecaoAliquota - Dados da seleção de alíquota
 * @param {Object} params.saldo - Informações de saldo do cliente
 * @returns {IMontarDetalhadoAliquotaFactoryRetorno[]} Lista estruturada com todos os detalhes da alíquota
 * formatados para exibição na interface
 */
export const montarDetalhadoAliquotaFactory = ({
  tipoAliquota,
  dadosSelecaoAliquota,
  saldo,
}: Resgate.IMontarDetalhadoAliquotaFactory): Resgate.IMontarDetalhadoAliquotaFactoryRetorno[] => {
  const isAliquotaProgressiva: boolean =
    tipoAliquota === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO;

  const {
    resumo,
    detalhado,
  }: Resgate.IConsultarDetalhesDaAliquotaFactoryRetorno =
    Resgate.mapearDadosPorAliquotaFactory(
      isAliquotaProgressiva,
      dadosSelecaoAliquota,
    );

  const dataFormatada: string = Resgate.dateTimeFormat(new Date());

  return [
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.NOME,
      value: resumo?.nomeCliente,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.CERTIFICADO,
      value: resumo?.codigoCertificado,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.PRODUTO_MODALIDADE,
      value: `${resumo?.descricaoProduto}, ${resumo?.tipoProduto}`,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.DATA_HORA_SIMULACAO,
      value: dataFormatada,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.SALDO_EM(dataFormatada),
      value: saldo?.saldoTotal?.toString(),
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_NOMINAL_ESTIMADO,
      value: detalhado?.totalSaldoPrincipal,
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_SIMULADO,
      value: detalhado?.totalValorSolicitado,
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_IR_ESTIMADO,
      value: detalhado?.totalValorIrrf,
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_LIQUIDO_ESTIMADO,
      value: detalhado?.totalValorLiquido,
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.TAXA_CARREGAMENTO_ESTIMADA,
      value: detalhado?.totalCarregamentoSaida,
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_MAXIMO_PERMITIDO,
      value: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_PADRAO_VAZIO,
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
    {
      label: Resgate.DETALHADO_ALIQUOTA_LABELS.VALOR_BLOQUEADO,
      value: saldo?.saldoTotalBloqueado?.toString(),
      mask: Resgate.DETALHADO_ALIQUOTA_LABELS.MASCARA.MONETARIO,
    },
  ];
};
