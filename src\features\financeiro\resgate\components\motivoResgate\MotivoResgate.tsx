import * as Resgate from '@src/features/financeiro/resgate/exports';

export const MotivoResgate = (): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <Resgate.GridItem xs="1" lg="1/3">
      <Resgate.Text variant="text-standard-600" fontColor="content-neutral-05">
        Motivo do resgate*
      </Resgate.Text>

      <Resgate.Select
        id="motivoResgate"
        onChange={event =>
          formik.setFieldValue('motivoResgate', event[0]?.value)
        }
        options={Resgate.mapearSelectFormFactory({
          lista: resgateFeatureData?.listaMotivosResgate,
          getText: motivo => motivo.descricao,
          getValue: motivo => motivo.codigo,
        })}
        placeholder="Selecione"
        size="standard"
        sizeWidth="standard"
        variant="box-classic"
      />
    </Resgate.GridItem>
  );
};
