import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IConfirmarResgatePayload {
  numeroResgate: string;
  codigoCertificado: string;
}
export interface IConfirmarResgateResponse {
  status: string;
  mensagem: string;
}
export interface IUseConfirmarResgateReturn {
  dadosConfirmacaoResgate: IConfirmarResgateResponse;
  isLoadingConfirmacaoResgate: boolean;
  confirmarResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<IConfirmarResgateResponse> | undefined>;
}
