import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IObterComprovanteResgatePayload {
  numeroCertificado: string;
  cpfCnpj: string;
  codigoRequisicao: string;
  idRequisicao: string;
}

export interface IObterComprovanteResgateResponse {
  comprovante: string;
  msgErroExcessao: string;
  base64: string;
  cpf: string;
}

export interface IUseObterComprovanteResgate {
  comprovanteResgate: IObterComprovanteResgateResponse;
  isLoadingComprovanteResgate: boolean;
  obterComprovanteResgate: (
    dynamicPayload?: Partial<IObterComprovanteResgatePayload>,
  ) => Promise<
    IHandleReponseResult<IObterComprovanteResgateResponse> | undefined
  >;
}
