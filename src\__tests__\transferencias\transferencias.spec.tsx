import {
  OpcoesPrimeiroSelect,
  useFiltraTipoTransferencia,
} from '@src/features/financeiro/transferencias/exports';

describe('selectModalTiposTransferenciaOpcoes', () => {
  it('retorna todas as opções quando o usuário tem todas as permissões', () => {
    const permissoes = [
      'TRANSFERENCIA_DE_FUNDOS',
      'TRANSFERENCIA_EMISSAO',
      'TRANSFERENCIA_CERTIFICADO',
    ];

    const result = useFiltraTipoTransferencia(permissoes);

    expect(result).toEqual(OpcoesPrimeiroSelect);
  });

  it('retorna apenas as opções que o usuário tem permissão', () => {
    const permissoes = ['TRANSFERENCIA_DE_FUNDOS', 'TRANSFERENCIA_CERTIFICADO'];

    const result = useFiltraTipoTransferencia(permissoes);

    expect(result).toEqual([
      {
        value: 'TRANSFERENCIA_DE_FUNDOS',
        text: 'Transferência entre fundos',
      },
      {
        value: 'TRANSFERENCIA_CERTIFICADO',
        text: 'Transferência entre certificados',
      },
    ]);
  });

  it('retorna vazio se o usuário não tem permissão para nenhuma opção', () => {
    const permissoes = ['OUTRA_PERMISSAO'];

    const result = useFiltraTipoTransferencia(permissoes);

    expect(result).toEqual([]);
  });
});
