import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useComprovanteResgate = (): Resgate.IUseComprovanteResgate => {
  const navigate = Resgate.useNavigate();

  const { resgateFeatureData } = Resgate.useResgateContext();

  const numeroResgateConsolidado = Resgate.tryGetValueOrDefault(
    [resgateFeatureData?.numeroResgateConsolidado],
    '',
  );

  const cpfCnpj = String(Resgate.getSessionItem('cpfCnpj'));

  const { obterComprovanteResgate, isLoadingComprovanteResgate } =
    Resgate.useObterComprovante();

  const { certificadoAtivo, setImpressao, setParametrosScroll } =
    Resgate.useContext(Resgate.PrevidenciaContext);

  const imprimirBoletoEComprovante = ({
    base64,
    tipoDocumento,
  }: Resgate.IImprimirBoletoEComprovante): void => {
    const urlPdf = URL.createObjectURL(Resgate.converterBase64(base64));
    const rota = window.location.pathname;
    const valorScroll = window.scrollY;

    setImpressao({
      tipoDocumento: Resgate.getTernaryResult(
        tipoDocumento === Resgate.TIPO_DOCUMENTO.BOLETO,
        Resgate.TIPO_DOCUMENTO.BOLETO,
        Resgate.TIPO_DOCUMENTO.COMPROVANTE,
      ),
      tipoEmail: Resgate.tipoEmailConstants.COMPROVANTE_RESGATE,
      urlPdf,
      parametrosEnvio: {
        numeroResgate: Resgate.tryGetValueOrDefault(
          [numeroResgateConsolidado],
          '',
        ),
        codigoRequisicao: Resgate.CODIGO_REQUISICAO.RESGATE,
        cpfCnpj,
        numeroCertificado: certificadoAtivo?.certificadoNumero,
      },
      base64: [base64],
    });

    setParametrosScroll({
      rota,
      valorScroll,
    });

    setParametrosScroll({
      rota,
      valorScroll,
    });

    navigate(Resgate.ROUTES.IMPRIMIR);
  };

  const handleComprovante = async (): Promise<void> => {
    const response = await obterComprovanteResgate({
      codigoRequisicao: Resgate.CODIGO_REQUISICAO.RESGATE,
      idRequisicao: Resgate.tryGetValueOrDefault(
        [numeroResgateConsolidado],
        '',
      ),
    });

    imprimirBoletoEComprovante({
      base64: Resgate.tryGetValueOrDefault(
        [response?.entidade?.comprovante],
        '',
      ),
      tipoDocumento: Resgate.TIPO_DOCUMENTO.COMPROVANTE,
    });
  };

  return { handleComprovante, isLoadingComprovanteResgate };
};
