import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { TUseObterCoberturasPayload } from '@src/corporativo/types/obterCoberturas/TUseObterCoberturasPayload';
import { TUseObterCoberturasResponse } from '@src/corporativo/types/obterCoberturas/TUseObterCoberturasResponse';
import { IUseObterCoberturasRequest } from '@src/corporativo/types/obterCoberturas/IUseObterCoberturasRequest';
import { tryGetValueOrDefault } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

export const useObterCoberturas: TUseObterCoberturasPayload = payload => {
  const { loading, response } = useApiGatewayCvpInvoker<
    IUseObterCoberturasRequest,
    TUseObterCoberturasResponse
  >(PECOS.ObterCoberturas, {
    data: {
      Cpf: tryGetValueOrDefault([payload?.Cpf], ''),
      NumeroCertificado: tryGetValueOrDefault([payload?.NumeroCertificado], ''),
    },
    autoFetch: true,
  });

  return {
    loading,
    response: tryGetValueOrDefault([response?.entidade?.retorno], null),
  };
};
