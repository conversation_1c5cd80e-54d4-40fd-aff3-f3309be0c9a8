import * as Resgate from '@src/features/financeiro/resgate/exports';

export const FormularioContaResgate = ({
  selecionarContaResgate,
  handleChangeCamposNovaContaResgate,
}: Resgate.IFormularioContaResgateProps): React.ReactElement => {
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <Resgate.ConditionalRenderer condition={!!formik.values.motivoResgate}>
      <Resgate.GridItem xs="1">
        <Resgate.SelecaoContaResgate onChange={selecionarContaResgate} />

        <Resgate.GridContaBancariaCustom>
          <Resgate.ConditionalRenderer condition={!!formik.values.isNovaConta}>
            <Resgate.CamposNovaContaResgate
              onChange={handleChangeCamposNovaContaResgate}
            />
          </Resgate.ConditionalRenderer>
        </Resgate.GridContaBancariaCustom>
      </Resgate.GridItem>
    </Resgate.ConditionalRenderer>
  );
};
