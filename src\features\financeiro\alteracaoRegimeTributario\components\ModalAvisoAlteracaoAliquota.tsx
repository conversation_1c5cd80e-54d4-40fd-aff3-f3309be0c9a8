import { Dialog } from '@cvp/design-system-caixa';
import { HeaderDialog } from '@src/features/financeiro/alteracaoRegimeTributario/style/styles';
import {
  Button,
  CONSTS,
  Match,
  ROUTES,
  SwitchCase,
  TButtonProps,
  Text,
  useModalAvisoAlteracaoAliquota,
} from '../exports';

interface IModalAvisoAlteracaoAliquota {
  mensagemModal: string;
  children?: TButtonProps;
  isOpen: boolean;
}

const ModalAvisoAlteracaoAliquota: React.FC<IModalAvisoAlteracaoAliquota> = ({
  mensagemModal,
  isOpen,
}) => {
  const {
    navigate,
    handleConfirmarAliquota,
    controlarModalAvisoAlteracaoAliquota,
  } = useModalAvisoAlteracaoAliquota();

  return (
    <Dialog open={isOpen} onOpenChange={controlarModalAvisoAlteracaoAliquota}>
      <Dialog.Content maxWidth="894px">
        <HeaderDialog>
          <Text variant="text-standard-400">{CONSTS.TITULOS.ATENCAO}</Text>
        </HeaderDialog>

        <Dialog.Body>
          <Text variant="text-big-400" textAlign="center">
            {mensagemModal}
          </Text>
        </Dialog.Body>

        <Dialog.Footer>
          <SwitchCase>
            <Match
              when={
                mensagemModal === CONSTS.MENSAGENS_MODAIS.ALTERACAO_ALIQUOTA
              }
            >
              <Button
                id={CONSTS.BOTOES.CONFIRMAR}
                onClick={event => handleConfirmarAliquota(event)}
              >
                {CONSTS.BOTOES.CONFIRMAR}
              </Button>
            </Match>
            <Match
              when={
                mensagemModal === CONSTS.ALERTS.AVISO_ALTERACAO_IRRETRATAVEL
              }
            >
              <Button
                id={CONSTS.BOTOES.FINALIZAR_ATENDIMENTO}
                onClick={() => {
                  navigate(ROUTES.INICIO);
                }}
              >
                {CONSTS.BOTOES.FINALIZAR_ATENDIMENTO}
              </Button>
            </Match>
          </SwitchCase>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};

export default ModalAvisoAlteracaoAliquota;
