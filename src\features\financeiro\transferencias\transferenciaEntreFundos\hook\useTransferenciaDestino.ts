import {
  useTransferenciaContext,
  useTransferenciaServicosContext,
  TResponseFundosDestino,
  EErroTransferencia,
  EEtapasTranferencia,
} from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export type TUseTransferenciaDestino = {
  confirmaDefinicaoTransferencia: () => Promise<void>;
  fundosDestinosPorEtapa: (
    fundos: TResponseFundosDestino[],
  ) => TResponseFundosDestino[];
  selecionaFundoDestino: (
    selecionado: boolean,
    fundo: TResponseFundosDestino,
  ) => void;
  alteraValorTransferenciaFundo: (
    fundo: TResponseFundosDestino,
    valor: number,
  ) => void;
};

function useTransferenciaDestino(): TUseTransferenciaDestino {
  const {
    fundosDestinos,
    setFundosDestinos,
    etapa,
    setEtapa,
    setErro,
    distribuicaoValores,
    setModalProsseguir,
  } = useTransferenciaContext();
  const {
    defineReservaDestino,
    consultaDestino,
    revalidarTransferencia,
    setMensagemErro,
  } = useTransferenciaServicosContext();

  const alteraValorTransferenciaFundo = (
    fundo: TResponseFundosDestino,
    valor: number,
  ) => {
    setFundosDestinos({
      ...fundosDestinos,
      [fundo.codFundo]: {
        ...fundo,
        transferenciaValor: valor,
      },
    });
  };

  const selecionaFundoDestino = (
    selecionado: boolean,
    fundo: TResponseFundosDestino,
  ) => {
    const novosFundos = { ...fundosDestinos };
    if (!selecionado) {
      delete novosFundos[fundo.codFundo];
    } else {
      novosFundos[fundo.codFundo] = {
        ...fundo,
        transferenciaValor: 0,
      };
    }

    setFundosDestinos(novosFundos);
  };

  const fundosDestinosPorEtapa = (fundos: TResponseFundosDestino[]) => {
    if (etapa === EEtapasTranferencia.DefinirFundosDestino) return fundos;
    return fundos.filter(fundo => Boolean(fundosDestinos[fundo.codFundo]));
  };

  const confirmaValidacaoTransferencia = async () => {
    try {
      await revalidarTransferencia.fetchData({
        numTransferencia: consultaDestino.response.numTransferencia,
      });

      setEtapa(EEtapasTranferencia.ValidarOperacao);
    } catch (err) {
      if (err instanceof Error) {
        setErro(EErroTransferencia.FluxoInvalido);
        setMensagemErro(err.message);
      }
    }
  };

  const confirmaDefinicaoTransferencia = async () => {
    try {
      setModalProsseguir(false);

      if (distribuicaoValores.distribuicaoRestante !== 0) {
        setErro(EErroTransferencia.ValorRestante);
        return;
      }

      await defineReservaDestino.fetchData(
        fundosDestinos,
        consultaDestino.response.numTransferencia,
      );
      await confirmaValidacaoTransferencia();
    } catch (err) {
      if (err instanceof Error) {
        setErro(EErroTransferencia.FluxoInvalido);
        setMensagemErro(err.message);
      }
    }
  };

  return {
    confirmaDefinicaoTransferencia,
    fundosDestinosPorEtapa,
    selecionaFundoDestino,
    alteraValorTransferenciaFundo,
  };
}

export default useTransferenciaDestino;
