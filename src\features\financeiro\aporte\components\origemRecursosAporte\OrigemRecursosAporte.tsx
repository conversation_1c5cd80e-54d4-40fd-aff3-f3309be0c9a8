import {
  BOTOES,
  Button,
  Dialog,
  EEtapasAporte,
  OpcoesOrigemRecursos,
  ORIGEM_RECURSOS_DECLARACAO,
  Text,
  useOrigemRecursosAporte,
} from '@src/features/financeiro/aporte/exports';

const OrigemRecursosAporte: React.FC = () => {
  const {
    declaracaoOrigem,
    openModal,
    openTextArea,
    etapa,
    setOpenModal,
    handleOrigemRecursoEditada,
    handleOrigemRecurso,
    handleProximaEtapa,
    setEtapa,
  } = useOrigemRecursosAporte();

  return (
    <Dialog
      open={etapa === EEtapasAporte.OrigemRecursosAporte}
      onOpenChange={() => setEtapa(EEtapasAporte.FiltrosTabelaAporte)}
    >
      <Dialog.Content>
        <Dialog.Header>
          <Text fontColor="content-neutral-05" variant="text-large-600">
            {ORIGEM_RECURSOS_DECLARACAO.declaracaoTitulo}
          </Text>
        </Dialog.Header>
        <Dialog.Body>
          <OpcoesOrigemRecursos
            openModal={openModal}
            openTextArea={openTextArea}
            setOpenModal={setOpenModal}
            handleOrigemRecursoEditada={handleOrigemRecursoEditada}
            handleOrigemRecurso={handleOrigemRecurso}
          />
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.Cancel asChild>
            <Button
              onClick={() => setEtapa(EEtapasAporte.FiltrosTabelaAporte)}
              variant="secondary-outlined"
            >
              {BOTOES.cancelar}
            </Button>
          </Dialog.Cancel>
          <Dialog.Action asChild>
            <Button
              onClick={handleProximaEtapa}
              disabled={declaracaoOrigem === ''}
              variant="primary"
            >
              {BOTOES.avancar}
            </Button>
          </Dialog.Action>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};

export default OrigemRecursosAporte;
