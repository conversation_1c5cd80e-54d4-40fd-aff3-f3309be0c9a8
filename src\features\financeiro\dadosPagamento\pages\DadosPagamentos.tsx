import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const DadosPagamentos: React.FC = () => {
  const { dadosCertificado } = DadosPagamento.useConsultaCertificadosPorCpf();

  const [editarPagamento, setEditarPagamento] = DadosPagamento.useState(false);
  const [abrirHistorico, setAbrir] = DadosPagamento.useState(false);

  const handleEditarPagamento = () => {
    setEditarPagamento(edit => !edit);
    setAbrir(false);
  };

  const handleAbrirHistorico = () => {
    setAbrir(open => !open);
    setEditarPagamento(false);
  };

  return (
    <>
      <DadosPagamento.SwitchCase fallback={null}>
        <DadosPagamento.Match
          when={DadosPagamento.checkIfAllItemsAreTrue([!editarPagamento])}
        >
          <DadosPagamento.ConditionalRenderer
            condition={!!dadosCertificado?.length}
          >
            <DadosPagamento.FormaPagamento
              pagamentoRegular={dadosCertificado[0]}
            />
          </DadosPagamento.ConditionalRenderer>
        </DadosPagamento.Match>
        <DadosPagamento.Match
          when={DadosPagamento.checkIfAllItemsAreTrue([editarPagamento])}
        >
          <DadosPagamento.AlterarDadosPagamentoProvider>
            <DadosPagamento.AlterarDadosPagamento
              onClose={handleEditarPagamento}
            />
          </DadosPagamento.AlterarDadosPagamentoProvider>
        </DadosPagamento.Match>
      </DadosPagamento.SwitchCase>

      <DadosPagamento.ConditionalRenderer
        condition={DadosPagamento.checkIfAllItemsAreTrue([!editarPagamento])}
      >
        <DadosPagamento.BotoesExpandir
          open={abrirHistorico}
          onClose={handleAbrirHistorico}
          onEdit={handleEditarPagamento}
        />
      </DadosPagamento.ConditionalRenderer>

      <DadosPagamento.ConditionalRenderer condition={abrirHistorico}>
        <DadosPagamento.HistoricoPagamentos
          metodoPagamento={DadosPagamento.tryGetValueOrDefault(
            [dadosCertificado?.[0]?.metodoPagamento],
            '',
          )}
        />
      </DadosPagamento.ConditionalRenderer>
    </>
  );
};

export default DadosPagamentos;
