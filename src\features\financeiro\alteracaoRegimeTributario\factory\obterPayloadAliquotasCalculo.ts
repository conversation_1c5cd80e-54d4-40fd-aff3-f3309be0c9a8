import {
  CONSTS,
  TObterPayloadCalculoResgateOutput,
} from '@src/features/financeiro/alteracaoRegimeTributario/exports';

export const obterPayloadAliquotasCalculo = (
  valorResgate?: number,
): TObterPayloadCalculoResgateOutput => {
  const base = {
    tipoResgate: CONSTS.TIPO_RESGATE.TOTAL,
    detalhesFundos: {},
    valorResgateTotal: valorResgate,
  };

  return {
    regressivo: {
      ...base,
      tipoRegimeTributario: CONSTS.ALIQUOTAS.REGRESSIVA.caractere,
    },
    progressivo: {
      ...base,
      tipoRegimeTributario: CONSTS.ALIQUOTAS.PROGRESSIVA.caractere,
    },
  };
};
