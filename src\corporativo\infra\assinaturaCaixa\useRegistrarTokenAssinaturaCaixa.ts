import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { IRegistroAssinatruaResponse } from '@src/corporativo/types/assinatura/IRegistroAssinatruaResponse';
import { IRegistroAssinaturaPayload } from '@src/corporativo/types/assinatura/IRegistroAssinaturaPayload';

export const useRegistrarTokenAssinaturaCaixa =
  (): IRegistroAssinatruaResponse => {
    const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
      IRegistroAssinaturaPayload,
      IRegistroAssinatruaResponse
    >('PECO_RegistrarAssinaturaCaixa', {
      autoFetch: false,
    });

    return {
      loading,
      invocarApiGatewayCvpComToken,
    };
  };
