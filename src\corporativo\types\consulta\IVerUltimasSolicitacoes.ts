import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IUseVerUltimasSimulacoesBody {
  Cpf: string;
  NumeroCertificado: string;
}

export interface IUseVerUltimasSolicitacoesResponse {
  descBeneficiarioRecebe: string;
  descPeridoBeneficiarioRecebe: string;
  dthDiaSimulacaoFormatada: string;
  dthHoraSimulacaoFormatada: string;
  nomTipoPagamento: string;
  seqSimulacao: string;
  tipoTributacao: string;
  vlrBeneficio: string;
  vlrBeneficioLiquido: string;
  vlrIRRF: string;
  vlrReserva: string;
}

export interface IUseVerUltimasSolicitacoes {
  loading: boolean;
  invocarApiGatewayCvpComToken: () => Promise<
    IHandleReponseResult<IUseVerUltimasSolicitacoesResponse[]> | undefined
  >;
}
