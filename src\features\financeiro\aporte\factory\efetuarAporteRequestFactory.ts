import {
  enumFormaPagamento,
  enumNomeBancos,
  enumNumeroBancos,
  enumTipoClientePep,
  enumTipoFundo,
  getTernaryResult,
  IDadosBancariosAporte,
  IEfetuarAportePayload,
  IFiltroTabelaAporte,
  IFundosAporte,
  tryGetValueOrDefault,
  ultimoDiaDoMes,
} from '@src/features/financeiro/aporte/exports';

interface IEfetuarAporteRequestFactory {
  cpfCnpj: string;
  numeroCertificado: string | undefined;
  fundoSelecionado: IFundosAporte[];
  contaBancariaSelecionada: IDadosBancariosAporte;
  filtroTabelaFundos: IFiltroTabelaAporte;
  isClientePep: boolean;
  origemRecurso: string;
}

export const efetuarAporteRequestFactory = ({
  cpfCnpj,
  numeroCertificado,
  fundoSelecionado,
  contaBancariaSelecionada,
  filtroTabelaFundos,
  isClientePep,
  origemRecurso,
}: IEfetuarAporteRequestFactory): IEfetuarAportePayload => {
  const fundosFactory = (fundo: IFundosAporte[]) => {
    return fundo.map(item => {
      return {
        reservaId: item.reservaId,
        fundoId: item.fundoId,
        valorContribuicao: item.valorContribuicao,
      };
    });
  };
  return {
    cpfCnpj,
    numeroCertificado,
    multiFundo: getTernaryResult(
      Number(fundoSelecionado[0].qntMaxFundoPermitida) !== 0,
      true,
      false,
    ),
    faturaImpressa: false,
    pagamento: {
      descricaoPagamento: filtroTabelaFundos.formaPagamento.toUpperCase(),
      metodoPagamento: getTernaryResult(
        filtroTabelaFundos.formaPagamento?.toUpperCase() ===
          enumFormaPagamento.DEBITO,
        enumFormaPagamento.CB,
        enumFormaPagamento.FC,
      ),
      dataPagamento: getTernaryResult(
        filtroTabelaFundos.dataDebito !== '',
        filtroTabelaFundos.dataDebito,
        ultimoDiaDoMes(new Date()).toDateString(),
      ),
      canalId: contaBancariaSelecionada?.canalId,
      nomeBanco: enumNomeBancos.CAIXA_ECONOMICA,
      numeroBanco: enumNumeroBancos.CAIXA_ECONOMICA,
      numeroConta: contaBancariaSelecionada?.numeroConta,
      digitoAgencia: contaBancariaSelecionada?.digitoAgencia,
      digitoConta: contaBancariaSelecionada?.digitoConta,
      numeroAgencia: contaBancariaSelecionada?.numeroAgencia,
      tipoContaBanco: contaBancariaSelecionada?.digitoAgencia,
    },
    fundos: fundosFactory(fundoSelecionado),
    origem: enumTipoFundo.DEFAULT,
    indPep: tryGetValueOrDefault([isClientePep], false),
    descricaoOrigem: tryGetValueOrDefault([origemRecurso], ''),
    tipoPep: getTernaryResult(
      isClientePep === true,
      enumTipoClientePep.TITULAR,
      enumTipoClientePep.NENUM,
    ),
    invoicePrintInd: true,
  };
};
