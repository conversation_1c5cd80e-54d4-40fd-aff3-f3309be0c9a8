import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Verifica se um objeto é válido para acesso seguro a propriedades.
 *
 * @param {unknown} objeto - O objeto a ser verificado.
 * @returns {boolean} True se o objeto for válido para acesso seguro.
 */
const validarObjeto = (objeto: unknown): boolean => {
  return Resgate.checkIfAllItemsAreTrue([
    !Resgate.isEmptyObject(objeto),
    typeof objeto === 'object',
  ]);
};

/**
 * Acessa uma propriedade de um objeto com verificações de segurança.
 *
 * @param {unknown} objeto - O objeto no qual acessar a propriedade.
 * @param {string} key - A chave da propriedade a ser acessada.
 * @returns {unknown} O valor da propriedade ou undefined se não for possível acessar.
 */
const acessarPropriedadeValida = (objeto: unknown, key: string): unknown => {
  return Resgate.getTernaryResult(
    validarObjeto(objeto),
    (objeto as Record<string, unknown>)?.[key],
    undefined,
  );
};

/**
 * Obtém um valor aninhado dentro de um objeto utilizando um caminho em formato string.
 *
 * @template T - O tipo do objeto de entrada.
 * @template K - O tipo esperado do valor de retorno (opcional).
 * @param {T} objeto - O objeto a ser percorrido.
 * @param {string} caminho - O caminho para o valor, usando notação de ponto (ex: "pessoa.endereco.cidade").
 * @returns {K | undefined} O valor encontrado no caminho especificado ou undefined se o caminho não existir.
 */
const getNestedValue = <T extends Record<string, unknown>, K = unknown>(
  objeto: T,
  caminho: string,
): K | undefined => {
  if (Resgate.isEmptyObject(objeto)) return undefined;

  try {
    const resultado = caminho
      .split('.')
      .reduce(acessarPropriedadeValida, objeto as unknown);

    return resultado as K | undefined;
  } catch (error) {
    return undefined;
  }
};

/**
 * Função utilitária para obter o estado de erro e se o campo foi tocado no Formik,
 * a partir de um caminho específico (flat ou direto).
 *
 * @template T - Tipo genérico dos valores do formulário.
 * @param errors - Objeto de erros retornado pelo Formik.
 * @param touched - Objeto touched retornado pelo Formik.
 * @param caminho - Caminho do campo no formato string (flat ou direto).
 * @returns Um objeto contendo:
 *   - hasError: booleano indicando se existe erro para o caminho informado e o campo foi tocado.
 *   - message: mensagem de erro correspondente ou null se não houver.
 *   - isTouched: boolean indicando se o campo foi tocado.
 */
export const obterErroFormikInput = <T>(
  errors: Resgate.FormikErrors<T> | undefined,
  touched: Resgate.FormikTouched<T> | undefined,
  caminho: string,
): { hasError: boolean; message: string | null; isTouched: boolean } => {
  const mensagemErro = Resgate.getTernaryResult(
    !!errors,
    (errors as Record<string, string>)[caminho],
    null,
  );

  const isTouched = Resgate.getTernaryResult(
    !!touched,
    !!getNestedValue<Record<string, unknown>, boolean>(
      touched as Record<string, unknown>,
      caminho,
    ),
    false,
  );

  return {
    hasError: Resgate.checkIfAllItemsAreTrue([!!mensagemErro, isTouched]),
    message: mensagemErro,
    isTouched,
  };
};
