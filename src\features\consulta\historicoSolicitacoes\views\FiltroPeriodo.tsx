import React from 'react';
import {
  FILTRO_PERIODO,
  PERIODO_VALOR,
  Styles as S,
  TEXTOS,
  TUseFiltroHistoricoSolicitacoes,
  VALORES_PERIODOS,
  ConditionalRenderer,
  Select,
} from '@src/features/consulta/historicoSolicitacoes/exports';

type TFiltroPeriodoProps = {
  controll: TUseFiltroHistoricoSolicitacoes;
};

export const FiltroPeriodo: React.FC<TFiltroPeriodoProps> = ({ controll }) => {
  const {
    filtros,
    selecionarFiltro,
    periodoPersonalizado,
    selecionarPeriodoPersonalizado,
  } = controll;

  return (
    <>
      <S.ItemFiltroContainer>
        <Select
          label={TEXTOS.PERIODO_SOLICITACAO}
          options={FILTRO_PERIODO}
          variant="box-classic"
          size="standard"
          sizeWidth="standard"
          placeholder="Selecione"
          onChange={option => selecionarFiltro(PERIODO_VALOR, option)}
        />
      </S.ItemFiltroContainer>
      <ConditionalRenderer
        condition={
          filtros.periodoValor?.value === VALORES_PERIODOS.PERIODO_PERSONALIZADO
        }
      >
        <S.ItemFiltroContainer>
          <S.InputDate
            label="De:"
            value={periodoPersonalizado.inicio}
            name="inicio"
            onChange={selecionarPeriodoPersonalizado}
            variant="box-classic"
          />
        </S.ItemFiltroContainer>
        <S.ItemFiltroContainer>
          <S.InputDate
            label="Até:"
            value={periodoPersonalizado.fim}
            name="fim"
            onChange={selecionarPeriodoPersonalizado}
            variant="box-classic"
          />
        </S.ItemFiltroContainer>
      </ConditionalRenderer>
    </>
  );
};
