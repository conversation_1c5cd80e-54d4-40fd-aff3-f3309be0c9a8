import * as TransferenciaEntreFundos from '@src/features/financeiro/transferencias/transferenciaEntreFundos/exports';

export type TUseTransferenciaOrigem = {
  selecionaReservaOrigem: (
    fundo: TransferenciaEntreFundos.TTransferenciaFundo,
  ) => void;
  selecionaTipoOrigem: (
    listaTipo: TransferenciaEntreFundos.SelectItem[],
    fundoOrigem: TransferenciaEntreFundos.TTransferenciaFundo,
  ) => void;
  editaTransferenciaValorOrigem: (
    valor: number,
    fundoOrigem: TransferenciaEntreFundos.TTransferenciaFundo,
  ) => void;
  verificaTransferenciaMultifundo: () => boolean;
  reservaAtiva: (
    fundoId: string,
  ) => TransferenciaEntreFundos.TReservasOrigem | undefined;
};

export const useTransferenciaOrigem = (): TUseTransferenciaOrigem => {
  const { reservasOrigem, setReservasOrigem } =
    TransferenciaEntreFundos.useTransferenciaContext();
  const { consultaOrigem } =
    TransferenciaEntreFundos.useTransferenciaServicosContext();

  const reservaAtiva = (fundoId: string) =>
    reservasOrigem.find(reserva => reserva.fundoId === fundoId);

  const onChangeReserva = (
    fundoId: string,
    reserva: Partial<TransferenciaEntreFundos.TReservasOrigem>,
  ) => {
    setReservasOrigem(
      reservasOrigem.map(reservaOrigem => {
        if (reservaOrigem.fundoId === fundoId)
          return {
            ...reservaOrigem,
            ...reserva,
          };

        return reservaOrigem;
      }),
    );
  };

  const selecionaReservaOrigem = (
    fundo: TransferenciaEntreFundos.TTransferenciaFundo,
  ) => {
    if (reservaAtiva(fundo.codFundo)) {
      return setReservasOrigem(
        reservasOrigem.filter(
          reservaItem => reservaItem.fundoId !== fundo.codFundo,
        ),
      );
    }

    const reserva: TransferenciaEntreFundos.TReservasOrigem = {
      fundoId: fundo.codFundo,
      valorSolicitado: 0,
    };

    return setReservasOrigem([...reservasOrigem, reserva]);
  };

  const selecionaTipoOrigem = (
    listaTipo: TransferenciaEntreFundos.SelectItem[],
    fundoOrigem: TransferenciaEntreFundos.TTransferenciaFundo,
  ) => {
    const [tipo] = listaTipo;
    const tipoValue =
      tipo.value ===
      TransferenciaEntreFundos.EDescricaoTiposTransferencia.TransferenciaParcial
        ? TransferenciaEntreFundos.ETiposTransferencia.Parcial
        : TransferenciaEntreFundos.ETiposTransferencia.Total;

    onChangeReserva(fundoOrigem.codFundo, {
      staTipRetirada: tipoValue,
      valorSolicitado:
        tipoValue === TransferenciaEntreFundos.ETiposTransferencia.Total
          ? Number(fundoOrigem.vlrSaldo)
          : 0,
    });
  };

  const editaTransferenciaValorOrigem = (
    valor: number,
    fundoOrigem: TransferenciaEntreFundos.TTransferenciaFundo,
  ) =>
    onChangeReserva(fundoOrigem.codFundo, {
      valorSolicitado: valor,
    });

  const verificaTransferenciaMultifundo = () => {
    const quantidadeTransferncia =
      TransferenciaEntreFundos.tryGetValueOrDefault(
        [Number(consultaOrigem.response?.retornoCertificado?.qtdMaximaFundos)],
        1,
      );

    return quantidadeTransferncia > 1;
  };

  return {
    selecionaReservaOrigem,
    selecionaTipoOrigem,
    editaTransferenciaValorOrigem,
    verificaTransferenciaMultifundo,
    reservaAtiva,
  };
};
