/**
 * Helper específico para React Fast Refresh
 * Melhora a experiência de HMR com componentes React
 */
import React from 'react';

declare global {
  interface Window {
    $RefreshReg$?: (type: any, id: string) => void;
    $RefreshSig$?: () => (type: any) => any;
  }
}

/**
 * Registra um componente para React Fast Refresh
 */
export function registerComponent(
  Component: React.ComponentType<any>,
  id: string,
) {
  if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
    if (window.$RefreshReg$) {
      window.$RefreshReg$(Component, id);
    }
  }
}

/**
 * Cria uma assinatura para React Fast Refresh
 */
export function createSignature() {
  if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
    if (window.$RefreshSig$) {
      return window.$RefreshSig$();
    }
  }
  return (type: any) => type;
}

/**
 * Wrapper que combina HMR tradicional com React Fast Refresh
 */
export function withFastRefresh<T extends React.ComponentType<any>>(
  Component: T,
  id: string,
): T {
  if (process.env.NODE_ENV !== 'development') {
    return Component;
  }

  // Registra o componente para React Fast Refresh
  registerComponent(Component, id);

  // Cria assinatura para preservar estado
  const signature = createSignature();
  if (signature) {
    signature(Component);
  }

  // Aceita atualizações HMR
  if (module.hot) {
    module.hot.accept();
  }

  return Component;
}

/**
 * Hook que força re-render quando há mudanças no código
 */
export function useFastRefresh() {
  const [, forceUpdate] = React.useReducer((x: number) => x + 1, 0);

  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development' && module.hot) {
      const handleUpdate = () => {
        console.log('🚀 Fast Refresh: Atualizando componente');
        forceUpdate();
      };

      module.hot.dispose(handleUpdate);
    }
  }, [forceUpdate]);

  return forceUpdate;
}

export default {
  registerComponent,
  createSignature,
  withFastRefresh,
  useFastRefresh,
};
