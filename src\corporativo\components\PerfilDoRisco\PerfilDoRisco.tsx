import {
  LISTA_DE_PERFIS,
  Match,
  PerfilDoRiscoTriangulo,
  React,
  S,
  SwitchCase,
  TTiposDePerfis,
} from '@src/features/financeiro/aporte/exports';

type TPerfilDoRiscoProps = { perfil: TTiposDePerfis };

const PerfilDoRisco: React.FC<TPerfilDoRiscoProps> = ({ perfil }) => {
  return (
    <S.PerfilDoRiscoContainer>
      {LISTA_DE_PERFIS.map(itemPerfil => (
        <S.PerfilDoRiscoItem key={itemPerfil.perfil}>
          <S.PerfilDoRiscoBox
            style={{
              background: itemPerfil.color,
            }}
          />
          <SwitchCase fallback={undefined}>
            <Match when={perfil === itemPerfil.perfil}>
              <S.PerfilDoRiscoTrianguloContainer>
                <PerfilDoRiscoTriangulo color={itemPerfil.color} />
                <S.PerfilDoRiscoDescricao
                  variant="text-small-400"
                  fontColor="content-neutral-04"
                >
                  {itemPerfil.perfil}
                </S.PerfilDoRiscoDescricao>
              </S.PerfilDoRiscoTrianguloContainer>
            </Match>
          </SwitchCase>
        </S.PerfilDoRiscoItem>
      ))}
    </S.PerfilDoRiscoContainer>
  );
};

export default React.memo(PerfilDoRisco);
