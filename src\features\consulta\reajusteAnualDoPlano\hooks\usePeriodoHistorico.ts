import {
  Infra,
  Types,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';
import { useState } from 'react';

export const usePeriodoHistorico = (): Types.TUsePeriodoHistorico => {
  const { obterHistoricoPorAno, response, loading } =
    Infra.useObterHistoricoPorAno();
  const [anoAtivo, setAnoAtivo] = useState('');

  const alterarAnoAtivo = (ano: string) => {
    setAnoAtivo(ano);

    obterHistoricoPorAno(ano);
  };

  const voltarEtapa = () => {
    setAnoAtivo('');
  };

  return {
    response,
    loading,
    anoAtivo,
    alterarAnoAtivo,
    voltarEtapa,
  };
};
