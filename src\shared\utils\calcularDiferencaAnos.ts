/**
 * Retorna a diferença entre duas datas em ano.
 *
 * @example
 * // Exemplo de uso com uma data incompleta
 * const duracao = calcularDiferencaAnos({dataInicio: Thu Jul 30 2020 00:00:00 GMT-0300,
 *   dataFim: Thu Jul 29 2049 00:00:00 GMT-0300,
 * });
 * console.log(duracao); // '28'
 */

export interface ICalcularDiferencaAnos {
  dataInicio: Date;
  dataFim: Date;
}

export function calcularDiferencaAnos({
  dataInicio,
  dataFim,
}: ICalcularDiferencaAnos): number {
  let diferencaAnos = dataFim.getFullYear() - dataInicio.getFullYear();

  const mesFimAnteriorAoInicio = dataFim.getMonth() < dataInicio.getMonth();
  const mesmoMesMasDiaAnterior =
    dataFim.getMonth() === dataInicio.getMonth() &&
    dataFim.getDate() < dataInicio.getDate();

  if (mesFimAnteriorAoInicio || mesmoMesMasDiaAnterior) {
    diferencaAnos -= 1;
  }

  return diferencaAnos;
}
