import {
  IListarFundosParaResgateAliquota,
  IObterDadosPorAliquotaRetorno,
} from '@src/features/financeiro/resgate/exports';

export interface ITabelasEscolhaAliquotasProps {
  aliquota: IListarFundosParaResgateAliquota;
  obterDadosPorAliquota: (
    tipoAliquota?: string,
  ) => IObterDadosPorAliquotaRetorno;
  validarAliquotaDesabilitada: (codigoAliquota: string) => boolean;
  selecionarOpcaoAliquota: (opcaoSelecionada: string) => void;
}
