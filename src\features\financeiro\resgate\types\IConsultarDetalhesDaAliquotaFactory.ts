import * as React from '@src/features/financeiro/resgate/exports';

export interface IConsultarDetalhesDaAliquotaFactory {
  formik: React.FormikProps<React.IFormikValuesSimulacaoResgate>;
  aliquotaAtual: string;
  resultadoCalculoResgateRestante: React.ICalcularValorResgateRestanteRetorno;
  calcularResgate: (
    dynamicPayload?: React.IObterPayloadCalcularResgateFactoryRetorno,
  ) => Promise<
    React.IHandleReponseResult<React.ICalcularResgateResponse> | undefined
  >;
  consultarResumoAliquota: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | React.IHandleReponseResult<React.IConsultarResumoAliquotaResponse>
    | undefined
  >;
  consultarDetalheCalculo: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | React.IHandleReponseResult<React.IConsultarDetalheCalculoResponse>
    | undefined
  >;
}

export interface IConsultarDetalhesDaAliquotaFactoryRetorno {
  calculo: React.ICalcularResgateDadosEncargo | null | undefined;
  resumo: React.IConsultarResumoAliquotaResponse | null | undefined;
  detalhado: React.IConsultarDetalheCalculoResponse | null | undefined;
}
