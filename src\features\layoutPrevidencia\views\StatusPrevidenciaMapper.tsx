import { Text } from '@cvp/design-system-caixa';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { tryGetValueOrDefault } from '@cvp/utils';
import { DescricaoStatusPrevidencia } from '../exports';
import { marginStatus } from '../constants/DescricaoStatusPrevidencia';
import { FlagStatus } from '../constants/flagStatus';

type TStatusCertificadoProps = {
  status: string;
};

const getStatusIcon = (status: string): React.JSX.Element => {
  const marginRightStyle = { marginRight: marginStatus };

  const iconePadrao = (
    <CheckCircleOutlineIcon color="success" style={marginRightStyle} />
  );
  switch (status) {
    case FlagStatus.EmBeneficio:
      return iconePadrao;
    case FlagStatus.Cancelado:
    case FlagStatus.Desligado:
    case FlagStatus.Encerrado:
      return <HighlightOffIcon color="error" style={marginRightStyle} />;
    case FlagStatus.Pendente:
    case FlagStatus.AtivoCobrancaSuspensa:
    case FlagStatus.Sinistro:
    case FlagStatus.EmTransicao:
      return <ErrorOutlineIcon color="warning" style={marginRightStyle} />;
    default:
      return iconePadrao;
  }
};

const validateStatus = (status: string) => {
  const statusMap: { [key in string]?: string } = {
    [FlagStatus.EmBeneficio]: DescricaoStatusPrevidencia.emBeneficio,
    [FlagStatus.Cancelado]: DescricaoStatusPrevidencia.cancelado,
    [FlagStatus.Desligado]: DescricaoStatusPrevidencia.desligado,
    [FlagStatus.Encerrado]: DescricaoStatusPrevidencia.encerrado,
    [FlagStatus.Pendente]: DescricaoStatusPrevidencia.pendente,
    [FlagStatus.AtivoCobrancaSuspensa]:
      DescricaoStatusPrevidencia.ativoCobrancaSuspensa,
    [FlagStatus.Sinistro]: DescricaoStatusPrevidencia.sinistro,
    [FlagStatus.EmTransicao]: DescricaoStatusPrevidencia.emTransicao,
  };

  return tryGetValueOrDefault(
    [statusMap[status]],
    DescricaoStatusPrevidencia.ativo,
  );
};

const StatusPrevidenciaMapper: React.FC<TStatusCertificadoProps> = ({
  status,
}) => {
  const descricaoStatus = validateStatus(status);
  const icon = getStatusIcon(status);

  return (
    <div style={{ display: 'flex', alignItems: 'center', width: 300 }}>
      {icon}
      <Text variant="text-standard-600">{descricaoStatus}</Text>
    </div>
  );
};

export default StatusPrevidenciaMapper;
