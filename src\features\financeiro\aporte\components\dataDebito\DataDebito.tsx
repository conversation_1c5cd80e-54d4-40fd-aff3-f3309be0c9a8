import {
  BOTOES,
  Text,
  FILTRO_APORTE,
  FORMA_PAGAMENTO,
  GridItem,
  IFiltroTabelaAporte,
  Match,
  TFormikProps,
  useDataDebito,
  Select,
  IShowInputs,
  FORMIK_VALUES,
} from '@src/features/financeiro/aporte/exports';

interface IDataDebito {
  formik: TFormikProps<IFiltroTabelaAporte>;
  showInputs: IShowInputs;
}

const DataDebito: React.FC<IDataDebito> = ({ formik, showInputs }) => {
  const { datasDeDebitoPossiveis, datasDeDebitoPossiveisLoading } =
    useDataDebito();

  return (
    <Match when={showInputs.formaPagamento === FORMA_PAGAMENTO.debito}>
      <GridItem xs="1 / 4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {FILTRO_APORTE.dataDebito}
        </Text>
        <Select
          id="dataDebito"
          onChange={event =>
            formik.setFieldValue(FORMIK_VALUES.DATA_DEBITO, event[0].value)
          }
          options={datasDeDebitoPossiveis.map(data => ({
            text: new Date(data).getDate().toString(),
            value: data,
          }))}
          placeholder={BOTOES.select}
          size="standard"
          sizeWidth="large"
          variant="box-classic"
          disabled={datasDeDebitoPossiveisLoading}
        />
      </GridItem>
    </Match>
  );
};

export default DataDebito;
