import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, getTernaryResult } from '@cvp/utils';
import { CONSENTIMENTO_CONSTANTES } from '@src/corporativo/constants/dadosParticipante/consentimento';
import { IConsultarConsentimentoResponse } from '@src/corporativo/types/dadosParticipante/consultar/IConsultarConsentimentoResponse';

export interface IAtualizarConsentimentoPayload {
  cpf: string;
  sistema: {
    codigo: string;
  };
  unidadeNegocio: {
    codigo: string;
  };
  processoNegocio: {
    subProcesso: {
      codigo: string;
    };
    macroProcesso: {
      codigo: string;
    };
  };
  evidencia: {
    codigo: string;
  };
  consentimento: {
    tipo: string;
    numConsentimentoNegocio: string;
  };
  cliente: {
    codigo: string;
  };
}

export const useAtualizarConsentimento = () => {
  const cpf = String(getSessionItem('cpfCnpj'));

  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    Partial<IAtualizarConsentimentoPayload>,
    IConsultarConsentimentoResponse
  >('PECO_AtualizarConsentimento', {
    data: {
      cpf,
      sistema: {
        codigo: String(CONSENTIMENTO_CONSTANTES.CODIGO_CONSENTIMENTO_SISTEMA),
      },
      unidadeNegocio: {
        codigo: String(
          CONSENTIMENTO_CONSTANTES.CODIGO_CONSENTIMENTO_UNIDADE_NEGOCIO,
        ),
      },
      evidencia: {
        codigo: [
          `sistema: ${CONSENTIMENTO_CONSTANTES.CODIGO_CONSENTIMENTO_SISTEMA}, cpf:`,
          cpf,
        ].join(' '),
      },

      cliente: {
        codigo: cpf,
      },
    },
    autoFetch: false,
  });

  return {
    invocarApiGatewayCvpComToken,
    loading,
  };
};

export function mountPayloadAtualizarConsentimento(
  consentimento: boolean,
  subProcesso: number,
) {
  return {
    consentimento: {
      tipo: String(
        getTernaryResult(
          consentimento,
          CONSENTIMENTO_CONSTANTES.TIPO_CONSENTIMENTO_1,
          CONSENTIMENTO_CONSTANTES.TIPO_CONSENTIMENTO_0,
        ),
      ),
      numConsentimentoNegocio:
        CONSENTIMENTO_CONSTANTES.NUM_CONSENTIMENTO_NEGOCIO,
    },
    processoNegocio: {
      subProcesso: {
        codigo: String(subProcesso),
      },
      macroProcesso: {
        codigo: String(CONSENTIMENTO_CONSTANTES.MACRO_PROCESSO),
      },
    },
  };
}
