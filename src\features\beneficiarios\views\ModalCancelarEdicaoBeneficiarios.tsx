import {
  Button,
  Dialog,
  Text,
  TModalCancelarEdicaoBeneficiariosProps,
} from '../exports';

export const ModalCancelarEdicaoBeneficiarios: React.FC<
  TModalCancelarEdicaoBeneficiariosProps
> = ({ handleProceed, handleReset, open }) => {
  return (
    <Dialog open={open}>
      <Dialog.Content>
        <Dialog.Header variant="highlight">
          <Text variant="heading-small-600">Editar beneficiários</Text>
        </Dialog.Header>
        <Dialog.Body>
          <Text variant="text-big-400">
            Deseja sair dessa tela? Para salvar as informações editadas,
            preencha a autenticação e clique em &quot;Voltar e Salvar&quot;.
          </Text>
        </Dialog.Body>
        <Dialog.Footer>
          <Button onClick={handleProceed}>Sair e não salvar</Button>

          <Button onClick={handleReset}>Voltar e salvar</Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};
