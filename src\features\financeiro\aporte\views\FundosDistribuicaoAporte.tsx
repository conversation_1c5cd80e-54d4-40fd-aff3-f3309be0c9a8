import {
  BOTOES,
  Button,
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  EEtapasAporte,
  Grid,
  GridItem,
  LOADING,
  LoadingSpinner,
  Match,
  SomaEValorRestanteContribuicao,
  TabelaFundosDistribuicaoExistentes,
  TabelaFundosDistribuicaoNovos,
  useFundosDistribuicao,
} from '@src/features/financeiro/aporte/exports';

interface IFundosDistribuicaoAporte {
  loadingFundos: boolean;
  loadingExtratoUnificado: boolean;
}

const FundosDistribuicaoAporte: React.FC<IFundosDistribuicaoAporte> = ({
  loadingFundos,
  loadingExtratoUnificado,
}) => {
  const {
    fundosDistribuicaoExistentesFiltrado,
    fundosDistribuicaoNovosFiltrado,
    valorContribuicaoRestante,
    somaDistribuicaoEntreFundos,
    disableButton,
    etapa,
    setEtapa,
  } = useFundosDistribuicao();

  return (
    <Match when={etapa === EEtapasAporte.FundosDistribuicaoAporte}>
      <Match
        when={checkIfSomeItemsAreTrue([loadingFundos, loadingExtratoUnificado])}
      >
        <LoadingSpinner size="medium">{LOADING.TABELA_FUNDOS}</LoadingSpinner>
      </Match>

      <Match
        when={checkIfAllItemsAreTrue([
          !loadingFundos,
          !loadingExtratoUnificado,
          !!fundosDistribuicaoExistentesFiltrado,
          !!fundosDistribuicaoNovosFiltrado,
        ])}
      >
        <Grid>
          <GridItem xs="1" margin="0px 22px">
            <TabelaFundosDistribuicaoExistentes
              renderFundos={fundosDistribuicaoExistentesFiltrado}
            />
          </GridItem>
          <GridItem xs="1" margin="0px 22px">
            <TabelaFundosDistribuicaoNovos
              renderFundos={fundosDistribuicaoNovosFiltrado}
            />
          </GridItem>
        </Grid>
        <SomaEValorRestanteContribuicao
          valorContribuicao={valorContribuicaoRestante}
          somaValorContribuicao={somaDistribuicaoEntreFundos}
        />
        <Grid justify="flex-end" margin="0px 7px">
          <GridItem>
            <Button
              onClick={() => setEtapa(EEtapasAporte.ModalVerificarCamposAporte)}
              disabled={disableButton}
              size="standard"
              variant="secondary"
            >
              {BOTOES.prosseguir}
            </Button>
          </GridItem>
        </Grid>
      </Match>
    </Match>
  );
};

export default FundosDistribuicaoAporte;
