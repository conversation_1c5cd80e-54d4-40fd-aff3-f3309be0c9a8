import * as AtivacaoSuspensao from '../exports';

export interface IDadosCertificado {
  campo: string;
  valor: string;
}

export const dadosTabelaCertificadoFactory = (
  dadosCertificado?: AtivacaoSuspensao.IRecuperarContribuicoesCertificadoResponse | null,
): IDadosCertificado[] => {
  if (!dadosCertificado) {
    return [];
  }

  return [
    {
      campo: 'CPF:',
      valor: AtivacaoSuspensao.tryGetValueOrDefault(
        [dadosCertificado.cpfPessoaCertificado],
        '-',
      ),
    },
    {
      campo: 'Produto:',
      valor: AtivacaoSuspensao.tryGetValueOrDefault(
        [dadosCertificado.descricaoProduto],
        '-',
      ),
    },
    {
      campo: 'Certificado:',
      valor: AtivacaoSuspensao.tryGetValueOrDefault(
        [dadosCertificado.contaId],
        '-',
      ),
    },
  ];
};
