import {
  ContaBancariaSelecionadaAporte,
  FiltroSelecionadoTabelaFundos,
  IDadosTabelaFundos,
  IFiltroTabelaAporte,
  useDadosComprovanteAporte,
} from '@src/features/financeiro/aporte/exports';

interface IComprovanteAporteDadosAporte {
  dadosAporte: IFiltroTabelaAporte;
  fundosExistentes: IDadosTabelaFundos;
  fundosNovos: IDadosTabelaFundos;
}

const DadosComprovanteAporte: React.FC<IComprovanteAporteDadosAporte> = ({
  dadosAporte,
  fundosExistentes,
  fundosNovos,
}) => {
  const {
    filtroTabelaFundos,
    contaBancariaSelecionada,
    perfilInvestidorConcat,
  } = useDadosComprovanteAporte(fundosExistentes, fundosNovos);

  return (
    <>
      <FiltroSelecionadoTabelaFundos
        dadosAporte={dadosAporte}
        perfilInvestidorConcat={perfilInvestidorConcat}
      />
      <ContaBancariaSelecionadaAporte
        dadosAporte={dadosAporte}
        filtroTabelaFundos={filtroTabelaFundos}
        contaBancariaSelecionada={contaBancariaSelecionada}
      />
    </>
  );
};

export default DadosComprovanteAporte;
