export const ALERTS = {
  AVISO_ALTERACAO:
    'Importante! Os dados de e-mail e telefone serão utilizados para autenticação da proposta. Atualize-os antes de iniciar a venda.',
  ALTERACAO_SUCESSO: 'A alteração de aliquota foi realizada com sucesso. ',
  ALTERACAO_FALHA: 'A falha na alteração de aliquota. ',
  CONFIRMACAO_REGIME_TRIBUTACAO:
    'O cliente confirma a opção pelo regime de tributação de alíquotas selecionada, previsto na IN RFB Nº 2.209 de 06 de agosto de 2024, para o certificado indicado, ciente de que esta opção é irretratável.',
  AVISO_ALTERACAO_IRRETRATAVEL:
    'Informamos que esse certificado já teve o regime tributário alterado anteriormente. Por esse motivo, não é possível realizar uma nova alteração.',
};

export const TITULOS = {
  DEDUCOES_APLICAVEIS:
    'Confira as deduções aplicáveis para cada regime tributário e selecione uma opção para prosseguir.',
  ATENCAO: 'Atenção',
  LOADING: 'Carregando...',
  NO_DATA_REGIME_TRIBUTARIO:
    'Não há opção de alíquota para o atual certificado.',
  AUTENTICACAO: 'Autenticação',
};

export const ALIQUOTAS = {
  REGRESSIVA: {
    id: 'regressiva',
    text: 'Alíquota Regressiva',
    caractere: 'R',
    opcaoTributacaoIrrf: 'TR',
  },
  PROGRESSIVA: {
    id: 'progressiva',
    text: 'Alíquota Progressiva',
    caractere: 'P',
    opcaoTributacaoIrrf: 'TP',
  },
};

export const TIPO_RESGATE = {
  TOTAL: 'T',
};

export const BOTOES = {
  PROSSEGUIR: 'Prosseguir',
  VOLTAR: 'Voltar',
  CONFIRMAR: 'Confirmar',
  FINALIZAR_ATENDIMENTO: 'Finalizar atendimento',
  GERAR_COMPROVANTE: 'Gerar comprovante',
};

export const MENSAGENS_MODAIS = {
  ALTERACAO_ALIQUOTA:
    'A alíquota escolhida não poderá mais ser alterada, sendo assim, definitiva. Tem certeza da escolha do modelo tributário?',
  ALTERACAO_ALIQUOTA_IMPEDIMENTO: {
    ALTERADO_ANTERIORMENTE:
      'Informamos que esse certificado já teve o regime tributário alterado anteriormente. Por esse motivo, não é possível realizar uma nova alteração.',
    ALTERADO_PORTABILIDADE:
      'Informamos que este plano é oriundo de uma portabilidade e teve o regime tributário alterado no momento em que a portabilidade foi realizada. Por esse motivo, não é possível realizar uma nova alteração de regime tributário.',
    ALTERACAO_NAO_RECOMENDADA:
      'Alteração de regime tributário não recomendada Identificamos que a mudança de regime resultaria em aumento na alíquota e, consequentemente, maior carga tributária.Recomendamos manter o regime atual, pois a alteração traria prejuízo financeiro.',
  },
};

export const REGIME_TRIBUTARIO = {
  PODE_ALTERAR: 'S',
  NAO_PODE_ALTERAR: 'N',
};

export const ASSINATURA_RESPONSE = 'assinaturaResponse';

export const listaAliquotas = [ALIQUOTAS.PROGRESSIVA, ALIQUOTAS.REGRESSIVA];

export const REGIME_TRIBUTARIO_CONTEXT =
  'useAlteracaoRegimeTributarioContext deve ser usado dentro de um AlteracaoRegimeTributarioProvider';

export const MENSAGEM_TOOLTIPS = {
  regressiva: `No caso de resgate, as alíquotas diminuem 5% a cada dois anos, conforme tabela abaixo. Quanto maior o tempo de permanência, menor será o Imposto de Renda . Indicado para quem pretende manter os recursos no plano por um prazo maior. No pagamento da renda a alíquota de IR é calculada com base no tempo médio ponderado de aportes de recursos no plano e tempo de pagamento de benefícios. A partir do início de recebimento da renda o prazo de acumulação continua sendo contabilizado, assim, caso haja uma mudança de faixa da Tabela Decrescente, a alíquota de tributação também muda automaticamente.
  Prazo de acumulação	Alíquotas Regressivas
  Até 2 anos	35%
  De 2 a 4 anos	30%
  De 4 a 6 anos	25%
  De 6 a 8 anos	20%
  De 8 a 10 anos	15%
  Acima de 10 anos	10%
`,
  progressiva: `Declaro minha opção pelo REGIME DE TRIBUTAÇÃO PROGRESSIVO, no qual haverá a incidência de Imposto de Renda à alíquota de 15% em casos de resgates, sujeito a ajuste na declaração de IR e aplicação da tabela de imposto de renda vigente [www.receita.fazenda.gov.br], no pagamento de benefícios. Estou ciente que poderei optar pelo REGIME DE TRIBUTAÇÃO REGRESSIVA, de forma irretratável, até a data da obtenção do benefício ou requisição do primeiro resgate.`,
};
