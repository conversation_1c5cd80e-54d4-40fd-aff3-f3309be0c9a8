import { TTextFieldProps } from '@cvp/design-system-caixa/dist/molecules/TextField';
import * as UTILS from '@cvp/utils';
import { FormikErrors, FormikProps, FormikTouched } from 'formik';

/**
 * <PERSON><PERSON> as propriedades de validação para o componente TextField do pacote @cvp/design-system-caixa.
 *
 * Esta função é utilizada para criar as propriedades necessárias para exibir
 * mensagens de erro em um campo de formulário integrado com Formik.
 *
 * @template T - O tipo genérico que representa os dados do formulário.
 *
 * @param {FormikProps<T>} formik - As propriedades do Formik associadas ao formulário.
 * @param {keyof FormikTouched<T> | keyof FormikErrors<T>} nomePropriedade - O nome da propriedade do campo que será validado.
 *
 * @returns {Pick<TTextFieldProps, 'error'>} - Retorna um objeto contendo a propriedade `error` com um booleano indicando se há erro e uma mensagem de erro.
 *
 * @example
 * // Exemplo de uso
 * const propsDeValidacao = propriedadesDeValidacaoFactory(formik, 'nome');
 * <TextField {...propsDeValidacao} />
 */
export const propriedadesDeValidacaoFactory = <T>(
  formik: FormikProps<T>,
  nomePropriedade: keyof FormikTouched<T> | keyof FormikErrors<T>,
): Pick<TTextFieldProps, 'error'> => {
  const usuarioErrou = UTILS.checkIfAllItemsAreTrue([
    !!formik.touched[nomePropriedade],
    !!formik.errors[nomePropriedade],
  ]);

  const usuarioNaoViu = UTILS.checkIfAllItemsAreTrue([
    !!formik.errors[nomePropriedade],
    formik.submitCount > 0,
  ]);

  const exibirErro = UTILS.checkIfSomeItemsAreTrue([
    usuarioErrou,
    usuarioNaoViu,
  ]);

  return {
    error: {
      error: exibirErro,
      message: String(formik.errors[nomePropriedade]),
    },
  };
};
