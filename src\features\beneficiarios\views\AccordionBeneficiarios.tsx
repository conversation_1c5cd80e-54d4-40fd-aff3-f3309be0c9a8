import {
  AlertaPrevidencia,
  AssinaturaBeneficiarios,
  Cobertura,
  For,
  Form,
  Formik,
  ICertificadoCoberturas,
  MultiAccordion,
  TFormBeneficiarios,
  useAccordionBeneficiarios,
} from '../exports';

export interface IAcordionBeneficiariosProps {
  beneficiarios: ICertificadoCoberturas;
}

export const AcordionBeneficiarios: React.FC<IAcordionBeneficiariosProps> = ({
  beneficiarios,
}) => {
  const { submitBeneficiarios } = useAccordionBeneficiarios();
  const formikValues = beneficiarios.beneficios?.reduce((acc, beneficio) => {
    return {
      ...acc,
      [beneficio.coberturaId]: {
        beneficiarios: (beneficio.beneficiarios ?? []).map(beneficiario => {
          return {
            nomeBeneficiario: beneficiario.pessoaFisica.nome,
            porcentagem: beneficiario.percentualDistribuicao.split('.')[0],
            dataNascimento: beneficiario.pessoaFisica.dataNascimento,
            numCpf: beneficiario.pessoaFisica.cpfCnpj,
            sexo: beneficiario.pessoaFisica.genero,
            idBeneficiario: beneficiario.pessoaFisica.pessoaFisicaId,
            idParentesco: beneficiario.grauParentescoId,
            estado: 'existente',
          };
        }),
        planoId: beneficio.planoId,
        editando: false,
      },
    };
  }, {} as TFormBeneficiarios);

  if (!formikValues) return null;

  return (
    <>
      <AlertaPrevidencia identificador="beneficiarios" />
      <Formik initialValues={formikValues} onSubmit={submitBeneficiarios}>
        <div data-testId="formulario-beneficiarios">
          <Form>
            <MultiAccordion.Root type="multiple">
              <For each={beneficiarios?.beneficios ?? []}>
                {cobertura => (
                  <Cobertura
                    key={cobertura.coberturaId}
                    nomTipoPagamento={cobertura.nomTipoPagamento}
                    valorBeneficio={cobertura.valorBeneficio}
                    coberturaId={cobertura.coberturaId}
                  />
                )}
              </For>
            </MultiAccordion.Root>
          </Form>
          <AssinaturaBeneficiarios />
        </div>
      </Formik>
    </>
  );
};
