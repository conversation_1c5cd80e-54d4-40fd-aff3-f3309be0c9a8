import { TTiposDePerfis } from '@src/features/financeiro/aporte/exports';

export interface IDadosTabelaFundos {
  dadosFundos: IFundosAporte[];
  qntMaxFundoPermitida?: string;
  qntFundoDisponivel?: string;
}

export interface IFundosAporte {
  fundoId: string;
  reservaId: string;
  descricaoFundo: string;
  descPerfilFundo: TTiposDePerfis;
  rentUlt12Meses: string;
  taxaAdministração: string;
  saldo: string;
  valorContribuicao: string;
  tipoFundo: string | undefined;
  valorMinimo: string;
  qntMaxFundoPermitida: string;
}
