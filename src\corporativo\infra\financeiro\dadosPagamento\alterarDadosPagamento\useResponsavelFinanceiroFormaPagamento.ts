import { useContext } from 'react';

import { IResponseResponsavelFinanceiro } from '@src/features/financeiro/dadosPagamento/types/DadosPagamento';
import * as REQUEST_TYPES from '@src/features/financeiro/dadosPagamento/types/AlteracaoFormaDadosPagamentoRequest';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { getSessionItem } from '@cvp/utils';

export const useResponsavelFinanceiroFormaPagamento = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj') ?? '';

  const {
    response: dadosResponsavelFinanceiro,
    loading: loadingDadosResponsavelFinanceiro,
    invocarApiGatewayCvpComToken: obterResponsavelFinanceiro,
  } = useApiGatewayCvpInvoker<
    REQUEST_TYPES.IRequestResponsavelFinanceiro,
    IResponseResponsavelFinanceiro
  >(PECOS.ResponsavelFinanceiro, {
    autoFetch: true,
    data: {
      cpfCnpj: cpfCnpjSession,
      numeroCertificado: certificadoAtivo?.certificadoNumero,
    },
  });

  return {
    dadosResponsavelFinanceiro: dadosResponsavelFinanceiro?.entidade,
    loadingDadosResponsavelFinanceiro,
    obterResponsavelFinanceiro,
  };
};
