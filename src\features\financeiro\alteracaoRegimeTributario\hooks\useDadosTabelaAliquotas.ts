import { useObterTabelasAliquotas } from '@src/features/financeiro/alteracaoRegimeTributario/hooks/useObterTabelasAliquotas';
import {
  ITabelaAliquotas,
  ordenaValor,
  useAlteracaoRegimeTributarioContext,
} from '../exports';

type TUseObterDadosTabelasAliquotas = {
  loading: boolean;
  obterDadosTabelaAliquotas: () => Promise<void>;
};

export const useObterDadosTabelasAliquotas =
  (): TUseObterDadosTabelasAliquotas => {
    const { setDadosTabelaAliquotas } = useAlteracaoRegimeTributarioContext();

    const { obterTabelasAliquotas, loading } = useObterTabelasAliquotas();

    const obterDadosTabelaAliquotas = async (): Promise<void> => {
      const tabelasAliquotas = await obterTabelasAliquotas();

      const ordenarValoresLista = <T>(
        lista: Partial<T>[],
        ordenador: string,
      ): Partial<T>[] => {
        return lista.sort((a, b) => -ordenaValor(ordenador)(a, b));
      };

      const aliquotasRegressivas: Partial<ITabelaAliquotas>[] =
        ordenarValoresLista(tabelasAliquotas.regressivo, 'aliquota');

      const aliquotasProgressivas: Partial<ITabelaAliquotas>[] =
        ordenarValoresLista(tabelasAliquotas.progressivo, 'aliquota');

      setDadosTabelaAliquotas({ aliquotasRegressivas, aliquotasProgressivas });
    };

    return {
      obterDadosTabelaAliquotas,
      loading,
    };
  };
