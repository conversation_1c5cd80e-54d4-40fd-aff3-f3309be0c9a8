import {
  BeneficiariosProvider,
  ContainerCoberturas,
  Grid,
  LoadingSpinner,
  useObterBeneficiarios,
} from '../exports';

const Beneficiarios: React.FC = () => {
  const { response, loading } = useObterBeneficiarios();

  if (loading) {
    return (
      <Grid margin="32px 0" justify="center" alignitem="center">
        <LoadingSpinner size="large">Carregando...</LoadingSpinner>
      </Grid>
    );
  }

  return (
    <div style={{ padding: '32px 20px', width: '100%' }}>
      <BeneficiariosProvider beneficios={response}>
        <ContainerCoberturas />
      </BeneficiariosProvider>
    </div>
  );
};

export default Beneficiarios;
