import { useContext } from 'react';
import * as REQUEST_TYPES from '@src/features/financeiro/dadosPagamento/types/AlteracaoFormaDadosPagamentoRequest';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { getSessionItem } from '@cvp/utils';
import { IResponseListaTiposContasBancarias } from '@src/features/financeiro/dadosPagamento/exports';

export const useListarContasBancarias = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj') ?? '';

  const {
    response: dadosListaContasBancarias,
    loading: loadingDadosListaContasBancarias,
    invocarApiGatewayCvpComToken: obterListaContasBancarias,
  } = useApiGatewayCvpInvoker<
    REQUEST_TYPES.IRequestListarContasBancarias,
    IResponseListaTiposContasBancarias[]
  >(PECOS.ListarContasBancarias, {
    data: {
      cpfCnpj: cpfCnpjSession,
      numeroCertificado: certificadoAtivo?.certificadoNumero,
    },
    autoFetch: true,
  });

  return {
    dadosListaContasBancarias: dadosListaContasBancarias?.entidade,
    loadingDadosListaContasBancarias,
    obterListaContasBancarias,
  };
};
