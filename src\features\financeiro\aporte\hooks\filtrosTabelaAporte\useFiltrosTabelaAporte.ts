import {
  checkIfAllItemsAreTrue,
  FORMA_PAGAMENTO,
  IFiltroTabelaAporte,
  IShowInputs,
  SHOW_INPUTS_INITIAL_STATE,
  TFormikProps,
  tryGetValueOrDefault,
  useAporteContext,
  useAporteServiceContext,
  useEffect,
  useState,
} from '@src/features/financeiro/aporte/exports';

type TUseFiltrosTabelaAporte = {
  formik: TFormikProps<IFiltroTabelaAporte>;
  showInputs: IShowInputs;
  filtroTabelaFundos: IFiltroTabelaAporte;
  loadingDatasDebito: boolean;
};

export const useFiltrosTabelaAporte = (): TUseFiltrosTabelaAporte => {
  const {
    filtroTabelaFundos,
    formikFiltrosTabelaAporte,
    setFiltroTabelaAporte,
  } = useAporteContext();

  const { datasDebito } = useAporteServiceContext();

  const [showInputs, setShowInputs] = useState<IShowInputs>(
    SHOW_INPUTS_INITIAL_STATE,
  );

  useEffect(() => {
    const dataPagamentoBoleto = datasDebito?.response.slice(-1)[0];

    if (
      checkIfAllItemsAreTrue([
        filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.boleto,
        !!dataPagamentoBoleto,
        formikFiltrosTabelaAporte.values.dataDebito !== dataPagamentoBoleto,
      ])
    ) {
      formikFiltrosTabelaAporte.setFieldValue(
        'dataDebito',
        dataPagamentoBoleto,
      );
      setFiltroTabelaAporte(prevState => ({
        ...prevState,
        dataDebito: dataPagamentoBoleto,
      }));
    }

    if (filtroTabelaFundos.formaPagamento === '') {
      formikFiltrosTabelaAporte.setFieldValue('dataDebito', '');
      setFiltroTabelaAporte(prevState => ({
        ...prevState,
        dataDebito: '',
      }));
    }
  }, [
    filtroTabelaFundos.formaPagamento,
    datasDebito?.response,
    filtroTabelaFundos.valorContribuicao,
  ]);

  useEffect(() => {
    setFiltroTabelaAporte(formikFiltrosTabelaAporte.values);

    setShowInputs({
      open: formikFiltrosTabelaAporte.values.formaPagamento !== '',
      formaPagamento: tryGetValueOrDefault(
        [formikFiltrosTabelaAporte.values.formaPagamento],
        '',
      ),
    });
  }, [formikFiltrosTabelaAporte.values]);

  return {
    formik: formikFiltrosTabelaAporte,
    showInputs,
    filtroTabelaFundos,
    loadingDatasDebito: datasDebito.loading,
  };
};
