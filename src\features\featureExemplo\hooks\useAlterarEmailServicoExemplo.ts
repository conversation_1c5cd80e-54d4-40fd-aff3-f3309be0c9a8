import { getSessionItem } from '@cvp/utils';
import { ASSINATURA_SESSION_KEY } from '@src/corporativo/constants/assinatura/assinaturaSession';
import { OPERACOES_PREVIDENCIA } from '@src/corporativo/constants/OperacoesPrevidencia';
import { useConfirmarOperacaoAssinaturaCaixa } from '@src/corporativo/infra/assinaturaCaixa/useConfirmarOperacaoAssinaturaCaixa';
import { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';

type TUseAlterarEmailExemplo = {
  loading: boolean;
  etapa: 'editando' | 'assinatura' | 'salvar';
  setEtapa: (etapa: string) => string;
  confirmarAlteracaoEmail: (email: string) => Promise<void>;
};

export const useAlterarEmailServicoExemplo = (): TUseAlterarEmailExemplo => {
  const { confirmarAssinatura } = useConfirmarOperacaoAssinaturaCaixa();

  const invocarApi = async (email: string) => {
    return {
      codigoSolicitacao: '2537530',
      email,
    };
  };

  const confirmarAlteracaoEmail = async (email: string) => {
    const respostaServiceoExemplo = await invocarApi(email);
    const assinaturaResponse = getSessionItem<IAssinaturaResponse>(
      ASSINATURA_SESSION_KEY,
    );

    if (!assinaturaResponse?.resposta?.token) return;

    confirmarAssinatura({
      codigoSolicitacao: respostaServiceoExemplo.codigoSolicitacao,
      metaDadoConfirmacao: assinaturaResponse.resposta.token,
      tipoOperacao: OPERACOES_PREVIDENCIA.MANUTENCAO_DE_EMAIL,
    });
  };

  return {
    loading: false,
    confirmarAlteracaoEmail,
    etapa: 'assinatura',
    setEtapa: (etapa: string) => etapa,
  };
};
