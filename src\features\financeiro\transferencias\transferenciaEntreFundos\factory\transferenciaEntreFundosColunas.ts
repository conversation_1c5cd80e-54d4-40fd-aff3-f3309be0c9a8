import {
  TTransferenciaFundo as IFundo,
  formatarValorPadraoBrasileiro,
  porcentagem,
  TableColumn,
} from '../exports';

type TTransferenciaFundoColumn = IFundo & {
  selecionar?: string;
  transferenciaValor?: string;
  tipoFundo?: string;
};

type TColumn = TableColumn<TTransferenciaFundoColumn>;

export const trasferenciaEntreFundosColunas: TColumn[] = [
  {
    name: 'Selecionar',
    selector: ({ selecionar }) => selecionar ?? '',
    center: true,
    width: '100px',
  },
  {
    name: 'Fundo',
    selector: ({ desFundo }) => desFundo,
    center: true,
  },
  {
    name: 'Perfil do risco',
    selector: ({ desPerfilFundo }) => desPerfilFundo,
    center: true,
  },
  {
    name: 'Rentabilidade(útimos 12 meses)',
    selector: ({ pctRentabUltimoAno }) =>
      porcentagem.mask(pctRentabUltimoAno ?? ''),
    center: true,
  },
  {
    name: 'Sal<PERSON>',
    selector: ({ vlrSaldo }) => formatarValorPadraoBrasileiro(vlrSaldo ?? ''),
    center: true,
  },
  {
    name: 'Tipo de transferência',
    selector: ({ tipoFundo }) => tipoFundo ?? '',
    center: true,
  },
  {
    name: 'Valor',
    center: true,
    selector: ({ transferenciaValor }) => {
      if (typeof transferenciaValor === 'string')
        return formatarValorPadraoBrasileiro(transferenciaValor);
      return transferenciaValor ?? '';
    },
  },
];
