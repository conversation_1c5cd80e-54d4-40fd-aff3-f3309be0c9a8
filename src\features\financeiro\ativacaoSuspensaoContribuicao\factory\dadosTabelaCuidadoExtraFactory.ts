import * as AtivacaoSuspensao from '../exports';

export interface IDadosCuidadoExtra {
  campo: string;
  valor: string;
}

export const dadosTabelaCuidadoExtraFactory = (
  contribuicao?: AtivacaoSuspensao.IContribuicaoItem,
): IDadosCuidadoExtra[] => {
  if (!contribuicao) {
    return [];
  }

  return [
    {
      campo: 'Tipo:',
      valor: AtivacaoSuspensao.tryGetValueOrDefault([contribuicao.nome], '-'),
    },
    {
      campo: 'Valor da Contribuição:',
      valor: contribuicao.valorContribuicao
        ? AtivacaoSuspensao.formatarValorPadraoBrasileiro(
            contribuicao.valorContribuicao,
          )
        : '-',
    },
    {
      campo: 'Dia de Vencimento:',
      valor: AtivacaoSuspensao.tryGetValueOrDefault(
        [contribuicao.diaVencimento],
        '-',
      ),
    },
    {
      campo: 'Indenização:',
      valor: contribuicao.valorIdentizacao
        ? AtivacaoSuspensao.formatarValorPadraoBrasileiro(
            contribuicao.valorIdentizacao,
          )
        : '-',
    },
    {
      campo: 'Duração do Recebimento do Cuidado Extra:',
      valor: AtivacaoSuspensao.tryGetValueOrDefault(
        [contribuicao.prazoRecebimento],
        '-',
      ),
    },
  ];
};
