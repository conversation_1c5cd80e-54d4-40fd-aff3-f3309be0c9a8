import {
  IDadosNovaContaBancariaAporte,
  TFormikErrors,
} from '@src/features/financeiro/aporte/exports';

export interface IHandleChange {
  (e: React.ChangeEvent<string>): void;
  <T = string | React.ChangeEvent<string>>(
    field: T,
  ): T extends React.ChangeEvent<string>
    ? void
    : (e: string | React.ChangeEvent<string>) => void;
}

export type TSetFieldValue = (
  field: string,
  value: string,
  shouldValidate?: boolean,
) => Promise<void | TFormikErrors<IDadosNovaContaBancariaAporte>>;
