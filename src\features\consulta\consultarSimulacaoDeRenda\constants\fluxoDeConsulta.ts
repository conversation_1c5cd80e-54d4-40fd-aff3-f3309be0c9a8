export const LABEL_SELECT_RENDA =
  'Selecione qual o tipo de renda que deseja simular:';

export const LABEL_SELECT_RENDA_VITALICIA = {
  porcentagem_reversivel: 'Porcentagem reversível ao beneficiário',
  sexo_beneficiario: 'Sexo do beneficiário',
  data_nascimento: 'Data de nascimento',
};

export const TIPO_DE_RENDA = {
  vitalicia: 'vitalicia',
  temporaria: 'temporaria',
  prazo_minimo: 'vitalicia com prazo minimo garantido',
  reversivel: 'vitalicia reversivel ao beneficiario',
  prazo_certo: 'prazo certo',
};

export const ALERTAS_CONSULTA = {
  MENSAGEM_ATENCAO:
    'Os cálculos e as hipóteses aqui apresentadas são meras estimativas, não se constituindo em garantia ou obrigação da Caixa Vida e Previdência.',
  ESTIMATIVA_DE_RENDA: [
    'Os cálculos e as hipóteses aqui apresentados são apenas estimativas, não se constituindo em garantia ou obrigação da Caixa',
    'Vida e Previdência.Os valores estão sujeitos a alteração.A taxa de rentabilidade considerada para a estimativa de renda',
    'futura é de 6% ao ano.',
    'A renda/ benefício está sujeita a tributação de Imposto de Renda, conforme a legislação fiscal vigente.',
    'Esta simulação ficará disponível por 24h.',
    'Não deixe de conferir as condições expostas no regulamento da sua previdência para saber mais.',
  ],
};

export const PORCENTAGEM_REVERSIVEL_BENEFICIARIO = {
  toda_renda: 'Toda sua renda',
  75: '75%',
  25: '25%',
};
export const SEXO_BENEFICIARIO = {
  M: 'Masculino',
  F: 'Feminino',
};

export const RENDA_SIMULADA_ABAIXO_DO_PREVISTO =
  'O valor da renda simulada está abaixo do previsto no regulamento do seu plano:';

export const INITIAL_VALUES_FORMIK_RENDA_VITALICIA = {
  qtdPrazoAnos: '',
  pctReversao: '',
  dthNascimentoConjuge: '',
  staGeneroConjuge: '',
};

export const TEXTS_MODAL_CERTIFICADO_NAO_ELEGIVEL = {
  title: 'Atenção!',
  text: 'Certificado não elegível a alteração tributária, manterá o regime existente',
};

export const CAMPO_OBRIGATORIO = 'Campo obrigatório';

export const CAMPO_VAZIO = '--';

export const INITIAL_VALUE_INCOME = {
  renda: '',
  numeroDaRenda: '',
};
export const HABILITADO_PARA_ALTERAR_REGIME = 'S';

export const PRAZO_TIPO_DE_RENDA = {
  TEMPORARIA: 'valores entre 5 e 35 anos',
  PRAZO_MINIMO: 'valores entre 5 e 35 anos',
  PRAZO_CERTO: 'valores entre 1 e 35 anos',
};

export const NORMALIZE_STRING = 'NFD';
