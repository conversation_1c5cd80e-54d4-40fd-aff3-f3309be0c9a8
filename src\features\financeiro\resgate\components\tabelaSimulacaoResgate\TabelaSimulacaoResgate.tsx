import * as Resgate from '@src/features/financeiro/resgate/exports';

export const TabelaSimulacaoResgate = ({
  fundosParaResgate,
  resultadoCalculoResgateRestante,
  isDisabledBtnSimulacaoResgate,
  confirmarSimulacao,
  reiniciarSimulacao,
  isLoadingConfirmacaoSimulacao,
}: Resgate.ITabelaSimulacaoResgateProps): React.ReactElement => {
  const theme = Resgate.useTheme();

  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  const { resgateFeatureData } = Resgate.useResgateContext();

  return (
    <Resgate.ConditionalRenderer condition={!!formik.values.tipoResgate}>
      <Resgate.GridItem xs="1">
        <Resgate.ResgateTable
          themeTable="cvp-05"
          highlightOnHover
          striped
          columns={Resgate.COLUNAS_SIMULACAO_RESGATE}
          data={fundosParaResgate}
          noDataComponent={Resgate.TABELA_SEM_DADOS}
        />

        <Resgate.ContainerResumoResgate>
          <Resgate.Text
            variant="text-standard-600"
            fontColor="content-neutral-05"
          >
            Saldo total:{' '}
            {Resgate.tryGetMonetaryValueOrDefault(
              resultadoCalculoResgateRestante.calculoRestante,
            )}
          </Resgate.Text>

          <div>
            <Resgate.Text
              variant="text-standard-600"
              fontColor="content-neutral-05"
            >
              Valor do resgate:{' '}
              {Resgate.tryGetMonetaryValueOrDefault(
                resultadoCalculoResgateRestante.calculoTotal,
              )}
            </Resgate.Text>

            <Resgate.Button
              size="standard"
              variant="secondary"
              onClick={() => {
                Resgate.getTernaryResult(
                  !!resgateFeatureData.hasDadosSimulacao,
                  reiniciarSimulacao,
                  confirmarSimulacao,
                )();
              }}
              disabled={Resgate.checkIfSomeItemsAreTrue([
                isDisabledBtnSimulacaoResgate,
                isLoadingConfirmacaoSimulacao,
              ])}
              leftIcon={
                <Resgate.ConditionalRenderer
                  condition={isLoadingConfirmacaoSimulacao}
                >
                  <Resgate.LoadingSpinner
                    size="small"
                    color={theme.color.content.neutral['01']}
                  />
                </Resgate.ConditionalRenderer>
              }
            >
              {Resgate.getTernaryResult(
                !!resgateFeatureData.hasDadosSimulacao,
                Resgate.LABEL_BUTTON_SIMULACAO.NOVA_SIMULACAO,
                Resgate.LABEL_BUTTON_SIMULACAO.SIMULAR_RESGATE,
              )}
            </Resgate.Button>
          </div>
        </Resgate.ContainerResumoResgate>
      </Resgate.GridItem>
    </Resgate.ConditionalRenderer>
  );
};
