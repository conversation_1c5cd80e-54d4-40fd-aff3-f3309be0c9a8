import {
  IHandleReponseResult,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useContext } from 'react';
import { PECOS } from '../config/api/endpoints';

type TUseObterDadosParticipante = () => {
  response: IDadosParticipanteEntidade;
  loading: boolean;
  obterDadosParticipante: () => Promise<
    IHandleReponseResult<IDadosParticipanteEntidade> | undefined
  >;
};

export interface IDadosParticipanteEntidade {
  idPessoa: string;
  nome: string;
  numCpf: string;
  sexo: string;
  estadoCivil: string;
  dataNascimento: string;
  idEndereco: string;
  endereco: string;
  numero: string;
  tipoEndereco: string;
  complemento: string;
  bairro: string;
  idCidade: string;
  cidade: string;
  uf: string;
  cep: string;
  idEstado: string;
  idPais: string;
  tipoRua: string;
  enderecoPadrao: string;
  email: string;
  idEmail: string;
  tipoEmail: string;
  telefones: Array<ITelefone>;
  nomeSocial: string;
}

export interface ITelefone {
  idTelefone: string;
  codigoArea: string;
  numeroTelefone: string;
  tipoTelefone: string;
  aceitaSms: string;
  telefonePrincipal: string;
  extensaoNumero: unknown;
  localTelefone: string;
}

const RETORNO_VAZIO = {} as IDadosParticipanteEntidade;

interface IObterDadosParticipanteRequest {
  cpfCnpj: string;
  numeroCertificado: string;
}

export const useObterDadosDoParticipante: TUseObterDadosParticipante = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IObterDadosParticipanteRequest,
      IDadosParticipanteEntidade
    >(PECOS.ObterDadosParticipante, {
      data: {
        cpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
        numeroCertificado: certificadoAtivo.certificadoNumero,
      },
      autoFetch: true,
    });

  return {
    loading,
    response: tryGetValueOrDefault([response?.entidade], RETORNO_VAZIO),
    obterDadosParticipante: invocarApiGatewayCvpComToken,
  };
};
