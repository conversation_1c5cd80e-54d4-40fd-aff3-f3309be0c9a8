import MatrizAcessoRenderizadorAlgumaPermissao from '@src/corporativo/components/MatrizAcessoRenderizadorAlgumaPermissao';
import MatrizAcessoRenderizadorTodasPermissoes from '@src/corporativo/components/MatrizAcessoRenderizadorTodasPermissoes';
import { render, screen } from '@testing-library/react';

describe('MatrizAcessoRenderizadorTodasPermissoes', () => {
  it('Deve renderizar o componente filho ao possuir todas as permissões necessárias', () => {
    const text = 'Texto teste';
    render(
      <MatrizAcessoRenderizadorTodasPermissoes
        matrizAcesso={['permissao1', 'permissao2']}
        permissoesComponente={['permissao1', 'permissao2']}
      >
        <p>{text}</p>
      </MatrizAcessoRenderizadorTodasPermissoes>,
    );

    expect(screen.getByText(text)).toBeInTheDocument();
  });

  it('Não deve renderizar o componente filho ao não possuir todas as permissões necessárias', () => {
    const text = 'Outro Texto teste';
    render(
      <MatrizAcessoRenderizadorTodasPermissoes
        matrizAcesso={['permissao1', 'permissao2']}
        permissoesComponente={['permissao2', 'permissao3']}
      >
        <p>{text}</p>
      </MatrizAcessoRenderizadorTodasPermissoes>,
    );

    expect(screen.queryByText(text)).not.toBeInTheDocument();
  });
});

describe('MatrizAcessoRenderizadorAlgumaPermissao', () => {
  it('Deve renderizar o componente filho ao possuir pelo menos uma das permissões necessárias', () => {
    const text = 'Devo renderizar';
    render(
      <MatrizAcessoRenderizadorAlgumaPermissao
        matrizAcesso={['permissao1', 'permissao2']}
        permissoesComponente={['permissao2', 'permissao3']}
      >
        <p>{text}</p>
      </MatrizAcessoRenderizadorAlgumaPermissao>,
    );

    expect(screen.queryByText(text)).toBeInTheDocument();
  });

  it('Não deve renderizar o componente filho ao não possuir pelo menos uma das permissões necessárias', () => {
    const text = 'Não devo renderizar';
    render(
      <MatrizAcessoRenderizadorAlgumaPermissao
        matrizAcesso={['permissao1', 'permissao2']}
        permissoesComponente={['permissao3', 'permissao4']}
      >
        <p>{text}</p>
      </MatrizAcessoRenderizadorAlgumaPermissao>,
    );

    expect(screen.queryByText(text)).not.toBeInTheDocument();
  });

  it('Não deve renderizar o componente se a matriz de acesso não possuir permissões', () => {
    const text = 'Não devo renderizar';
    render(
      <MatrizAcessoRenderizadorAlgumaPermissao
        matrizAcesso={[]}
        permissoesComponente={['permissao']}
      >
        <p>{text}</p>
      </MatrizAcessoRenderizadorAlgumaPermissao>,
    );

    expect(screen.queryByText(text)).not.toBeInTheDocument();
  });

  it('Deve renderizar o componente filho caso não possua permissão específica', () => {
    const text = 'Devo renderizar';
    render(
      <MatrizAcessoRenderizadorAlgumaPermissao
        matrizAcesso={['permissao1', 'permissao2']}
        permissoesComponente={[]}
      >
        <p>{text}</p>
      </MatrizAcessoRenderizadorAlgumaPermissao>,
    );

    expect(screen.queryByText(text)).toBeInTheDocument();
  });
});
