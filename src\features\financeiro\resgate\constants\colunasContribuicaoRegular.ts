import {
  IMapearFundosContribuicaoRegularFactoryReturn,
  TableColumn,
} from '@src/features/financeiro/resgate/exports';

export const COLUNAS_CONTRIBUICAO_REGULAR: TableColumn<IMapearFundosContribuicaoRegularFactoryReturn>[] =
  [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      cell: (row: IMapearFundosContribuicaoRegularFactoryReturn) =>
        row.selecionar,
      center: true,
      minWidth: '50px',
    },
    {
      name: 'Fundo',
      selector: (row: IMapearFundosContribuicaoRegularFactoryReturn) =>
        row.descricaoFundo,
      center: true,
      wrap: true,
      minWidth: '180px',
    },
    {
      name: 'Perfil do risco',
      cell: (row: IMapearFundosContribuicaoRegularFactoryReturn) =>
        row.perfilFundo,
      center: true,
      minWidth: '120px',
    },
    {
      name: 'Ren<PERSON>bilidade (útimos 12 meses)',
      selector: (row: IMapearFundosContribuicaoRegularFactoryReturn) =>
        row.rentabilidade,
      center: true,
      minWidth: '150px',
    },
    {
      name: 'Contribuição mensal',
      center: true,
      cell: (row: IMapearFundosContribuicaoRegularFactoryReturn) =>
        row.valorContribuicao,
      minWidth: '160px',
    },
  ];
