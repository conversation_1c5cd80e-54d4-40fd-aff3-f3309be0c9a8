import { Match, SwitchCase } from '@cvp/componentes-posvenda';
import {
  Al<PERSON>,
  But<PERSON>,
  Card,
  ConditionalRenderer,
  Grid,
  IconInfoRound,
  IconInfoSharp,
  LoadingSpinner,
  Select,
  Table,
  Text,
  useTheme,
} from '@cvp/design-system-caixa';
import {
  checkIfSomeItemsAreTrue,
  formatarDataHoraAmigavel,
  getTernaryResult,
  tryGetMonetaryValueOrDefault,
} from '@cvp/utils';
import { VerticalDivider } from '@src/corporativo/components/divider';
import { TabelaPrevidencia } from '@src/shared/components/TabelaPrevidencia';

export type { TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';

export * as Infra from '@src/corporativo/infra/consulta/reajusteAnualDoPlano';
export * as CONSTANTES from '@src/features/consulta/reajusteAnualDoPlano/constants';
export * as Fac from '@src/features/consulta/reajusteAnualDoPlano/factories';
export * as Styles from '@src/features/consulta/reajusteAnualDoPlano/styles';
export * as Types from '@src/features/consulta/reajusteAnualDoPlano/types';
export * as Views from '@src/features/consulta/reajusteAnualDoPlano/views';
export * as Hooks from '@src/features/consulta/reajusteAnualDoPlano/hooks';

export const Ds = {
  Text,
  Card,
  Alert,
  IconInfoRound,
  IconInfoSharp,
  Table,
  Button,
  VerticalDivider,
  TabelaPrevidencia,
  Grid,
  useTheme,
  SwitchCase,
  Match,
  LoadingSpinner,
  ConditionalRenderer,
  Select,
};

export const Utils = {
  getTernaryResult,
  formatarDataHoraAmigavel,
  tryGetMonetaryValueOrDefault,
  checkIfSomeItemsAreTrue,
};
