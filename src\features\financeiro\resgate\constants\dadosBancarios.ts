import {
  ITipoContasBancarias,
  TCamposNovaContaResgate,
} from '@src/features/financeiro/resgate/exports';

export const TIPOS_CANAL_PAGAMENTO = {
  RESGATE_DOC: '23',
  RESGATE_TED: '24',
  G<PERSON><PERSON>_PAGAMENTO_BANCARIO: '31',
  RESGATE_TRANSFERENCIA: '9',
};

export const PADROES_CONTA_BANCARIA = {
  SIGLA_CONTA_CORRENTE: 'CC',
  SIGLA_CONTA_POUPANCA: 'CP',
  CODIGO_CONTA_CORRENTE: '001',
  CODIGO_CONTA_POUPANCA: '013',
  DIGITO_AGENCIA_PADRAO: '0',
};

export const CONFIGURACAO_TIPO_CONTA: Record<string, string> = {
  [PADROES_CONTA_BANCARIA.SIGLA_CONTA_CORRENTE]:
    PADROES_CONTA_BANCARIA.CODIGO_CONTA_CORRENTE,
  [PADROES_CONTA_BANCARIA.SIGLA_CONTA_POUPANCA]:
    PADROES_CONTA_BANCARIA.CODIGO_CONTA_POUPANCA,
};

export const TIPO_CONTAS_BANCARIAS_CEF: ITipoContasBancarias[] = [
  {
    codigo: '001',
    detalhado: '001 - Conta Corrente de Pessoa Física',
    descricao: 'PF',
  },
  {
    descricao: 'PJ',
    codigo: '003',
    detalhado: '003 - Conta Corrente de Pessoa Jurídica',
  },
  {
    descricao: 'PF',
    codigo: '013',
    detalhado: '013 - Poupança de Pessoa Física',
  },
  {
    codigo: '022',
    detalhado: '022 - Poupança de Pessoa Jurídica',
    descricao: 'PJ',
  },
  {
    descricao: 'Conta Caixa Fácil',
    detalhado: '023 - Conta Caixa Fácil',
    codigo: '023',
  },
  {
    codigo: '1288',
    descricao: 'Poupança PF / Caixa Fácil',
    detalhado: '1288 - Poupança PF / Caixa Fácil',
  },
  {
    descricao: 'PJ',
    detalhado: '1292 - Conta Corrente PJ',
    codigo: '1292',
  },
  {
    codigo: '3701',
    detalhado: '3701 - Conta Corrente PF',
    descricao: 'PF',
  },
  {
    codigo: '3702',
    detalhado: '3702 - Poupança PJ',
    descricao: 'PJ',
  },
];

export const TIPO_CONTAS_BANCARIAS_NAO_CEF: ITipoContasBancarias[] = [
  {
    codigo: '001',
    descricao: 'CC',
    detalhado: '',
  },
  {
    codigo: '013',
    descricao: 'CP',
    detalhado: '',
  },
];

export const NOVA_CONTA = {
  text: 'Outra instituição financeira',
  value: '',
};

export const CAMPOS_NOVA_CONTA_RESGATE: Record<
  TCamposNovaContaResgate,
  TCamposNovaContaResgate
> = {
  agencia: 'agencia',
  conta: 'conta',
  digito: 'digito',
  operacao: 'operacao',
};

export const LISTA_CAMPOS_NOVA_CONTA_RESGATE: TCamposNovaContaResgate[] =
  Object.values(CAMPOS_NOVA_CONTA_RESGATE);

export const CODIGO_BANCO = {
  QTD_CARACTERES_DEFAULT: 3,
  CARACTERE_DEFAULT_ADICIONADO: '0',
};
