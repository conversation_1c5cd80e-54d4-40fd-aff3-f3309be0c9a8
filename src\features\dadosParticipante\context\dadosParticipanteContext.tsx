import {
  dadosParticipanteReducer,
  IDadosParticipanteEntidade,
  IDadosParticipanteState,
  TActionsDadosParticipante,
  React,
  dadosParticipanteInitialState,
  criarStateInicialDadosParticipante,
} from '../exports';

interface IDadosParticipanteProvider extends React.PropsWithChildren {
  dadosParticipante: IDadosParticipanteEntidade;
}

export const DadosParticipanteContext =
  React.createContext<IDadosParticipanteState>(dadosParticipanteInitialState);
export const DadosParticipanteDispatchContext = React.createContext<
  React.Dispatch<TActionsDadosParticipante>
>({} as React.Dispatch<TActionsDadosParticipante>);

export const DadosParticipanteProvider: React.FC<
  IDadosParticipanteProvider
> = ({ children, dadosParticipante }) => {
  const [state, dispatch] = React.useReducer(
    dadosParticipanteReducer,
    dadosParticipante,
    criarStateInicialDadosParticipante,
  );

  return (
    <DadosParticipanteContext.Provider value={state}>
      <DadosParticipanteDispatchContext.Provider value={dispatch}>
        {children}
      </DadosParticipanteDispatchContext.Provider>
    </DadosParticipanteContext.Provider>
  );
};
