import {
  columnsTabelaFundosDistribuicaoFactory,
  customStyles,
  dadosTabelaFundosAporteFactory,
  FUNDOS,
  IDadosFundosExistentesAporte,
  Match,
  Table,
  Text,
  TIPO_FUNDO,
  tryGetValueOrDefault,
  useAporteContext,
  useRenderTabelaFundos,
} from '@src/features/financeiro/aporte/exports';

interface ITabelaFundosDistribuicaoExistentes {
  renderFundos: IDadosFundosExistentesAporte;
}

const TabelaFundosDistribuicaoExistentes: React.FC<
  ITabelaFundosDistribuicaoExistentes
> = ({ renderFundos }) => {
  const { itensExtrato } = useAporteContext();

  const dadosTabelaFundos = dadosTabelaFundosAporteFactory({
    renderFundos,
    itensExtrato,
  });

  return (
    <Match when={renderFundos.dadosFundos.length > 0}>
      <Text variant="text-large-600" fontColor="content-neutral-04">
        {FUNDOS.existentes}
      </Text>
      <Table
        responsive
        striped
        highlightOnHover
        themeTable="cvp-04"
        customStyles={customStyles}
        data={tryGetValueOrDefault(
          [
            useRenderTabelaFundos({
              dadosTabelaFundos,
              tipoFundo: TIPO_FUNDO.existente,
            }),
          ],
          [],
        )}
        columns={columnsTabelaFundosDistribuicaoFactory}
        noDataComponent={FUNDOS.fundosExistentesIndisponiveis}
      />
    </Match>
  );
};

export default TabelaFundosDistribuicaoExistentes;
