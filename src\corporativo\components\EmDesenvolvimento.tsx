import { LoadingSpinner, Text } from '@cvp/design-system-caixa';

interface IEmDesenvolvimentoProps {
  funcionalidade: string;
}

export const EmDesenvolvimento: React.FC<IEmDesenvolvimentoProps> = ({
  funcionalidade,
}) => {
  return (
    <div style={{ padding: '30px' }}>
      <LoadingSpinner size="medium">
        <Text variant="text-large-400" fontColor="brand-primary-09">
          A funcionalidade{' '}
          <strong style={{ color: '#005CA9' }}>{funcionalidade}</strong> está em
          desenvolvimento...
        </Text>
      </LoadingSpinner>
    </div>
  );
};
