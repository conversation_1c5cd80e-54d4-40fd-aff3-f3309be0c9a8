import {
  AMBIENTE_INTEGRADO_URL,
  Assinatura,
  Button,
  Conditional<PERSON><PERSON><PERSON>,
  EBeneficiariosActionKind,
  LoadingSpinner,
  Match,
  ModalCancelarEdicaoBeneficiarios,
  PrevidenciaContext,
  S,
  SwitchCase,
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  getSessionItem,
  useBeneficiariosForm,
  useContext,
  useControlesAssinaturaBeneficiarios,
} from '../exports';

export const AssinaturaBeneficiarios: React.FC = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { isSubmitting } = useBeneficiariosForm();
  const {
    assinaturaCallback,
    assinaturaValida,
    blocker,
    editando,
    confirmandoEdicao,
    dispatch,
    cancelarEdicaoBeneficiarios,
    confirmarEdicaoBeneficiarios,
  } = useControlesAssinaturaBeneficiarios();

  const cpfCnpj = String(getSessionItem('cpfCnpj'));

  return (
    <ConditionalRenderer
      condition={checkIfSomeItemsAreTrue([editando, confirmandoEdicao])}
    >
      <ConditionalRenderer condition={confirmandoEdicao}>
        <Assinatura
          dados={{
            cpfCnpj,
            numeroCertificado: String(certificadoAtivo?.numeroCertificado),
          }}
          callback={assinaturaCallback}
        />
      </ConditionalRenderer>
      <ModalCancelarEdicaoBeneficiarios
        open={blocker.state === 'blocked'}
        handleProceed={() => blocker.proceed!()}
        handleReset={() => blocker.reset!()}
      />
      <S.ButtonsContainer>
        <Button
          type="button"
          variant="secondary-outlined"
          onClick={cancelarEdicaoBeneficiarios}
        >
          Cancelar
        </Button>

        <ConditionalRenderer
          condition={confirmandoEdicao}
          fallback={
            <S.SaveButton
              type="button"
              onClick={() => {
                dispatch({
                  type: EBeneficiariosActionKind.CONFIRMAR_EDICAO_BENEFICIARIOS,
                });
              }}
              variant="secondary"
            >
              Salvar
            </S.SaveButton>
          }
        >
          <S.SaveButton
            type="button"
            onClick={confirmarEdicaoBeneficiarios}
            variant="secondary"
            disabled={checkIfSomeItemsAreTrue([
              isSubmitting,
              checkIfAllItemsAreTrue([
                AMBIENTE_INTEGRADO_URL,
                !assinaturaValida,
              ]),
            ])}
          >
            <SwitchCase fallback="Confirmar">
              <Match when={isSubmitting}>
                <LoadingSpinner color="#9EB2B8" />
              </Match>
            </SwitchCase>
          </S.SaveButton>
        </ConditionalRenderer>
      </S.ButtonsContainer>
    </ConditionalRenderer>
  );
};
