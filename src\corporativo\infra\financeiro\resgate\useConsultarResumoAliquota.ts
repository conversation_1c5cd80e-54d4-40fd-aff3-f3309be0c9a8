import { useContext } from 'react';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IConsultarResumoAliquotaPayload,
  IConsultarResumoAliquotaResponse,
  IUseConsultarResumoAliquotaReturn,
} from '@src/corporativo/types/financeiro/resgate/IConsultarResumoAliquota';

export const useConsultarResumoAliquota =
  (): IUseConsultarResumoAliquotaReturn => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const codigoCertificado = tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    );

    const {
      response: dadosConsultaResumoAliquota,
      loading: isLoadingConsultaResumoAliquota,
      invocarApiGatewayCvpComToken: consultarResumoAliquota,
    } = useApiGatewayCvpInvoker<
      Partial<IConsultarResumoAliquotaPayload>,
      IConsultarResumoAliquotaResponse
    >(PECOS.ConsultarResumoAliquota, {
      data: { codigoCertificado },
    });

    return {
      dadosConsultaResumoAliquota: tryGetValueOrDefault(
        [dadosConsultaResumoAliquota?.entidade],
        {} as IConsultarResumoAliquotaResponse,
      ),
      isLoadingConsultaResumoAliquota,
      consultarResumoAliquota,
    };
  };
