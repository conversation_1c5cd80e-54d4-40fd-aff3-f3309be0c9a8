import {
  Button,
  Conditional<PERSON><PERSON><PERSON>,
  Dialog,
  LoadingSpinner,
  Text,
  TextField,
  useTheme,
} from '@cvp/design-system-caixa';

import { Alerta } from '@src/corporativo/components/Alerta';
import { MODAL_ENVIO_EMAIL } from '@src/shared/constants/modalEnvioEmail';
import useModalEnvioEmail from '@src/shared/hooks/useModalEnvioEmail';
import { IModalEnvioEmailProps } from '@src/shared/types/IModalEnvioEmailProps';
import React from 'react';

export const ModalEnvioEmail = ({
  objetoEmail,
  onEmailSent,
  emailDefault = '',
  label = MODAL_ENVIO_EMAIL.LABEL_DEFAULT,
  buttonText = MODAL_ENVIO_EMAIL.TEXTO_BOTAO_DEFAULT,
}: IModalEnvioEmailProps): React.ReactElement => {
  const theme = useTheme();

  const {
    toggleModal,
    handleChange,
    handleSend,
    isOpen,
    email,
    mensagens,
    isLoading,
    isDisabled,
  } = useModalEnvioEmail({ objetoEmail, onEmailSent, emailDefault });

  return (
    <>
      <Button onClick={toggleModal}>{buttonText}</Button>

      <Dialog open={isOpen} onOpenChange={toggleModal}>
        <Dialog.Content>
          <Dialog.Header>
            <Text variant="heading-small-600">Informe o E-mail para Envio</Text>
          </Dialog.Header>
          <Dialog.Body>
            <TextField
              type="email"
              variant="box-classic"
              placeholder="Digite o e-mail"
              label={label}
              value={email}
              onChange={handleChange}
            />

            <ConditionalRenderer condition={!!mensagens.erroEmail}>
              <Alerta tipo="erro">{mensagens.erroEmail}</Alerta>
            </ConditionalRenderer>

            <ConditionalRenderer condition={!!mensagens.sucessoEmail}>
              <Alerta tipo="sucesso">{mensagens.sucessoEmail}</Alerta>
            </ConditionalRenderer>
          </Dialog.Body>
          <Dialog.Footer>
            <Button
              size="standard"
              variant="primary"
              onClick={handleSend}
              disabled={isDisabled}
              leftIcon={
                <ConditionalRenderer condition={isLoading}>
                  <LoadingSpinner
                    size="small"
                    color={theme.color.content.neutral['01']}
                  />
                </ConditionalRenderer>
              }
            >
              Enviar
            </Button>
          </Dialog.Footer>
        </Dialog.Content>
      </Dialog>
    </>
  );
};
