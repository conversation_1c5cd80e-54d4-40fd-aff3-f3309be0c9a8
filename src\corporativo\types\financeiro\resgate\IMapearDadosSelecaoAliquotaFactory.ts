import { ICalcularResgateDadosEncargo } from '@src/corporativo/types/financeiro/resgate/ICalcularResgate';
import { IConsultarDetalheCalculoResponse } from '@src/corporativo/types/financeiro/resgate/IConsultarDetalheCalculo';
import { IConsultarResumoAliquotaResponse } from '@src/corporativo/types/financeiro/resgate/IConsultarResumoAliquota';

export interface IMapearDadosSelecaoAliquotaFactoryRetorno {
  calculoAliquotaProgressiva: ICalcularResgateDadosEncargo | undefined | null;
  calculoAliquotaRegressiva: ICalcularResgateDadosEncargo | undefined | null;
  resumoAliquotaProgressiva:
    | IConsultarResumoAliquotaResponse
    | undefined
    | null;
  resumoAliquotaRegressiva: IConsultarResumoAliquotaResponse | undefined | null;
  detalhamentoAliquotaProgressiva:
    | IConsultarDetalheCalculoResponse
    | undefined
    | null;
  detalhamentoAliquotaRegressiva:
    | IConsultarDetalheCalculoResponse
    | undefined
    | null;
}
