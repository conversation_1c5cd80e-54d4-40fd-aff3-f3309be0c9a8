import {
  checkIfSomeItemsAreTrue,
  FILTRO_TODOS,
  IResponseHistoricoSolicitacoes,
  TFiltros,
} from '@src/features/consulta/historicoSolicitacoes/exports';

export const formatarListagemComFiltros = (
  listagem: IResponseHistoricoSolicitacoes[],
  filtros: TFiltros,
): IResponseHistoricoSolicitacoes[] => {
  if (
    checkIfSomeItemsAreTrue([
      !filtros?.filtroAtivo?.tipoSolicitadoValor,
      filtros?.filtroAtivo?.tipoSolicitadoValor === FILTRO_TODOS.value,
    ])
  ) {
    return listagem;
  }

  return listagem.filter(
    item => item.tipoRequisicao === filtros?.filtroAtivo?.tipoSolicitadoValor,
  );
};
