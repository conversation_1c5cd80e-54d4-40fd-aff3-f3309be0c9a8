import {
  tryGetValueOrDefault,
  TIPOS_RESGATE,
  IMontarDadosTabelaDetalheCalculoFactoryRetorno,
} from '@src/features/financeiro/resgate/exports';

export const CONDITIONAL_ROW_STYLES_TABELA_VALORES_DETALHADOS_ALIQUOTA = [
  {
    when: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno): boolean =>
      tryGetValueOrDefault([row.data, row.descricaoFundo], '') ===
      TIPOS_RESGATE.TOTAL.value,
    style: {},
  },
];
