import {
  IAlerta,
  IAlertaIdentificado,
  IconCheckCircleOutlinedRound,
  IconInfoRound,
  IconWarningOutlinedRound,
  IconWarningSharp,
} from '../exports';

const COLOR_DANGER = '#900000';

export const TIPOS_ALERTA = {
  TABELA: 'tabela',
  BENEFICIARIOS: 'beneficiarios',
};

export const ALERTA_SUCESSO_EDICAO: IAlertaIdentificado = {
  identificador: 'beneficiarios',
  message: 'Beneficiários editados com sucesso!',
  variant: 'warning-01',
  icon: <IconCheckCircleOutlinedRound size="medium" color="#127527" />,
};

export const ALERTA_PORCENTAGEM_WARN: IAlerta = {
  message:
    'Atençã! A porcentagem total atribuída aos beneficiários é diferente de 100%.',
  variant: 'warning-01',
  icon: <IconWarningSharp size="medium" color="#977203" />,
};

export const ALERTA_ERRO_EDICAO: IAlertaIdentificado = {
  identificador: 'beneficiarios',
  message: 'Erro ao editar os beneficiários',
  variant: 'danger-01',
  icon: <IconWarningOutlinedRound size="medium" color={COLOR_DANGER} />,
};

export const ALERTA_HERDEIROS_LEGAIS: IAlerta = {
  message:
    'Não havendo a indicação expressa de beneficiário(s), a indenização será paga, em sua metade, ao cônjuge não separado judicialmente e o restante ao(s) Herdeiro(s) Legal(is) do titular, conforme disposto no artigo 792 do Código Civil Brasileiro.',
  variant: 'information-01',
  icon: <IconInfoRound size="medium" color="#038299" />,
};

export const ALERTA_CPF_INVALIDO: IAlerta = {
  message: 'Cpf inválido',
  variant: 'danger-01',
  icon: <IconWarningOutlinedRound size="medium" color={COLOR_DANGER} />,
};

export const ALERTA_CPF_EXISTENTE: IAlerta = {
  message: 'Cpf já adicionado a lista de novos usuários',
  variant: 'danger-01',
  icon: <IconWarningOutlinedRound size="medium" color={COLOR_DANGER} />,
};
