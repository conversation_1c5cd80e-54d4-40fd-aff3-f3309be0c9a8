import * as Resgate from '@src/features/financeiro/resgate/exports';

export const FluxoTelasResgate = (): React.ReactElement => {
  const { etapa } = Resgate.useResgateContext();

  return (
    <Resgate.SwitchCase>
      <Resgate.Match when={etapa === Resgate.EEtapasResgate.SIMULACAO}>
        <Resgate.SimulacaoResgate />
      </Resgate.Match>

      <Resgate.Match when={etapa === Resgate.EEtapasResgate.SOLICITACAO}>
        <Resgate.SolicitacaoResgate />
      </Resgate.Match>

      <Resgate.Match when={etapa === Resgate.EEtapasResgate.SUCESSO}>
        <Resgate.SucessoResgate />
      </Resgate.Match>
    </Resgate.SwitchCase>
  );
};
