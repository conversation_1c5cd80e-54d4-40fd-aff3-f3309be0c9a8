import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useFormularioContaResgate =
  (): Resgate.IUseFormularioContaResgate => {
    const formik =
      Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();
    const { resgateFeatureData } = Resgate.useResgateContext();

    const limparFormularioNovaConta = (): void => {
      formik.setValues({
        ...formik.values,
        novaConta: {
          banco: { label: '', value: '' },
          agencia: '',
          conta: '',
          digito: '',
          operacao: '',
        },
      });
    };

    const selecionarContaResgate = (event: Resgate.SelectItem[]): void => {
      const contaSelecionada = Resgate.tryGetValueOrDefault(
        [event[0]?.value],
        '',
      );
      const isNovaConta = !contaSelecionada;

      if (!isNovaConta) limparFormularioNovaConta();

      formik.setValues({
        ...formik.values,
        isNovaConta,
        contaExistente: Resgate.mapearContaExistenteFormFactory({
          tiposPagamento: resgateFeatureData.tiposPagamento?.tipoPagamentos,
          codigoContaSelecionada: contaSelecionada,
        }),
      });
    };

    const handleChangeCamposNovaContaResgate = (
      field: string,
      value: string,
    ): void => {
      formik.setValues({
        ...formik.values,
        novaConta: {
          ...formik.values.novaConta,
          [field]: value,
        },
      });
    };

    const temContaPreenchida: boolean = Resgate.checkIfSomeItemsAreTrue([
      formik.values.isNovaConta,
      !!formik.values.contaExistente.codigoBanco,
    ]);

    return {
      selecionarContaResgate,
      handleChangeCamposNovaContaResgate,
      temContaPreenchida,
    };
  };
