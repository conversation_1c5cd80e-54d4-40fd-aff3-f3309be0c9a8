import { IMontarResumoAliquotaFactoryRetorno } from '@src/corporativo/types/financeiro/resgate/IMontarResumoAliquotaFactory';

export interface IObterResumoAliquotaSelecionada {
  indicadorPermiteEditarAliquota: boolean;
  aliquotaAtual: string;
  resumoAliquotaProgressiva: IMontarResumoAliquotaFactoryRetorno[];
  resumoAliquotaRegressiva: IMontarResumoAliquotaFactoryRetorno[];
  dadosResumoSelecionado: IMontarResumoAliquotaFactoryRetorno[];
  opcaoRegimeTributario: string;
}

export interface IObterResumoAliquotaSelecionadaRetorno {
  resumo: IMontarResumoAliquotaFactoryRetorno[] | undefined;
  tipoAliquota: string | undefined;
}
