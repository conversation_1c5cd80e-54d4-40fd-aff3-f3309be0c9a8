import {
  BeneficiariosContext,
  formatterTableCells,
  TCelulaTabelaProps,
  TFormatterTableCellParams,
  useContext,
  BENEFICIARIOS_CAMPOS_EDITAVEIS,
  useBeneficiariosForm,
  InputsBeneficiarios,
} from '../exports';

export const InputCelulaBeneficiario: React.FC<TCelulaTabelaProps> = ({
  tipo,
  field,
  estado,
  coberturaId,
  ...rest
}) => {
  const { verificarCoberturaEditando } = useBeneficiariosForm();
  const { confirmandoEdicao } = useContext(BeneficiariosContext);

  if (
    confirmandoEdicao ||
    !verificarCoberturaEditando(coberturaId) ||
    !BENEFICIARIOS_CAMPOS_EDITAVEIS[
      estado as keyof typeof BENEFICIARIOS_CAMPOS_EDITAVEIS
    ].includes(String(rest.name))
  )
    return (
      <span>
        {formatterTableCells({
          val: field.value,
          tipo,
        } as unknown as TFormatterTableCellParams)}
      </span>
    );

  const Component = InputsBeneficiarios[tipo];
  return <Component coberturaId={coberturaId} field={field} rest={rest} />;
};
