import { useContext } from 'react';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IDefinirContribuicaoRegularPayload,
  IDefinirContribuicaoRegularResponse,
  IUseDefinirContribuicaoRegularReturn,
} from '@src/corporativo/types/financeiro/resgate/IDefinirContribuicaoRegular';

export const useDefinirContribuicaoRegular =
  (): IUseDefinirContribuicaoRegularReturn => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const codigoCertificado = tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    );

    const {
      response: dadosContribuicaoRegular,
      loading: isLoadingDefinicaoContribuicaoRegular,
      invocarApiGatewayCvpComToken: definirContribuicaoRegular,
    } = useApiGatewayCvpInvoker<
      Partial<IDefinirContribuicaoRegularPayload>,
      IDefinirContribuicaoRegularResponse
    >(PECOS.DefinirContribuicaoRegular, {
      data: { codigoCertificado },
    });

    return {
      dadosContribuicaoRegular: tryGetValueOrDefault(
        [dadosContribuicaoRegular?.entidade],
        {} as IDefinirContribuicaoRegularResponse,
      ),
      isLoadingDefinicaoContribuicaoRegular,
      definirContribuicaoRegular,
    };
  };
