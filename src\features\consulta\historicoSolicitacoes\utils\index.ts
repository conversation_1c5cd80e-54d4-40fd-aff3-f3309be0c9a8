import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
} from '@src/features/consulta/historicoSolicitacoes/exports';

export const verificaErroPeriodoData = (
  inicio: Date | null | string,
  fim: Date | null | string,
): boolean => {
  if (inicio && fim) {
    const dataInicial = inicio instanceof Date ? inicio : new Date(inicio);
    const dataFinal = fim instanceof Date ? fim : new Date(fim);

    const anoDataInicial = dataInicial.getFullYear();
    const mesDataInicial = dataInicial.getMonth();
    const diaDataInicial = dataInicial.getUTCDate();

    const anoDataFinal = dataFinal.getFullYear();
    const mesDataFinal = dataFinal.getMonth();
    const diaDataFinal = dataFinal.getUTCDate();

    const mesIgual = mesDataFinal === mesDataInicial;
    const anoIgual = anoDataFinal === anoDataInicial;

    return checkIfSomeItemsAreTrue([
      anoDataInicial > anoDataFinal,
      checkIfAllItemsAreTrue([
        mesIgual,
        anoIgual,
        diaDataInicial >= diaDataFinal,
      ]),
      checkIfAllItemsAreTrue([anoIgual, mesDataFinal < mesDataInicial]),
    ]);
  }

  return false;
};
