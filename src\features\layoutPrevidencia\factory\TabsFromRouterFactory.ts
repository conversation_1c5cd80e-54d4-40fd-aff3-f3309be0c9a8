import { tryGetValueOrDefault } from '@cvp/utils';
import router from '@src/corporativo/routes/Router';
import { CONSULTAS_COMPONENTES_PERMISSOES } from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';
import { verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias } from '@src/corporativo/utils/matrizAcesso';
import {
  TRoute,
  TTabsFromRouterFactory,
} from '@src/features/layoutPrevidencia/types/tabsTypes';

const tabsFromRouterFactory = (
  permissoesMatrizAcesso: string[],
): TTabsFromRouterFactory => {
  const firstRoute = router.routes[0];

  if (!firstRoute?.children || firstRoute.children.length === 0) {
    return [];
  }

  const filtraTabsPorMatrizAcesso = (route: TRoute) => {
    const routePath = route.path.replace('-', '_');

    if (!Object.keys(CONSULTAS_COMPONENTES_PERMISSOES).includes(routePath))
      return true;

    const routePathpermissoesNecessarias =
      CONSULTAS_COMPONENTES_PERMISSOES[routePath].permissions;

    return verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      permissoesMatrizAcesso,
      routePathpermissoesNecessarias,
    );
  };

  const todasRotas = firstRoute.children.filter(
    (route): route is TRoute => !!route.path,
  );

  const tabsMapper = (routeTab: TRoute) => {
    const value = routeTab.path;
    const label = tryGetValueOrDefault([routeTab.id], 'Rota Sem Título')
      .split('-')
      .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return {
      value,
      label,
    };
  };

  return todasRotas.filter(filtraTabsPorMatrizAcesso).map(tabsMapper);
};

export default tabsFromRouterFactory;
