import {
  IEnderecoParticipanteResponse,
  ITelefoneParticipanteResponse,
} from '@src/corporativo/types/dadosParticipante';

export interface IAtualizarDadosParticipanteResponse {
  operacao: string;
  numeroCertificado: string;
  opcaoPagamento: string;
  diaDebito: string;
  numeroCartao: string;
  usuario: string;
  canalVendas: string;
  pontoVenda: string;
  codigoUsuario: string;
  numeroMatricula: string;
  codigoCliente: string;
  agenciaContaDebito: string;
  operacaoContaDebito: string;
  numeroContaDebito: string;
  digitoContaDebito: string;
  email: string;
  dadosEnderecoParticipante: IEnderecoParticipanteResponse;
  dadosTelefoneParticipante: ITelefoneParticipanteResponse;
}
