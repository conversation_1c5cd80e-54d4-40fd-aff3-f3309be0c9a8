export { useFormik } from 'formik';
export { useCallback, useContext, useEffect, useMemo, useState } from 'react';

export {
  Match,
  SwitchCase,
  type IHandleReponseResult,
} from '@cvp/componentes-posvenda';
export {
  Button,
  ConditionalRenderer,
  Dialog,
  Grid,
  GridItem,
  IconInfoOutlined,
  InputText,
  LoadingSpinner,
  Select,
  Table,
  Text,
  ToolTip,
} from '@cvp/design-system-caixa';
export type { TColumn } from '@cvp/design-system-caixa/dist/atoms/Table/Table.types';
export type { TableColumn } from '@cvp/design-system-caixa/dist/types/table-types';
export { Alerta } from '@src/corporativo/components/Alerta';
export type { TRowData } from '@src/corporativo/types/beneficiarios';
export * as Yup from 'yup';

export {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  tryGetMonetaryValueOrDefault,
  tryGetValueOrDefault,
} from '@cvp/utils';
export { default as FormatarDataHora } from '@cvp/utils/Formatters/DateTime/FormatarDataHora';

export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
export { default as useConsultarRegimeTributario } from '@src/corporativo/infra/simulacaoDeRenda/useConsultarRegimeTributario';
export { default as useObterTipoDeRenda } from '@src/corporativo/infra/simulacaoDeRenda/useObterTipoDeRenda';
export { default as useSimularRenda } from '@src/corporativo/infra/simulacaoDeRenda/useSimularRenda';
export { default as useVerUltimasSimulacoes } from '@src/corporativo/infra/simulacaoDeRenda/useVerUltimasSimulacoes';
export type { IUseVerUltimasSolicitacoesResponse } from '@src/corporativo/types/consulta/IVerUltimasSolicitacoes';

export {
  ALERTAS_CONSULTA,
  CAMPO_OBRIGATORIO,
  CAMPO_VAZIO,
  HABILITADO_PARA_ALTERAR_REGIME,
  INITIAL_VALUE_INCOME,
  INITIAL_VALUES_FORMIK_RENDA_VITALICIA,
  LABEL_SELECT_RENDA,
  LABEL_SELECT_RENDA_VITALICIA,
  NORMALIZE_STRING,
  PORCENTAGEM_REVERSIVEL_BENEFICIARIO,
  PRAZO_TIPO_DE_RENDA,
  RENDA_SIMULADA_ABAIXO_DO_PREVISTO,
  SEXO_BENEFICIARIO,
  TEXTS_MODAL_CERTIFICADO_NAO_ELEGIVEL,
  TIPO_DE_RENDA,
} from '@src/features/consulta/consultarSimulacaoDeRenda/constants/fluxoDeConsulta';
export { REGEX_SIMULACAO_RENDA } from '@src/shared/constants/regex';

export {
  BENEFICIARIOS_RECEBERAO,
  DATA_SIMULACAO,
  DESCONTO_IR,
  RENDA_MENSAL_BRUTA,
  RENDA_MENSAL_LIQUIDA,
  RESERVA_CONSIDERADA,
  TEMPO_PARA_RECEBER,
  TIPO_RENDA,
} from '@src/features/consulta/consultarSimulacaoDeRenda/constants/tableTexts';
export {
  ALIQUOTA_PROGRESSIVA_TOOLTIP,
  ALIQUOTA_REGRESSIVA_TOOLTIP,
} from '@src/features/consulta/consultarSimulacaoDeRenda/constants/tooltipTexts';

export { AlertsTable } from '@src/features/consulta/consultarSimulacaoDeRenda/components/AlertsTable/AlertsTable';
export { FooterSimulacaoDeRenda } from '@src/features/consulta/consultarSimulacaoDeRenda/components/FooterSimulacaoDeRenda/FooterSimulacaoDeRenda';
export { FooterConsulta } from '@src/features/consulta/consultarSimulacaoDeRenda/components/FooterSimulacaoDeRenda/styles';
export { LoadingTable } from '@src/features/consulta/consultarSimulacaoDeRenda/components/LoadingTable/LoadingTable';
export { RendaVitaliciaReversivelBeneficiario } from '@src/features/consulta/consultarSimulacaoDeRenda/components/RendaVitaliciaReversivelBeneficiario/RendaVitaliciaReversivelBeneficiario';
export { ContainerRendaVitaliciaReversivelBeneficiario } from '@src/features/consulta/consultarSimulacaoDeRenda/components/RendaVitaliciaReversivelBeneficiario/styles';
export { SelectTypeRenda } from '@src/features/consulta/consultarSimulacaoDeRenda/components/SelectTypeRenda/SelectTypeRenda';
export { SimulacaoDeRendaTemporariaPrazoCerto } from '@src/features/consulta/consultarSimulacaoDeRenda/components/SimulacaoDeRendaTemporariaPrazoCerto/SimulacaoDeRendaTemporariaPrazoCerto';
export { ContainerSimulacaoTemporaria } from '@src/features/consulta/consultarSimulacaoDeRenda/components/SimulacaoDeRendaTemporariaPrazoCerto/styles';
export { CelulaData } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TableCelulaConsulta/styles';
export { TabelaCelulaConsulta } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TableCelulaConsulta/TableCelulaConsulta';
export { TableDataConsulta } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TableDataConsulta/TableDataConsulta';
export { TableDataUltimasSolicitacoes } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TableDataUltimasSimulacoes/TableDataUltimasSimulacoes';
export { TooltipTableProgressiva } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TooltipTableProgressiva/TooltipTableProgressiva';
export { TooltipTableRegressiva } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TooltipTableRegressiva/TooltipTableRegressiva';
export { COLUMNS_SIMULACAO_DE_RENDA } from '@src/features/consulta/consultarSimulacaoDeRenda/constants/columnsRenda';
export {
  COLUMNS_TABLE_TOOLTIP,
  DATA_TABLE_TOOLTIP,
} from '@src/features/consulta/consultarSimulacaoDeRenda/constants/columnsTableTooltip';
export { COLUMNS_ULTIMAS_SIMULACOES } from '@src/features/consulta/consultarSimulacaoDeRenda/constants/columnsUltimasSimulacoes';
export { formatarDadosTabelaSimulacaoDeRenda } from '@src/features/consulta/consultarSimulacaoDeRenda/factories/formatarDadosTabelaSimulacaoDeRenda';
export { formatarDadosTabelaUltimasSolicitacoes } from '@src/features/consulta/consultarSimulacaoDeRenda/factories/formatarDadosTabelaUltimasSolicitacoes';
export { useConsultarRenda } from '@src/features/consulta/consultarSimulacaoDeRenda/hooks/useSimularRenda';
export { useVerUltimasSimulacoesDeRenda } from '@src/features/consulta/consultarSimulacaoDeRenda/hooks/useVerUltimasSimulacoesDeRenda';
export { AlertsTablePersonalizado } from '@src/features/consulta/consultarSimulacaoDeRenda/styles/index';
export type { IFooterSimulacaoDeRendaProps } from '@src/features/consulta/consultarSimulacaoDeRenda/types/IFooter';
export type {
  IModalCertificadoNaoElegivel,
  IRendaVitaliciaReversivelBeneficiario,
  ISelectItem,
  ISelectSimulacaoDeRendaProps,
  ISelectTypeRenda,
  ISimulacaoDeRendaResponse,
  ISimulacaoDeRendaTemporariaPrazoCerto,
  ISimulacaoPayload,
  ISimulacaoRow,
  ITabelaCelulaConsulta,
  IUltimasSimulacoesRow,
  IUseConsultarRenda,
  IUseVerUltimasSimulacoesDeRendaReturn,
  TSimulacaoPayloadMap,
  TSimulacaoPayloadValues,
} from '@src/features/consulta/consultarSimulacaoDeRenda/types/ISimulacaoDeRenda';
export type {
  IAletsTable,
  ILoadingTable,
  ITableDataConsulta,
  ITableDataUltimasSolicitacoes,
  ITooltipTableRow,
} from '@src/features/consulta/consultarSimulacaoDeRenda/types/ITable';
export * as Styles from '../styles';

export {
  textSmulationRendaTemporariaPrazoCerto,
  validarPrazoSchema,
  validarReversivelAoBeneficiarioSchema,
  validateAndNormalizeString,
} from '@src/features/consulta/consultarSimulacaoDeRenda/validators/simulacaoValidatorYup';
export { ModalCertificadoNaoElegivel } from '@src/features/consulta/consultarSimulacaoDeRenda/views/ModalCertificadoNaoElegivel';

export { TableConsulta } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TableDataConsulta/styles';
export { TableUltimasSimulacoes } from '@src/features/consulta/consultarSimulacaoDeRenda/components/TableDataUltimasSimulacoes/styles';
