import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ContribuicaoRegular = ({
  fundosContribuicaoRegular,
  deveRenderizarTabelaContribuicao,
}: Resgate.IContribuicaoRegularProps): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  return (
    <Resgate.ConditionalRenderer condition={deveRenderizarTabelaContribuicao}>
      <Resgate.GridItem xs="1">
        <Resgate.Text variant="text-standard-400">
          {Resgate.OBSERVACAO_CONTRIBUICAO_REGULAR(
            Resgate.tryGetMonetaryValueOrDefault(
              resgateFeatureData?.contribuicaoRegular
                ?.valorContribuicaoRegularlAtual,
            ),
          )}
        </Resgate.Text>
        <Resgate.ResgateTable
          themeTable="cvp-05"
          highlightOnHover
          striped
          columns={Resgate.COLUNAS_CONTRIBUICAO_REGULAR}
          data={fundosContribuicaoRegular}
          noDataComponent={Resgate.TABELA_SEM_DADOS}
        />
      </Resgate.GridItem>
    </Resgate.ConditionalRenderer>
  );
};
