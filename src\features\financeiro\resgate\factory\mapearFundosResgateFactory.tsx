import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Mapear dados de fundos para o formato adequado à exibição na interface de resgate
 *
 * Esta função transforma os dados brutos dos fundos em componentes visuais interativos,
 * incluindo checkboxes para seleção, componentes de perfil de risco, campos de entrada
 * para valores de resgate e formatação de valores monetários e percentuais.
 *
 * @param {Object} params - Parâmetros para o mapeamento dos fundos
 * @param {Array} params.fundos - Lista de fundos disponíveis para resgate
 * @param {Function} params.selecionarFundoParaResgate - Função para manipular a seleção de um fundo
 * @param {Function} params.alterarValorResgatado - Função para manipular alteração do valor a ser resgatado
 * @param {boolean} params.isTipoResgateTotal - Indica se o tipo de resgate é total ou parcial
 * @param {number} params.valorMinimoResgate - Valor mínimo permitido para resgate
 * @returns {IFundosParaResgateFactory[]} Lista de fundos formatados com componentes visuais para exibição na interface
 */
export const mapearFundosResgateFactory = ({
  fundos,
  selecionarFundoParaResgate,
  alterarValorResgatado,
  isTipoResgateTotal,
  valorMinimoResgate,
}: Resgate.IMapearFundosResgateFactory): Resgate.IFundosParaResgateFactory[] => {
  return fundos.map(fundo => {
    return {
      ...fundo,
      selecionar: (
        <Resgate.Checkbox
          name="selecionar"
          variant="outlineBlack"
          key={`checkbox-${fundo.codigoFundo}`}
          checked={fundo.selecionado}
          disabled={isTipoResgateTotal}
          onChange={() => selecionarFundoParaResgate(fundo.codigoFundo)}
        />
      ),
      perfilFundo: (
        <Resgate.PerfilDoRisco
          key={`perfil-${fundo.codigoFundo}`}
          perfil={fundo.perfilFundo}
        />
      ),
      valorRentabilidadeDozeMeses: Resgate.porcentagem.mask(
        Resgate.tryGetValueOrDefault(
          [fundo.valorRentabilidadeDozeMeses.toString()],
          '',
        ),
      ),
      saldoDisponivel: Resgate.tryGetMonetaryValueOrDefault(
        fundo.saldoDisponivel,
      ),
      taxaAdm: Resgate.TAXA_ADM,
      valorResgatado: (
        <Resgate.InputValorResgatado
          key={`input-${fundo.codigoFundo}`}
          fundo={fundo}
          onChange={alterarValorResgatado}
          isTipoResgateTotal={isTipoResgateTotal}
          valorMinimoResgate={valorMinimoResgate}
        />
      ),
    };
  });
};
