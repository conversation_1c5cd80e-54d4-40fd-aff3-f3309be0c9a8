import {
  IDadosFundosAporte,
  IItensExtrato,
  TColumn,
} from '@src/features/financeiro/aporte/exports';

export const columnsTabelaFundosDistribuicaoFactory: TColumn[] = [
  {
    name: 'Selecionar',
    selector: (row: IDadosFundosAporte) => row.selecionado,
    wrap: true,
    grow: 0,
    center: true,
    compact: true,
  },
  {
    name: 'Fundo',
    selector: (row: IDadosFundosAporte) => row.descricaoFundo,
    grow: 0,
    wrap: true,
    compact: true,
    center: true,
  },
  {
    name: 'Perfil de Risco',
    selector: (row: IDadosFundosAporte) => row.descPerfilFundo,
    compact: true,
  },
  {
    name: 'Rentabilidade (Últimos 12 Meses)',
    selector: (row: IDadosFundosAporte) => row.rentUlt12Meses,
    sortable: true,
    compact: true,
    center: true,
  },
  {
    name: 'Taxa adm',
    selector: (row: IItensExtrato) => row.taxaAdministrativa,
    sortable: true,
    compact: true,
    center: true,
  },
  {
    name: '<PERSON><PERSON>',
    selector: (row: IDadosFundosAporte) => row.saldo,
    sortable: true,
    compact: true,
    center: true,
  },
  {
    name: 'Valor do Aporte',
    selector: (row: IDadosFundosAporte) => row.valorContribuicao,
    compact: true,
    center: true,
  },
];
