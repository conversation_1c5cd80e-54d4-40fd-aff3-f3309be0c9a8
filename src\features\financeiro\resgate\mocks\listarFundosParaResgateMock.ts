export const listarFundosParaResgateMock = {
  sucesso: true,
  mensagem: '',
  dataExecucao: '05/05/2025',
  horaInicioExecucao: '16:54:56',
  tempoExecucaoMilisegundos: '0',
  stackTrace: null,
  dados: {
    entidade: {
      numeroResgate: '37867026',
      indicadorPermiteResgate: true,
      indicadorPrazoDeDiferimentoExpirado: false,
      indicadorPermiteAssinatura: true,
      aliquota: {
        indicadorPermiteEditarAliquota: true,
        aliquotaAtual: 'R',
        opcoesAliquotas: [
          {
            descricaoAliquota: 'Regressiva',
            codigoAliquota: 'R',
          },
          {
            descricaoAliquota: 'Progressiva',
            codigoAliquota: 'P',
          },
        ],
      },
      saldo: {
        saldoTotal: 509433.74,
        saldoTotalBloqueado: 0.0,
        saldoDisponivelParaResgate: 509433.74,
      },
      limitesCertificado: {
        valorMinimoResgate: 50,
        valorMaximoResgate: 509433.74,
        valorMinimoPermanencia: 5000,
      },
      fundosDisponiveis: [
        {
          permiteResgate: true,
          codigoReserva: '1001',
          descricaoReserva: 'SALDO PMBAC',
          codigoFundo: '1002',
          descricaoFundo: 'CAIXA FIC PREV 100 RF',
          saldoTotal: 509433.74,
          saldoDisponivel: 509433.74,
          saldoBloqueado: 0.0,
          valorMinimoPermanencia: 5000,
          valorRentabilidadeDozeMeses: 35.78,
          perfilFundo: 'Conservador',
          riscoFundo: 'Baixo',
          exibeMensagem: false,
          mensagensFundo: [],
          carencia: null,
        },
      ],
      mensagensCertificado: [
        {
          tipo: 'INFORMACAO',
          codigo: 'SALDO_DISP',
          descricao: 'Certicado disponível para resgate',
        },
      ],
    },
    sucesso: true,
    mensagens: [
      {
        codigo: 'INFRPSLFP0',
        descricao:
          'Consulta lista de fundos para resgate executado com sucesso.',
      },
    ],
  },
};
