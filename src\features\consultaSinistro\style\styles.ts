import styled from 'styled-components';

export const ButtonDetalhesStyled = styled.button`
  align-items: center;
  background: none;
  border: none;
  display: flex;
  width: 100%;
  padding: 0;
  margin: 0;
`;

export const GoBackButtonContainerStyled = styled.div`
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: 25px;
`;

export const ContainerSinistrosStyled = styled.div`
  container-type: inline-size;
`;

export const SinistrosGridStyled = styled.div`
  box-shadow: 4px 4px 8px 3px rgba(0, 0, 0, 0.15),
    0px 1px 3px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding: 24px 12px 56px;
  background-color: #fff;
  gap: 12px;
  > div {
    height: max-content;
  }
  @container (min-width: 580px) {
    padding: 24px;
    padding-bottom: 56px;

    gap: 24px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  @container (min-width: 980px) {
    grid-template-columns: repeat(3, 1fr);
  }
  @container (min-width: 1148px) {
    grid-template-columns: repeat(4, 1fr);
  }
`;

export const CardSinistroStyled = styled.div`
  padding: 24px;
  padding-left: 28px;
  padding-right: 32px;
  height: max-content;

  & > div:first-child {
    padding: 0;
  }
`;

export const SinistroInfoStyled = styled.div`
  margin-top: 14px;
  display: flex;
  align-items: flex-end;
  gap: 2.625rem;
`;

export const SinistroInfoColumnStyled = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

export const SinistroInfoDataStyled = styled.div`
  padding: 2px;
  padding-right: 5px;
`;
