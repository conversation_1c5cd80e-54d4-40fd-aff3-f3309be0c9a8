import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

export const useServiceAlterarDadosPagamento = () => {
  const contaHandler = DadosPagamento.useAlterarConta();

  const {
    dadosListaContasBancarias,
    loadingDadosListaContasBancarias,
    obterListaContasBancarias,
  } = DadosPagamento.useListarContasBancarias();

  const {
    dadosCertificado,
    loadingDadosCertificado,
    consultarCertificadoPorCpf,
  } = DadosPagamento.useConsultaCertificadosPorCpf();

  const dadosCertificadoPorCpf = DadosPagamento.tryGetValueOrDefault(
    [dadosCertificado?.[0]],
    null,
  );

  const { dadosRecuperacaoContribuicoesCertificado } =
    DadosPagamento.useRecuperarContribuicoesCertificado();

  const {
    atualizarFormaPagamento,
    loadingDadosAtualizacaoFormaPagamento,
    responseAtualizacaoFormaPagamento,
    setResponse: setResponseAtualizarFormaPagamento,
  } = DadosPagamento.useAtualizarFormaPagamento({
    canalId: dadosCertificadoPorCpf?.codigoCanalRecuperacao,
    tipoContribuicao:
      dadosRecuperacaoContribuicoesCertificado
        ?.beneficioContribuicaoCertificado[0]?.tipoContribuicao,
  });

  const {
    atualizarDiaVencimento,
    loadingAtualizaDiaVencimento,
    responseAtualizaDiaVencimento,
    setResponse: setResponseAtualizaDiaVencimento,
  } = DadosPagamento.useAtualizarDataVencimento({
    dataVencimento: contaHandler.novaConta?.diaVencimento,
  });

  const {
    validarConta,
    isLoadingValidacaoConta,
    responseValidacaoConta,
    setResponse: setResponseValidarConta,
  } = DadosPagamento.useValidarConta();

  const deveAtualizarDiaVencimento = DadosPagamento.checkIfAllItemsAreTrue([
    !!contaHandler.novaConta.diaVencimento.length,
    contaHandler.novaConta.diaVencimento !== dadosCertificado?.[0].diaPagamento,
  ]);

  const deveAtualizarFormaPagamento = DadosPagamento.checkIfAllItemsAreTrue([
    contaHandler.contaExistente !== DadosPagamento.TEXT_NOVA_CONTA,
    !!contaHandler.contaExistente.length,
  ]);

  const deveAtualizarDadosPagamento = DadosPagamento.checkIfSomeItemsAreTrue([
    contaHandler.deveValidarConta,
    deveAtualizarDiaVencimento,
    deveAtualizarFormaPagamento,
  ]);

  const atualizarDiaVencimentoSeNecessario = async (): Promise<void> => {
    if (deveAtualizarDiaVencimento) await atualizarDiaVencimento();
  };

  const validarContaSeNecessario = async (): Promise<void> => {
    if (contaHandler.deveValidarConta) {
      await validarConta({
        codigoAgencia: contaHandler.novaConta.numeroAgencia,
        codigoOperacao: contaHandler.novaConta.operacao,
        digitoVerificador: contaHandler.novaConta.digitoConta,
        numeroBanco: DadosPagamento.NUMERO_BANCO_CAIXA,
        numeroConta: contaHandler.novaConta.numeroConta,
      });
    }
  };

  const atualizarFormaPagamentoSeNecessario = async (): Promise<void> => {
    if (deveAtualizarFormaPagamento) await atualizarFormaPagamento();
  };

  const handleAtualizarDadosPagamento = async (): Promise<void> => {
    await atualizarDiaVencimentoSeNecessario();
    await validarContaSeNecessario();
    await atualizarFormaPagamentoSeNecessario();
    await obterListaContasBancarias();
    await consultarCertificadoPorCpf();
    contaHandler.resetNovaContaPorSucesso({
      retornoValidarConta: !!responseValidacaoConta?.entidade?.codigoRetorno,
      retornoAtualizacaoFormaPagamento: responseAtualizacaoFormaPagamento!,
      retornoAtualizaDiaVencimento: responseAtualizaDiaVencimento!,
    });
  };

  const resetarMensagens = (): void => {
    const retornaResponseSemMessages = <T>(response: T) => {
      return {
        ...response,
        mensagens: [],
      };
    };

    setResponseAtualizarFormaPagamento(retornaResponseSemMessages);
    setResponseAtualizaDiaVencimento(retornaResponseSemMessages);
    setResponseValidarConta(retornaResponseSemMessages);
  };

  return {
    ...contaHandler,
    responseAtualizaDiaVencimento,
    responseAtualizacaoFormaPagamento,
    responseValidacaoConta,
    dadosListaContasBancarias,
    obterListaContasBancarias,
    dadosCertificado,
    consultarCertificadoPorCpf,
    handleAtualizarDadosPagamento,
    resetarMensagens,
    deveAtualizarDadosPagamento,
    loading: DadosPagamento.checkIfSomeItemsAreTrue([
      loadingAtualizaDiaVencimento,
      loadingDadosAtualizacaoFormaPagamento,
      isLoadingValidacaoConta,
      loadingDadosListaContasBancarias,
      loadingDadosCertificado,
    ]),
  };
};
