export { default as React } from 'react';
export { useNavigate } from 'react-router-dom';
export {
  <PERSON><PERSON>,
  LoadingSpinner,
  Card,
  Separator,
  Timeline,
  Text,
  Grid,
  GridItem,
  IconExpandMoreSharp,
  IconExpandLessSharp,
  Svg,
  Alert,
  IconInfoRound,
  IconWarningRound,
} from '@cvp/design-system-caixa';
export type { ITimelineProps } from '@cvp/design-system-caixa/dist/molecules/Timeline/Timeline.types';
export type { ISvgProps } from '@cvp/design-system-caixa/dist/atoms/Svg/Svg.types';

export {
  For,
  SwitchCase,
  Match,
  LayoutPlataforma,
} from '@cvp/componentes-posvenda';

export {
  formatarDataHoraAmigavel,
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
} from '@cvp/utils';

export { useListarSinistro } from '@src/corporativo/infra/consultaSinistro/useConsultaSinistro';

export { default as FormatarDataHora } from '@cvp/utils/Formatters/DateTime/FormatarDataHora';

export type {
  IDadosSinistroResponse,
  IDadosSinistroEntidade,
} from '@src/corporativo/types/consultaSinistro/IDadosSinistroResponse';

export { novaDataFormatada } from '@src/corporativo/types/shared/formatDate';

export { LOADING } from '@src/shared/constants/api';

export { default as previdencia } from '@src/corporativo/constants/PrevidenciaConstants';
export { REDIRECT_BUTTONS } from '@src/shared/factory/botoesRedirect';
export { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';

export * as S from '../style/styles';
export { default as CardSinistro } from '../views/SinistroCard';
export { DetalheSinistro } from '../views/SinistroDetalhe';
export { IconSinistro } from '../views/SinistroIcon';
export { InfoSinistro } from '../views/SinistroInfo';
export { DetalhesSinistro } from '../views/SinistroDetalhes';
export type { TDetalheSinistroProps } from '../types/TDetalheSinistroProps';
export type { TSinistroInfoProps } from '../types/TSinistroInfoProps';
