import { useEffect } from 'react';
import { Alert } from '@cvp/design-system-caixa';
import { useAlertaConsulta } from '@src/shared/hooks/useAlerta';
import * as S from './styles';

const AlertaPrevidencia: React.FC<{
  identificador: string;
}> = ({ identificador }) => {
  const { alerta, callbackAlerta } = useAlertaConsulta(identificador);

  useEffect(() => {
    if (!alerta || alerta.withoutTimeout) return;
    setTimeout(() => {
      callbackAlerta();
    }, alerta.duration ?? 7000);
  }, [callbackAlerta, alerta]);

  if (alerta === null) return null;
  return (
    <S.AlertaApoliceContainer visible={alerta !== null}>
      <Alert icon={alerta?.icon} variant={alerta?.variant}>
        {alerta?.message}
      </Alert>
    </S.AlertaApoliceContainer>
  );
};

export default AlertaPrevidencia;
