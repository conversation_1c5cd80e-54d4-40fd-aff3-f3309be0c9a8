import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import {
  IResponseFundo,
  IResponseFundosPorCertificado,
} from '@src/corporativo/types/consultaCertificado/Response/IFundosPorCertificadoResponse';

const RETORNO_VAZIO = {} as IResponseFundosPorCertificado;
const RETORNO_VAZIO_FUNDOS = [] as IResponseFundo[];

export type TUseConsultaFundosPorCertificado = {
  loading: boolean;
  response: IResponseFundosPorCertificado;
  fundos: IResponseFundo[];
  vlrMinTransferencia?: number;
  fetchData: (numCertificado: string) => Promise<void>;
};

type TPayload = {
  cpf: string | null;
};

function useConsultaFundosPorCertificado(): TUseConsultaFundosPorCertificado {
  const payload = {
    cpf: getSessionItem('cpfCnpj'),
  };

  const { invocarApiGatewayCvpComToken, response, loading } =
    useApiGatewayCvpInvoker<TPayload, IResponseFundosPorCertificado>(
      'PECO_ObterInformacoesCertificado',
      {
        data: { cpf: String(payload.cpf) },
        autoFetch: false,
      },
    );

  const consultaFundos = async (numCertificado: string) => {
    await invocarApiGatewayCvpComToken({
      codContaOrigem: numCertificado,
    });
  };

  const retornoCertificado = tryGetValueOrDefault(
    [response?.entidade?.retornoCertificado],
    {} as IResponseFundosPorCertificado['retornoCertificado'],
  );

  return {
    response: tryGetValueOrDefault([response?.entidade], RETORNO_VAZIO),
    fundos: tryGetValueOrDefault(
      [retornoCertificado?.certificadoOrigem?.fundos],
      RETORNO_VAZIO_FUNDOS,
    ),
    vlrMinTransferencia: tryGetValueOrDefault(
      [Number(retornoCertificado?.vlrMinTransferencia)],
      0,
    ),
    loading,
    fetchData: consultaFundos,
  };
}

export default useConsultaFundosPorCertificado;
