# APP CONFIG

REACT_APP_CLIENT_ID =
REACT_APP_NOME_ACESSO_STORE_KEY = nomeAcesso
REACT_APP_BEARER_TOKEN_STORE_KEY = token
REACT_APP_USER_METADATA_STOREY_KEY = metadata
REACT_APP_CACHE_DURATION = 3000

# APP API URLS

REACT_APP_API_BASE_URL = https://integracaodev.caixavidaeprevidencia.intranet/api

#AMBIENTE
REACT_APP_PE_ENV = development
#END AMBIENTE

# CHAT SALESFORCE URLS
REACT_APP_CHAT_BASE_URL = https://caixavidaeprevidencia.my.salesforce-sites.com

#CHAT WEBCHAT URL
REACT_APP_WEBCHAT_BASE_URL = https://caixavidaeprevidencia.my.salesforce-sites.com

REACT_APP_NOME_MFE = PlataformaCaixa-PosVenda-Previdencia

REACT_APP_MFE_ASSINATURA_NAME = caixa-sipnc-assinaturas-des

REACT_APP_INSIGHTS_CONNECTION_STRING = InstrumentationKey=913fe511-4c1f-4875-8f26-802ed4580068;IngestionEndpoint=https://brazilsouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://brazilsouth.livediagnostics.monitor.azure.com/;ApplicationId=af5ea92c-5404-4dfe-a96f-47e08bcb3e65
