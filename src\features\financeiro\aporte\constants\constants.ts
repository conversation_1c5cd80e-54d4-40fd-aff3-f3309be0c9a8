import { IFiltroTabelaAporte } from '../exports';

export const FILTRO_APORTE = {
  contribuicoes:
    'Para alcance dos objetivos financeiros o cliente poderá fazer contribuições adicionais quando quiser.',
  valorDaContribuicao: 'Valor da contribuição',
  selectFormaPagamento: 'Qual será a forma de pagamento?',
  selectPerfilFundoInvestimento: 'Perfil do fundo de investimento?',
  boleto: 'Boleto bancário',
  debito: 'Débito em conta',
  dataDebito: 'Data do débito',
  defaultValorContribuicao: 'R$ 5.000,00',
  parametroDataPagamento: '5',
};

export const PERFIS = {
  conservador: 'Conservador',
  moderado: 'Moderado',
  arrojado: 'Arrojado',
  agressivo: 'Agressivo',
};

export const ALERTS = {
  importante_boleto:
    'Importante! Oriente o cliente a pagar o boleto até a data de vencimento.',
  importante_debito:
    'Importante! Caso a data selecionada seja feriado, o débito será realizado no próximo dia útil.',
  sucesso_aporte_boleto: 'Aporte adicional solicitado com sucesso.  ',
  sucesso_aporte_debito:
    'Aporte adicional solicitado com sucesso. Oriente o cliente a ter saldo na conta na data do débito.',
};

export const FUNDOS = {
  existentes: 'Fundos existentes',
  fundosNovos: 'Novos fundos',
  fundosExistentesIndisponiveis: 'Não há fundos existentes disponiveis',
  fundosNovosIndisponiveis: 'Não há fundos novos disponiveis',
  total: 'Total dos fundos selecionados:',
  distribuicao: 'Adicione um valor de distribuição',
  distribuicaoPlaceholder: 'R$ 0,00',
  restante: 'Restante:',
};

export const TIPO_FUNDO = {
  novo: 'novo',
  existente: 'existente',
};

export const MODAL_APORTE = {
  cabecalho: 'Aporte Adicional',
  titulo: 'Confirmar dados do cliente  para o aporte adicional',
  titular: 'Titular',
  certificado: 'Certificado',
  formaPagamento: 'Forma de pagamento',
  valor: 'Valor',
  modalidade: 'Modalidade / Regime Tributário',
};

export const ORIGEM_RECURSOS = [
  {
    id: '0',
    descricao: 'Aplicações ﬁnanceiras',
  },
  {
    id: '01',
    descricao: 'Herança familiar',
  },
  {
    id: '02',
    descricao: 'Economias',
  },
  {
    id: '03',
    descricao: 'Participação de Lucros',
  },
  {
    id: '04',
    descricao: 'Indenização',
  },
  {
    id: '05',
    descricao: 'Prêmio de Loteria',
  },
  {
    id: '06',
    descricao: 'Premiação de Sorteios',
  },
  {
    descricao: 'Venda de Imóvel',
    id: '07',
  },
  {
    id: '08',
    descricao: 'Venda de Veículo',
  },
  {
    descricao: 'Obtenção de empréstimo ou financiamento',
    id: '09',
  },
  {
    descricao: 'Não desejo informar',
    id: '10',
  },
  {
    descricao: 'Outros:',
    id: '11',
  },
];

export const ORIGEM_RECURSOS_DECLARACAO = {
  declaracaoTitulo: 'Declaração de Origem dos Recursos',
  declaracaoOrigem: 'Qual a origem do recurso para esse aporte?',
  desejoNaoInformar: 'Não desejo informar',
  declaracaoNaoInforma:
    'A falta de informação sobre a origem de recursos será considerada como resistência ao fornecimento de informações necessárias para apuração de risco. Isso poderá implicar em agravamento do perfil de risco do Cliente. O Cliente está ciente disso?',
  declaracao:
    'De acordo com a Art. 32 e 36 da Circular SUSEP 612/2020 – Nos casos de operações, inclusive propostas de operações, envolvendo pessoas expostas politicamente, seus familiares, representantes, estreitos colaboradores ou pessoas jurídicas de que participem deve ser observado o procedimento de diligências para estabelecer a origem dos recursos, bem como o monitoramento de contribuições de alto valor financeiro.',
};

export const CONTA_DEBITO = {
  titulo: 'Selecione uma conta para que o débito seja feito:',
  label: 'Selecione uma conta para débito',
  conta: 'Conta:*',
  novaConta: 'Indicar uma nova conta',
};

export const NOVA_CONTA_DEBITO = {
  titulo: 'Adicione uma nova conta bancária para que o débito seja feito:',
  tipoOperacao: 'Tipo de Operação:',
  agencia: 'Agência',
  contaBancaria: 'Conta Bancária',
  digito: 'Dígito',
  escolha: 'Escolha a opção',
};

export const BOTOES = {
  voltar: 'Voltar',
  confirmarAporte: 'Confirmar aporte',
  cancelar: 'Cancelar',
  avancar: 'Avançar',
  gerarComprovante: 'Gerar comprovante',
  finalizar: 'Finalizar atendimento',
  select: 'Selecione',
  confirmar: 'Confirmar',
  prosseguir: 'Prosseguir',
  boleto: 'Gerar boleto',
  comprovante: 'Comprovante aporte',
};

export const VALIDACAO_APORTE = {
  nome: 'Informe o primeiro nome do cliente',
  nomeLabel: 'Primeiro nome',
  cpf: 'Informe os 4 últimos dígitos do CPF',
  cpfLabel: 'Últimos dígitos',
};

export const FUNDO_SELECIONADO_INITIAL_STATE = {
  reservaId: '',
  fundoId: '',
  saldo: '',
  descricaoFundo: '',
  valorMinimo: '',
  vlrProporcionalContribuicao: '',
  descPerfilFundo: '',
  rentUlt12Meses: '',
  descRiscoFundo: '',
  percetualProporcionalSaldo: '',
  valorContribuicao: '',
  selecionado: false,
  idSelecionado: '',
  qntMaxFundoPermitida: '',
};

export const FILTRO_TABELA_INITIAL_STATE: IFiltroTabelaAporte = {
  valorContribuicao: '',
  dataDebito: '',
  formaPagamento: '',
  perfilInvestidor: [],
};

export const SHOW_INPUTS_INITIAL_STATE = {
  open: false,
  formaPagamento: '',
};

export const TIPO_OPERACAO = [
  { id: '001', valor: 'Conta Corrente PF' },
  { id: '003', valor: 'Conta Corrente PJ' },
  { id: '013', valor: 'Poupança PF' },
  { id: '022', valor: 'Poupança PJ' },
  { id: '023', valor: 'Conta Caixa Fácil' },
  { id: '1288', valor: 'Poupança PF / Caixa Fácil' },
  { id: '1292', valor: 'Conta Corrente PJ' },
  { id: '3701', valor: 'Conta Corrente PF' },
  { id: '3702', valor: 'Poupança PJ' },
];

export const TIPO_CONTA_BANCO = {
  SIGLA_CONTA_CORRENTE: 'CC',
  SIGLA_CONTA_POUPANCA: 'CP',
};

export const CONTA_BANCO = {
  CONTA_CORRENTE: 'Conta Corrente',
  CONTA_POUPANCA: 'Conta Poupança',
};

export const ENVIO_IMPRESSAO = {
  boleto: 'Boleto',
  comprovante: 'Comprovante de Aporte',
  codigoRequisicao: 'APORTE',
};

export const CONTA_BANCARIA_INITIAL_STATE = {
  tipoPagamentoId: '',
  descricaoPagamento: '',
  canalId: '',
  dataPagamento: '',
  metodoPagamento: '',
  tipoContaBanco: '',
  numeroBanco: '',
  nomeBanco: '',
  numeroAgencia: '',
  digitoAgencia: '',
  numeroConta: '',
  digitoConta: '',
  operacao: '',
  banco: '',
  conta: '',
};

export const NOVA_CONTA_BANCARIA_INITIAL_STATE = {
  operacao: '',
  agencia: '',
  contaBancaria: '',
  digito: '',
};

export const FORMA_PAGAMENTO = { boleto: 'boleto', debito: 'debito' };

export const COMPROVANTE_APORTE = {
  FORMA_PAGAMENTO: 'Forma de Pagamento',
  VALOR_CONTRIBUICAO: 'Valor do Aporte',
  PERFIS: 'Perfil(s) de investimento selecionado(s)',
  BANCO: 'Banco',
  OPERACAO: 'Operação',
  AGENCIA: 'Agência',
  CONTA_BANCARIA: 'Conta Bancária',
  DATA_DEBITO: 'Data de Débito',
};

export const CONFIRMAR_APORTE = {
  confirmacao: 'Você confirma o aporte no certificado',
};

export const LOADING = {
  FILTROS_APORTE: 'Carregando Filtros do Aporte',
  TABELA_FUNDOS: 'Carregando Tabela de Fundos',
  DADOS_BANCARIOS: 'Carregando dados bancários',
};

export const ALERT = {
  FILTROS_APORTE: 'Erro ao carregar filtros do Aporte',
  TABELA_FUNDOS: 'Erro ao carregar fundos de distribuição',
  MODAL_VALIDAR_CAMPOS: 'Erro ao validar campos',
  MODAL_APORTE: 'Erro ao efetuar aporte',
  CONTAS_BANCARIAS: 'Erro ao buscar contas bancárias',
  VALIDAR_CONTA: 'Erro ao validar conta',
  CONTA_INVALIDA: 'Conta bancária inválida',
};

export const VALIDA_CAMPOS_INITIAL_STATE = {
  primeiroNome: '',
  finalCpfCnpj: '',
};

export const ALERT_ERROR = { FUNDOS: 'fundos' };

export const FORMIK_VALUES = {
  DATA_DEBITO: 'dataDebito',
};

export const DIGITO_AGENCIA_DEFAULT = '0';

export const enum enumValidacaoContaBancaria {
  CONTA_BANCARIA_CADASTRADA_VALIDADA = '00',
}

export const ASSINATURA = {
  AUTENTICACAO: 'Autenticação',
  INVALIDA: 'Não foi possível carregar a assinatura.',
};

export const TEXTO_DADOS_BANCARIOS_INITIAL_STATE = { id: '', text: '' };

export enum enumFormaPagamento {
  BOLETO = 'BOLETO',
  DEBITO = 'DEBITO',
  CB = 'CB',
  FC = 'FC',
}

export const enum enumNomeBancos {
  CAIXA_ECONOMICA = 'CAIXA ECONOMICA FEDERAL',
}

export const enum enumNumeroBancos {
  CAIXA_ECONOMICA = '104',
}

export const enum enumTipoFundo {
  DEFAULT = '05',
  Novo = 'novo',
  Existente = 'existente',
}

export const enum enumTipoClientePep {
  TITULAR = 'T',
  RELACIONADO = 'R',
  NENUM = 'null',
}

export const enum enumTipoContaBancaria {
  ORIGEM_DOS_RECURSOS = '99',
  CONTA_BANCARIA_EXISTENTE = '100',
  CONTA_BANCARIA_NOVA = '101',
}
