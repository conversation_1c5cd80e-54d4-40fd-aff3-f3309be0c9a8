import {
  ICriarObjetoEmailSimulacaoResgate,
  IObjetoEmailSimulacaoResgate,
  obterNumeroResgateConsolidado,
  tipoEmailConstants,
} from '@src/features/financeiro/resgate/exports';

/**
 * Criar objeto com os dados necessários para envio de e-mail de simulação de resgate
 *
 * @param {Object} params - Parâmetros necessários para criar o objeto de e-mail
 * @param {Object} params.certificadoAtivo - Dados do certificado ativo do cliente
 * @param {Object} params.resgateFeatureData - Dados da feature de resgate como dadosSelecaoAliquota e resumoAliquotaSelecionada
 * @returns {IObjetoEmailSimulacaoResgate} Objeto formatado para envio de e-mail contendo tipo e parâmetros necessários
 */
export const criarObjetoEmailSimulacaoResgate = ({
  certificadoAtivo,
  resgateFeatureData,
}: ICriarObjetoEmailSimulacaoResgate): IObjetoEmailSimulacaoResgate => {
  return {
    tipoEmail: tipoEmailConstants.SIMULACAO_RESGATE,
    parametrosEnvio: {
      numeroResgate: obterNumeroResgateConsolidado(
        resgateFeatureData?.dadosSelecaoAliquota,
        resgateFeatureData?.resumoAliquotaSelecionada?.tipoAliquota,
      ),
      numeroCertificado: certificadoAtivo?.certificadoNumero,
    },
  };
};
