import * as tabelaImports from '@src/features/consulta/consultarSimulacaoDeRenda/exports';

export const formatarDadosTabelaSimulacaoDeRenda = (
  data: tabelaImports.IHandleReponseResult<tabelaImports.ISimulacaoDeRendaResponse>,
): tabelaImports.ISimulacaoRow[] => {
  const { entidade } = data;

  return [
    {
      label: tabelaImports.DATA_SIMULACAO,
      progressiva: tabelaImports.FormatarDataHora(new Date().toString()),
      regressiva: tabelaImports.FormatarDataHora(new Date().toString()),
      estimativa: tabelaImports.FormatarDataHora(
        entidade?.dthInicioGozoOriginal,
      ),
    },
    {
      label: tabelaImports.RESERVA_CONSIDERADA,
      progressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrReserva,
      ),
      regressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrReserva,
      ),
      estimativa: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrReservaOriginal,
      ),
    },
    {
      label: tabelaImports.RENDA_MENSAL_BRUTA,
      progressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrBeneficio,
      ),
      regressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrBeneficio,
      ),
      estimativa: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrBeneficioOriginal,
      ),
    },
    {
      label: tabelaImports.DESCONTO_IR,
      progressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrIRRF,
      ),
      regressiva: tabelaImports.tryGetMonetaryValueOrDefault(entidade?.vlrIRRF),
      estimativa: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrIRRFOriginal,
      ),
    },
    {
      label: tabelaImports.RENDA_MENSAL_LIQUIDA,
      progressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrBeneficioLiquido,
      ),
      regressiva: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrBeneficioLiquido,
      ),
      estimativa: tabelaImports.tryGetMonetaryValueOrDefault(
        entidade?.vlrBeneficioLiquidoOriginal,
      ),
    },
    {
      label: tabelaImports.TEMPO_PARA_RECEBER,
      progressiva: tabelaImports.tryGetValueOrDefault(
        [entidade?.descPeridoBeneficiarioRecebe],
        tabelaImports.CAMPO_VAZIO,
      ),
      regressiva: tabelaImports.tryGetValueOrDefault(
        [entidade?.descPeridoBeneficiarioRecebe],
        tabelaImports.CAMPO_VAZIO,
      ),
      estimativa: tabelaImports.tryGetValueOrDefault(
        [entidade?.descPeridoBeneficiarioRecebeOriginal],
        tabelaImports.CAMPO_VAZIO,
      ),
    },
    {
      label: tabelaImports.BENEFICIARIOS_RECEBERAO,
      progressiva: tabelaImports.tryGetValueOrDefault(
        [entidade?.descBeneficiarioRecebe],
        tabelaImports.CAMPO_VAZIO,
      ),
      regressiva: tabelaImports.tryGetValueOrDefault(
        [entidade?.descBeneficiarioRecebe],
        tabelaImports.CAMPO_VAZIO,
      ),
      estimativa: tabelaImports.tryGetValueOrDefault(
        [entidade?.descBeneficiarioRecebeOriginal],
        tabelaImports.CAMPO_VAZIO,
      ),
    },
    {
      label: tabelaImports.TIPO_RENDA,
      progressiva: tabelaImports.tryGetValueOrDefault(
        [entidade?.nomTipoPagamento],
        tabelaImports.CAMPO_VAZIO,
      ),
      regressiva: tabelaImports.tryGetValueOrDefault(
        [entidade?.nomTipoPagamento],
        tabelaImports.CAMPO_VAZIO,
      ),
      estimativa: tabelaImports.tryGetValueOrDefault(
        [entidade?.nomTipoPagamentoOriginal],
        tabelaImports.CAMPO_VAZIO,
      ),
    },
  ];
};
