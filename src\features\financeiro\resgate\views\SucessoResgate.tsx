import * as Resgate from '@src/features/financeiro/resgate/exports';

export const SucessoResgate = (): React.ReactElement => {
  const theme = Resgate.useTheme();

  const { resgateFeatureData } = Resgate.useResgateContext();

  const {
    isOpenModalPendencia,
    deveRenderizarMensagemAguardandoAssinatura,
    deveRenderizarMensagemPendente,
    deveRenderizarMensagemSucesso,
    deveRenderizarTabelaResumo,
    controlarModalPendencia,
  } = Resgate.useSucessoResgate();

  const { handleComprovante, isLoadingComprovanteResgate } =
    Resgate.useComprovanteResgate();

  return (
    <Resgate.Grid margin="18" container>
      <Resgate.GridItem xs="1">
        <Resgate.MensagensSucessoResgate
          deveRenderizarMensagemSucesso={deveRenderizarMensagemSucesso}
          deveRenderizarMensagemPendente={deveRenderizarMensagemPendente}
          deveRenderizarMensagemAguardandoAssinatura={
            deveRenderizarMensagemAguardandoAssinatura
          }
        />

        <Resgate.ConditionalRenderer condition={deveRenderizarTabelaResumo}>
          <Resgate.ContainerTabelaResumoResgate>
            <Resgate.Text variant="text-standard-600">
              {Resgate.obterDescricaoAliquota(
                resgateFeatureData.resumoAliquotaSelecionada?.tipoAliquota,
              )}
              <Resgate.TooltipInfoAliquota
                tipoAliquota={
                  resgateFeatureData.resumoAliquotaSelecionada?.tipoAliquota
                }
              />
            </Resgate.Text>
            <Resgate.Text variant="text-standard-400">
              Dados do resgate
            </Resgate.Text>
            <Resgate.TabelaResumoSimulacao />
          </Resgate.ContainerTabelaResumoResgate>
        </Resgate.ConditionalRenderer>

        <Resgate.For
          each={Resgate.tryGetValueOrDefault(
            [Resgate.INFORMATIVO_FINALIZACAO_RESGATE],
            [],
          )}
        >
          {item => (
            <Resgate.Text variant="text-standard-400" marginBottom="10px">
              {item}
            </Resgate.Text>
          )}
        </Resgate.For>
      </Resgate.GridItem>
      <Resgate.GridItem xs="1">
        <Resgate.ContainerButtons>
          <Resgate.Button
            size="standard"
            variant="secondary"
            onClick={handleComprovante}
            disabled={isLoadingComprovanteResgate}
            leftIcon={
              <Resgate.ConditionalRenderer
                condition={isLoadingComprovanteResgate}
              >
                <Resgate.LoadingSpinner
                  size="small"
                  color={theme.color.content.neutral['01']}
                />
              </Resgate.ConditionalRenderer>
            }
          >
            Gerar comprovante
          </Resgate.Button>
        </Resgate.ContainerButtons>
      </Resgate.GridItem>

      <Resgate.ModalPendenciasResgate
        isOpenModalPendencia={isOpenModalPendencia}
        controlarModalPendencia={controlarModalPendencia}
      />
    </Resgate.Grid>
  );
};
