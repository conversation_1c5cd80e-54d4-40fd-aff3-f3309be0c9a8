import * as Resgate from '@src/features/financeiro/resgate/exports';

export const LoaderResgate = ({
  loadingText,
}: Resgate.ILoaderResgateProps): React.ReactElement => {
  const theme = Resgate.useTheme();

  return (
    <Resgate.GridItem xs="1">
      <Resgate.LoadingSpinner
        color={theme.color.brand.primary['06']}
        size="medium"
      >
        {loadingText}
      </Resgate.LoadingSpinner>
    </Resgate.GridItem>
  );
};
