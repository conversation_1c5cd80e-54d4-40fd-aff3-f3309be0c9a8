import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IDadosBuscarEnderecoCepResponse,
  TUseBuscarEnderecoCep,
} from '@src/corporativo/types/dadosParticipante';
import { IDadosBuscarEnderecoCepRequest } from '@src/corporativo/types/dadosParticipante/consultar/IDadosBuscarEnderecoCepResponse';
import { PECOS } from '../config/api/endpoints';

export const useBuscarEnderecoCep: TUseBuscarEnderecoCep = () => {
  const { response, loading, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      Partial<IDadosBuscarEnderecoCepRequest>,
      IDadosBuscarEnderecoCepResponse
    >(PECOS.BuscarEnderecoCep, {
      data: {
        cpfCnpj: getSessionItem('cpfCnpj'),
      },
      autoFetch: false,
    });

  return {
    response: tryGetValueOrDefault([response?.entidade], null),
    buscarEnderecoCep: invocarApiGatewayCvpComToken,
    loading,
  };
};
