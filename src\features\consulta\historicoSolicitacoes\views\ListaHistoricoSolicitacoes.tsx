import * as ListaHistorico from '@src/features/consulta/historicoSolicitacoes/exports';

type TListaHistoricoSolicitacoesProps = {
  dados: ListaHistorico.IResponseHistoricoSolicitacoes[];
  erroPeriodo?: string;
  assinaturaSucessoCallback: VoidFunction;
};

export const ListaHistoricoSolicitacoes: React.FC<
  TListaHistoricoSolicitacoesProps
> = ({ dados, erroPeriodo, assinaturaSucessoCallback }) => {
  const { handleObterComprovante, error, loadingComprovantes } =
    ListaHistorico.useComprovanteSolicitacao();
  const [assinando, setAssinando] = ListaHistorico.useState<
    string | undefined
  >();

  const assinarMovimentacao = (
    row: ListaHistorico.IResponseHistoricoSolicitacoes,
  ): void => {
    setAssinando(row.idRequisicao);
  };

  const paginator = ListaHistorico.usePaginator({ rowsPerPage: 4 });
  const theme = ListaHistorico.useTheme();

  const currentData =
    ListaHistorico.usePaginatedData<ListaHistorico.IResponseHistoricoSolicitacoes>(
      {
        data: dados,
        ...paginator,
      },
    );

  return (
    <>
      <ListaHistorico.ConditionalRenderer
        condition={ListaHistorico.checkIfSomeItemsAreTrue([
          !!error.length,
          !!erroPeriodo,
        ])}
      >
        <ListaHistorico.Alert
          variant="danger-01"
          icon={
            <ListaHistorico.IconWarningRound
              size="big"
              color={theme.color.palette.negative['90']}
            />
          }
        >
          {erroPeriodo ?? error}
        </ListaHistorico.Alert>
      </ListaHistorico.ConditionalRenderer>
      <ListaHistorico.Styles.DataTable
        themeTable="default"
        striped
        columns={ListaHistorico.colunasHistoricoSolicitacoes(
          handleObterComprovante,
          assinarMovimentacao,
          loadingComprovantes,
        )}
        data={currentData}
        noDataComponent={ListaHistorico.TEXTOS.SEM_DADOS}
      />

      <ListaHistorico.ListaHistoricoPaginacao
        dados={dados}
        paginator={paginator}
      />

      <ListaHistorico.AssinaturaSolicitacao
        open={!!assinando}
        assinaturaSucessoCallback={assinaturaSucessoCallback}
      />
    </>
  );
};
