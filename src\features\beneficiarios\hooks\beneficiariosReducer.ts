import { ICertificadoCoberturas } from '../exports';

export enum EBeneficiariosActionKind {
  EDITAR_BENEFICIARIOS = 'EDITAR_BENEFICIARIOS',
  CONFIRMAR_EDICAO_BENEFICIARIOS = 'CONFIRMAR_EDICAO_BENEFICIARIOS',
  ALTERAR_ASSINATURA_VALIDA = 'ALTERAR_ASSINATURA_VALIDA',
  CANCELAR_EDICAO_BENEFICIARIOS = 'CANCELAR_EDICAO_BENEFICIARIOS',
}

export type TBeneficiariosState = {
  editando: boolean;
  confirmandoEdicao: boolean;
  assinaturaValida: boolean;
  coberturasOriginal: ICertificadoCoberturas;
};

export type TBeneficiariosActions =
  | IActionEditarBeneficiarios
  | IActionConfirmarEdicaoBeneficiarios
  | IActionSalvarAssinaturaValida
  | IActionCancelarEdicaoBeneficiarios;

interface IActionEditarBeneficiarios {
  type: EBeneficiariosActionKind.EDITAR_BENEFICIARIOS;
}

interface IActionConfirmarEdicaoBeneficiarios {
  type: EBeneficiariosActionKind.CONFIRMAR_EDICAO_BENEFICIARIOS;
  value?: boolean;
}

interface IActionCancelarEdicaoBeneficiarios {
  type: EBeneficiariosActionKind.CANCELAR_EDICAO_BENEFICIARIOS;
}

interface IActionSalvarAssinaturaValida {
  type: EBeneficiariosActionKind.ALTERAR_ASSINATURA_VALIDA;
  assinaturaValida: boolean;
}

export const beneficiariosInitialState: TBeneficiariosState = {
  editando: false,
  confirmandoEdicao: false,
  assinaturaValida: false,
  coberturasOriginal: {} as ICertificadoCoberturas,
};

export function criaBeneficiariosEstadoInicial(
  coberturasOriginal: ICertificadoCoberturas,
): TBeneficiariosState {
  return {
    ...beneficiariosInitialState,
    coberturasOriginal,
  };
}

export function beneficiariosReducer(
  state: TBeneficiariosState,
  action: TBeneficiariosActions,
): TBeneficiariosState {
  switch (action.type) {
    case EBeneficiariosActionKind.EDITAR_BENEFICIARIOS: {
      return {
        ...state,
        editando: true,
      };
    }
    case EBeneficiariosActionKind.CONFIRMAR_EDICAO_BENEFICIARIOS: {
      const val = action.value === undefined;
      return {
        ...state,
        editando: !val,
        confirmandoEdicao: val,
      };
    }
    case EBeneficiariosActionKind.CANCELAR_EDICAO_BENEFICIARIOS: {
      return {
        ...beneficiariosInitialState,
        coberturasOriginal: { ...state.coberturasOriginal },
      };
    }

    case EBeneficiariosActionKind.ALTERAR_ASSINATURA_VALIDA: {
      return {
        ...state,
        assinaturaValida: action.assinaturaValida,
      };
    }
    default: {
      throw Error(`Unknown action: ${(action as { type: string }).type}`);
    }
  }
}
