import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import {
  IPayloadObterHistoricoSolicitacoes,
  IResponseHistoricoSolicitacoes,
} from '@src/corporativo/types/consulta/historicoSolicitacao';
import { useContext } from 'react';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const DEFAULT_RETURN: IResponseHistoricoSolicitacoes[] = [];

type TUseObterHistoricoSolicitacao = {
  fetcher: (params: Partial<IPayloadObterHistoricoSolicitacoes>) => void;
  loading: boolean;
  response: IResponseHistoricoSolicitacoes[];
};

export const useObterHistoricoSolicitacao =
  (): TUseObterHistoricoSolicitacao => {
    const cpf = String(getSessionItem('cpfCnpj'));
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const { loading, invocarApiGatewayCvpComToken, response } =
      useApiGatewayCvpInvoker<
        Partial<IPayloadObterHistoricoSolicitacoes>,
        IResponseHistoricoSolicitacoes[]
      >(PECOS.ObterHistoricoSolicitacoes, {
        autoFetch: false,
        data: {
          cpfCnpj: cpf,
          numeroCertificado: certificadoAtivo.certificadoNumero,
        },
      });

    const obterHistoricoSolicitacoes = (
      params: Partial<IPayloadObterHistoricoSolicitacoes>,
    ) => {
      invocarApiGatewayCvpComToken(params);
    };

    return {
      fetcher: obterHistoricoSolicitacoes,
      response: tryGetValueOrDefault([response?.entidade], DEFAULT_RETURN),
      loading,
    };
  };
