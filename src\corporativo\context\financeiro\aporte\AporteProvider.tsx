/* eslint-disable max-lines */
import {
  AporteContext,
  CONTA_BANCARIA_INITIAL_STATE,
  enumTipoContaBancaria,
  FILTRO_TABELA_INITIAL_STATE,
  IDadosBancariosAporte,
  IEfetuarAporteResponse,
  IFiltroTabelaAporte,
  IFundosAporte,
  IItensExtrato,
  IObterFundosResponse,
  IValorContribuicao,
  useFormik,
  useMemo,
  useState,
  validarInputValorDistruibuicao,
  validationSchemaFiltrosTabelaAporte,
} from '@src/features/financeiro/aporte/exports';

interface IAporteProvider {
  children: React.ReactNode;
}

export const enum EEtapasAporte {
  FiltrosTabelaAporte,
  FundosDistribuicaoAporte,
  OrigemRecursosAporte,
  ModalVerificarCamposAporte,
  ModalConfirmarAporte,
  ContaDebitoAporte,
  NovaContaDebitoAporte,
  ComprovanteAporte,
}

const AporteProvider: React.FC<IAporteProvider> = ({ children }) => {
  const formikFiltrosTabelaAporte = useFormik<IFiltroTabelaAporte>({
    initialValues: FILTRO_TABELA_INITIAL_STATE,
    onSubmit: () => undefined,
    validationSchema: validationSchemaFiltrosTabelaAporte,
  });

  const formikValorDistribuido = useFormik<IValorContribuicao>({
    initialValues: { valorDistribuido: {}, valoresMinimos: {} },
    validateOnMount: true,
    validateOnBlur: true,
    validateOnChange: true,
    validate: values => validarInputValorDistruibuicao(values),
    onSubmit: () => undefined,
  });

  const [fundoSelecionado, setFundoSelecionado] = useState<IFundosAporte[]>([]);

  const [fundos, setFundos] = useState<IObterFundosResponse>();

  const [filtroTabelaFundos, setFiltroTabelaAporte] =
    useState<IFiltroTabelaAporte>(FILTRO_TABELA_INITIAL_STATE);

  const [etapa, setEtapa] = useState<EEtapasAporte>(
    EEtapasAporte.FiltrosTabelaAporte,
  );

  const [contaBancariaSelecionada, setContaBancariaSelecionada] =
    useState<IDadosBancariosAporte>(CONTA_BANCARIA_INITIAL_STATE);

  const [contasBancarias, setContasBancarias] = useState<
    IDadosBancariosAporte[]
  >([CONTA_BANCARIA_INITIAL_STATE]);

  const [origemRecurso, setOrigemRecurso] = useState<string>('');

  const [aporte, setAporte] = useState<IEfetuarAporteResponse>();

  const [tipoContaBancaria, setTipoContaBancaria] = useState<string>(
    enumTipoContaBancaria.CONTA_BANCARIA_EXISTENTE,
  );

  const [itensExtrato, setItensExtrato] = useState<IItensExtrato[]>([]);

  const [assinatura, setAssinatura] = useState<boolean>(false);

  const memorizedValues = useMemo(
    () => ({
      fundoSelecionado,
      setFundoSelecionado,
      filtroTabelaFundos,
      setFiltroTabelaAporte,
      etapa,
      setEtapa,
      fundos,
      setFundos,
      origemRecurso,
      setOrigemRecurso,
      contaBancariaSelecionada,
      setContaBancariaSelecionada,
      aporte,
      setAporte,
      contasBancarias,
      setContasBancarias,
      tipoContaBancaria,
      setTipoContaBancaria,
      itensExtrato,
      setItensExtrato,
      assinatura,
      setAssinatura,
      formikFiltrosTabelaAporte,
      formikValorDistribuido,
    }),
    [
      fundoSelecionado,
      filtroTabelaFundos,
      etapa,
      fundos,
      origemRecurso,
      contaBancariaSelecionada,
      aporte,
      contasBancarias,
      tipoContaBancaria,
      itensExtrato,
      formikFiltrosTabelaAporte,
      formikValorDistribuido,
      assinatura,
    ],
  );

  return (
    <AporteContext.Provider value={memorizedValues}>
      {children}
    </AporteContext.Provider>
  );
};

export default AporteProvider;
