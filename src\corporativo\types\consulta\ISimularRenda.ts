import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { ISimulacaoDeRendaResponse } from '@src/features/consulta/consultarSimulacaoDeRenda/types/ISimulacaoDeRenda';

export interface IUseObterConsultarRegimeTributario {
  Cpf: string;
  NumeroCertificado: string;
  numeroConta: string;
}
export interface IUseObterConsultarRegimeTributarioResponse {
  dados: {
    opcaoTributacaoIrrfAtual: string;
    opcoesTributacaoIrrfDisponiveis: string[];
    podeAlterarRegimeTributario: string;
  };
}

export interface IUseConsultarRegimeTributarioReturn {
  loading: boolean;
  podeAlterarRegimeTributario: string;
  tipoRegime: string;
}

export interface IUseObterTipoDeRendaResponse {
  nomTipoPagamento: string;
  codTipoPagamento: string;
}

export interface IUseObterTipoDeRendaBody {
  Cpf: string;
  NumeroCertificado: string;
}

export interface IUseObterTipoDeRenda {
  loading: boolean;

  tiposDeRendaFormatado: ITiposDeRendaFormatedSelect[];
}

export interface ITiposDeRendaFormatedSelect {
  value: string;
  text: string;
}

export interface ISimularRendaBody {
  tipoRenda: string;
  Cpf: string;
  NumeroCertificado: string;
  tipoTributacao: string;
}

export interface IUseSimularRenda {
  loading: boolean;
  invocarApiGatewayCvpComToken: (
    data: Partial<ISimularRendaBody>,
  ) => Promise<IHandleReponseResult<ISimulacaoDeRendaResponse> | undefined>;
}
