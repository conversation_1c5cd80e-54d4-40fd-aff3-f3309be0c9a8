import {
  Add,
  AlertaTimeout,
  Button,
  ConditionalRenderer,
  cpfCnpjMask,
  Dialog,
  InputText,
  Select,
  Text,
  TModalNovoBeneficiario,
  useModalNovoBeneficiario,
} from '../exports';

export const ModalNovoBeneficiario: React.FC<TModalNovoBeneficiario> = ({
  coberturaId,
  beneficiarioId,
  adicionarBeneficiario,
}) => {
  const {
    alertaModal,
    handleSubmit,
    setAlertaModal,
    numCpf,
    setNumCpf,
    setSexo,
    sexo,
  } = useModalNovoBeneficiario({
    coberturaId,
    beneficiarioId,
    adicionarBeneficiario,
  });

  return (
    <Dialog>
      <Dialog.Trigger asChild>
        <Button variant="secondary-outlined" leftIcon={<Add />} type="button">
          Adicionar
        </Button>
      </Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Header variant="highlight">
          <Text variant="heading-small-600">Adicionar beneficiário</Text>
        </Dialog.Header>
        <Dialog.Body>
          <ConditionalRenderer condition={alertaModal !== null}>
            <AlertaTimeout
              alerta={alertaModal!}
              callbackTimeout={() => setAlertaModal(null)}
            />
          </ConditionalRenderer>

          <Text variant="text-big-400">
            Cadastre um novo beneficiário, preenchendo os campos abaixo:
          </Text>
          <div
            style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
          >
            <InputText
              name="numCpf"
              variant="box-classic"
              placeholder="CPF"
              value={cpfCnpjMask.mask(numCpf)}
              onChange={e => setNumCpf(cpfCnpjMask.unmask(e.target.value))}
            />
            <Select
              placeholder="Sexo"
              onChange={value => setSexo(value[0].value)}
              selectedValues={[sexo]}
              options={[
                { text: 'Masculino', value: 'M' },
                { text: 'Feminino', value: 'F' },
              ]}
              size="standard"
              sizeWidth="standard"
              variant="box-classic"
            />
          </div>
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.Cancel asChild>
            <Button variant="secondary-outlined" type="button">
              Cancelar
            </Button>
          </Dialog.Cancel>

          <Dialog.Action asChild>
            <Button onClick={handleSubmit} type="button">
              Adicionar beneficiário
            </Button>
          </Dialog.Action>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};
