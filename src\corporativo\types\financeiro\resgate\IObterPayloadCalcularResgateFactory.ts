import { FormikProps } from 'formik';
import { ICalcularValorResgateRestanteRetorno } from '@src/corporativo/types/financeiro/resgate/ICalcularValorResgateRestante';
import { IFormikValuesSimulacaoResgate } from '@src/corporativo/types/financeiro/resgate/IFormikValuesSimulacaoResgate';

export interface IObterPayloadCalcularResgateFactory {
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
  aliquota: string;
  resultadoCalculoResgateRestante: ICalcularValorResgateRestanteRetorno;
}

export interface IObterPayloadCalcularResgateFactoryRetorno {
  tipoRegimeTributario: string;
  tipoResgate: string;
  detalhesFundos: unknown;
  valorResgateTotal: number;
}
