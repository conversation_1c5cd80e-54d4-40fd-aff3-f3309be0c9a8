import {
  IAssinaturaResponse,
  ITabelaAliquotas,
  TAliquotaRecord,
} from '@src/features/financeiro/alteracaoRegimeTributario/exports';

export type TUseFluxoAlteracaoRegimeTributario = {
  isLoading: boolean;
  assinaturaValida: boolean;
  isLoadingSolicitarAlteracaoPerfilTributario: boolean;
  podeExibirAssinatura: boolean;
  urlAssinatura: string;
  obterAssinatura: (response: IAssinaturaResponse) => void;
  handleSolicitarAlteracaoPerfilTributario: () => Promise<void>;
};

export type TObterPayloadCalculoResgateOutput = TAliquotaRecord<{
  tipoRegimeTributario: string;
  tipoResgate: string;
  detalhesFundos: object;
  valorResgateTotal: number | undefined;
}>;

export type TUseObterNumeroResgate = {
  loading: boolean;
  obterNumeroResgate: () => Promise<TAliquotaRecord<string>>;
};

export type TUseObterTabelasAliquotas = {
  obterTabelasAliquotas: () => Promise<TAliquotaRecord<ITabelaAliquotas[]>>;
  loading: boolean;
};

export type TUseAssinaturaAlteracaoRegimeTributario = () => {
  assinaturaValida: boolean;
  loading: boolean;
  obterAssinatura: (response: IAssinaturaResponse) => void;
  confirmarAssinaturaAlteracaoRegimeTributario: (
    codigoSolicitacao?: string,
  ) => Promise<void>;
};

export type TUseSolicitarAlteracaoRegimeTributario = () => {
  loading: boolean;
  urlAssinatura: string;
  assinaturaValida: boolean;
  isLoadingSolicitarAlteracaoPerfilTributario: boolean;
  obterAssinatura: (response: IAssinaturaResponse) => void;
  handleSolicitarAlteracaoPerfilTributario: () => Promise<void>;
};
