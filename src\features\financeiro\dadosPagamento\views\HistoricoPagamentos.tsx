import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const HistoricoPagamentos: React.FC<
  DadosPagamento.TPagamentosHistoricoProps
> = ({ metodoPagamento }) => {
  const { certificadoAtivo } = DadosPagamento.useContext(
    DadosPagamento.PrevidenciaContext,
  );
  const { data, loading } = DadosPagamento.useObterContribuicoes(
    certificadoAtivo?.certificadoNumero ?? '',
  );
  const [cobrancaSelecionada, setCobrancaSelecionada] =
    DadosPagamento.useState<string>();

  const paginator = DadosPagamento.usePaginator({
    rowsPerPage: 4,
  });

  const currentData = DadosPagamento.usePaginatedData({
    data,
    ...paginator,
  });

  const theme = DadosPagamento.useTheme();

  return (
    <DadosPagamento.HistoricoPagamentosContainer
      loading={loading}
      metodoPagamento={metodoPagamento}
    >
      <DadosPagamento.Divider topDistance="20px" />
      <DadosPagamento.Table
        themeTable="default"
        columns={DadosPagamento.columns(
          cobrancaSelecionada,
          setCobrancaSelecionada,
        )}
        data={currentData}
        noDataComponent="Não há pagamentos para exibir"
      />
      <DadosPagamento.Divider
        size="0px"
        topDistance="10px"
        bottomDistance="0px"
      />
      <DadosPagamento.Paginator
        variants="default"
        IconFirstPage={
          <DadosPagamento.IconFirstPageSharp
            color={theme.color.palette.primary['90']}
            size="large"
          />
        }
        IconLastPage={
          <DadosPagamento.IconLastPageSharp
            color={theme.color.palette.primary['90']}
            size="large"
          />
        }
        IconLeft={
          <DadosPagamento.IconChevronLeftSharp
            color={theme.color.palette.primary['90']}
            size="large"
          />
        }
        IconRight={
          <DadosPagamento.IconChevronRightSharp
            color={theme.color.palette.primary['90']}
            size="large"
          />
        }
        currentPage={paginator.currentPage}
        rowsPerPage={paginator.rowsPerPage}
        rowCount={data.length}
        hasItemsPerPage={false}
        onChangePage={paginator.handleChangePage}
        onChangeRowsPerPage={() => null}
      />
      <DadosPagamento.Divider bottomDistance="20px" />

      <DadosPagamento.ConditionalRenderer condition={!!cobrancaSelecionada}>
        <DadosPagamento.BotaoImprimir
          cobrancaSelecionada={cobrancaSelecionada}
        />
      </DadosPagamento.ConditionalRenderer>
    </DadosPagamento.HistoricoPagamentosContainer>
  );
};

export default HistoricoPagamentos;
