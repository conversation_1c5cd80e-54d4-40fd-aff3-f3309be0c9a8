import { CONSENTIMENTO_CONSTANTES, Utils } from '../exports';

export function montarPayloadAtualizarConsentimento(
  consentimento: boolean,
  subProcesso: number,
) {
  return {
    consentimento: {
      tipo: String(
        Utils.getTernaryResult(
          consentimento,
          CONSENTIMENTO_CONSTANTES.TIPO_CONSENTIMENTO_1,
          CONSENTIMENTO_CONSTANTES.TIPO_CONSENTIMENTO_0,
        ),
      ),
      numConsentimentoNegocio:
        CONSENTIMENTO_CONSTANTES.NUM_CONSENTIMENTO_NEGOCIO,
    },
    processoNegocio: {
      subProcesso: {
        codigo: String(subProcesso),
      },
      macroProcesso: {
        codigo: String(CONSENTIMENTO_CONSTANTES.MACRO_PROCESSO),
      },
    },
  };
}
