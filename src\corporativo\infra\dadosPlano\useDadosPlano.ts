import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';
import { IDadosPlanoRequest } from '@src/corporativo/types/dadosPlano/IDadosPlanoRequest';
import { TDadosPlanoResponse } from '@src/corporativo/types/dadosPlano/TDadosPlanoResponse';
import { TUseDadosPlano } from '@src/corporativo/types/dadosPlano/TUseDadosPlano';
import { PECOS } from '../config/api/endpoints';

export const useDadosPlano: TUseDadosPlano = payload => {
  const { loading, response } = useApiGatewayCvpInvoker<
    IDadosPlanoRequest,
    TDadosPlanoResponse
  >(PECOS.ObterDadosPalno, {
    data: payload,
    autoFetch: true,
  });

  return {
    loading,
    response: tryGetValueOrDefault(
      [response?.entidade],
      {} as TDadosPlanoResponse,
    ),
  };
};
