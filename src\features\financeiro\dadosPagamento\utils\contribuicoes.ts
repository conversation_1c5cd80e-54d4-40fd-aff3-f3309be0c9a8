import { CODIGO_MEIO_PAGAMENTO } from '../constants/constants';

export const converterFormaPagamento = (pagamentoMetodo?: string): string => {
  switch (pagamentoMetodo) {
    case CODIGO_MEIO_PAGAMENTO.DEBITOCONTA:
      return '<PERSON><PERSON><PERSON><PERSON> em Conta';
    case CODIGO_MEIO_PAGAMENTO.BOLETO:
      return 'Ficha de Compensão';
    default:
      return '';
  }
};

export const obterProximoMes = () => {
  const dataAtual = new Date();
  const proximoMes = new Date(dataAtual.setMonth(new Date().getMonth() + 1));
  return proximoMes.toISOString();
};
