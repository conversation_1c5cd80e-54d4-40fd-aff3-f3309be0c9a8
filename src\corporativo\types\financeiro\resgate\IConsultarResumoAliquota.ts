import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IConsultarResumoAliquotaPayload {
  codigoCertificado: string;
  numeroResgate: string;
}

export interface IConsultarResumoAliquotas {
  totalSaldoNominal: string;
  totalRentabilidade: string;
  totalSaldo: string;
  totalBaseIrrf: string;
  totalValorIrrf: string;
  totalValorLiquido: string;
  totalAliquotaIrrf: string;
}

export interface IConsultarResumoAliquotaResponse {
  resumoAliquotas: IConsultarResumoAliquotas[];
  codigoEmpresa: string;
  numeroResgate: string;
  codigoCertificado: string;
  codigoProduto: string;
  descricaoProduto: string;
  cpf: string;
  nomeCliente: string;
  codigoContaColetiva: string;
  tipoProduto: string;
  opcaoTributacao: string;
  nomeEmpresa: string;
  totalSaldoNominal: string;
  totalRentabilidade: string;
  totalSaldo: string;
  totalBaseIrrf: string;
  totalValorIrrf: string;
  totalValorLiquido: string;
}

export interface IUseConsultarResumoAliquotaReturn {
  dadosConsultaResumoAliquota: IConsultarResumoAliquotaResponse;
  isLoadingConsultaResumoAliquota: boolean;
  consultarResumoAliquota: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<IConsultarResumoAliquotaResponse> | undefined
  >;
}
