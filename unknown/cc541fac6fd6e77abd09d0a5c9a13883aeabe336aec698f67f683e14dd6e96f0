import {
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
  useCalcularResgate,
  useListarFundosParaResgate,
  obterPayloadAliquotasCalculo,
} from '@src/features/financeiro/alteracaoRegimeTributario/exports';

type TUseObterNumeroResgate = {
  loading: boolean;
  obterNumeroResgate: () => Promise<
    Record<'regressivo' | 'progressivo', string>
  >;
};

export const useObterNumeroResgate = (): TUseObterNumeroResgate => {
  const { calcularResgate, isLoadingCalculoResgate } = useCalcularResgate();
  const { listarFundosParaResgate, isLoadingListaFundosParaResgate } =
    useListarFundosParaResgate();

  const obterNumeroResgate = async () => {
    const dadosListaFundosParaResgate = await listarFundosParaResgate();

    const payloadCalcularResgate = obterPayloadAliquotasCalculo(
      dadosListaFundosParaResgate?.entidade?.saldo.saldoDisponivelParaResgate,
    );

    const dadosCalcularResgateRegressivo = await calcularResgate(
      payloadCalcularResgate.regressivo,
    );

    const dadosCalcularResgateProgressivo = await calcularResgate(
      payloadCalcularResgate.progressivo,
    );

    return {
      progressivo: tryGetValueOrDefault(
        [dadosCalcularResgateProgressivo?.entidade?.dadosEncargo.numeroResgate],
        '',
      ),
      regressivo: tryGetValueOrDefault(
        [dadosCalcularResgateRegressivo?.entidade?.dadosEncargo.numeroResgate],
        '',
      ),
    };
  };

  return {
    obterNumeroResgate,
    loading: checkIfSomeItemsAreTrue([
      isLoadingCalculoResgate,
      isLoadingListaFundosParaResgate,
    ]),
  };
};
