import {
  ITabelaAliquotas,
  IFundosAliquota,
  IValoresAliquotas,
} from '@src/features/financeiro/alteracaoRegimeTributario/exports';

export const obterDadosTabelaAliquotas = (
  fundosAliquota: IFundosAliquota[],
): ITabelaAliquotas[] => {
  return fundosAliquota.flatMap(item => {
    const listaValoresAliquotasRegressiva = item.valoresAliquotas;

    return listaValoresAliquotasRegressiva.map(
      (valoresAliquotasRegressiva: IValoresAliquotas) => ({
        descricaoFundo: item.descricaoFundo,
        ...valoresAliquotasRegressiva,
      }),
    );
  });
};
