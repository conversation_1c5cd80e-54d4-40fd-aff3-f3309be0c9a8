import { porcentagem } from '@cvp/utils';
import {
  ITabelaAliquotas,
  TColumn,
  tryGetMonetaryValueOrDefault,
  tryGetValueOrDefault,
} from '../exports';

export const tabelaAliquotasDadosColunas: TColumn[] = [
  {
    name: 'Fundos',
    selector: (row: ITabelaAliquotas) => row.descricaoFundo,
    minWidth: '200px',
    cell: (row: ITabelaAliquotas) => {
      return tryGetValueOrDefault([row.descricaoFundo], '-');
    },
    wrap: true,
  },
  {
    name: 'Saldo Total',
    selector: (row: ITabelaAliquotas) => row.saldoTotal,
    cell: (row: ITabelaAliquotas) =>
      tryGetMonetaryValueOrDefault(row.saldoTotal),
    center: true,
    minWidth: '180px',
  },
  {
    name: '<PERSON>íq<PERSON><PERSON> IRPF (%)',
    selector: (row: ITabelaAliquotas) => row.aliquota,
    cell: (row: ITabelaAliquotas) => {
      return tryGetValueOrDefault([porcentagem.mask(row.aliquota)], '-');
    },
  },
  {
    name: 'Saldo Disponível',
    selector: (row: ITabelaAliquotas) => row.valorDisponivel,
    minWidth: '180px',
    cell: (row: ITabelaAliquotas) => {
      return tryGetMonetaryValueOrDefault(row.valorDisponivel);
    },
  },
  {
    name: 'Saldo Indisponível',
    selector: (row: ITabelaAliquotas) => row.valorIndisponivel,
    center: true,
    minWidth: '180px',
    cell: (row: ITabelaAliquotas) => {
      return tryGetMonetaryValueOrDefault(row.valorIndisponivel);
    },
  },
];
