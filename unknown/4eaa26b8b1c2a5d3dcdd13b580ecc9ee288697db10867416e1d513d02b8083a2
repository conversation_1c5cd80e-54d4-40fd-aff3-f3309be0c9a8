import {
  ASSINATURA_SESSION_KEY,
  CONSTS,
  OPERACOES_PREVIDENCIA,
  TUseAssinaturaAlteracaoRegimeTributario,
  useConfirmarOperacaoAssinaturaCaixa,
  IAssinaturaResponse,
  getSessionItem,
  setSessionItem,
  useValidarAssinatura,
  useRegistrarTokenAssinaturaCaixa,
} from '../exports';

const useAssinaturaAlteracaoRegimeTributario: TUseAssinaturaAlteracaoRegimeTributario =
  () => {
    const { assinaturaValida, validarAssinatura } = useValidarAssinatura();
    const { invocarApiGatewayCvpComToken } = useRegistrarTokenAssinaturaCaixa();
    const { confirmarAssinatura, loading } =
      useConfirmarOperacaoAssinaturaCaixa();

    const obterAssinatura = (response: IAssinaturaResponse) => {
      setSessionItem(CONSTS.ASSINATURA_RESPONSE, JSON.stringify(response));
      invocarApiGatewayCvpComToken(response);
      validarAssinatura(response);
    };

    const confirmarAssinaturaAlteracaoRegimeTributario = async (
      codigoSolicitacao?: string,
    ) => {
      const assinaturaResponse = getSessionItem<IAssinaturaResponse>(
        ASSINATURA_SESSION_KEY,
      );

      if (!assinaturaResponse?.resposta?.token || !codigoSolicitacao) return;

      confirmarAssinatura({
        tipoOperacao: OPERACOES_PREVIDENCIA.MANUTENCAO_DE_REGIME_TRIBUTARIO,
        metaDadoConfirmacao: assinaturaResponse.resposta.token,
        codigoSolicitacao,
      });
    };

    return {
      assinaturaValida,
      obterAssinatura,
      confirmarAssinaturaAlteracaoRegimeTributario,
      loading,
    };
  };

export default useAssinaturaAlteracaoRegimeTributario;
