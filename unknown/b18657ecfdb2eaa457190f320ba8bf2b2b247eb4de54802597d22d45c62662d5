import * as ContentModalExport from '@src/features/financeiro/aporte/exports';

export interface IContentModalConfirmarAporteFactoryReturn {
  id: number;
  label: string;
  value: string | number;
}

export const contentModalConfirmarAporteFactory = ({
  filtroTabelaFundos,
  dadosCertificado,
}: ContentModalExport.IContentModalConfirmarAporteFactory): IContentModalConfirmarAporteFactoryReturn[] => {
  return [
    {
      id: 1,
      label: ContentModalExport.MODAL_APORTE.titular,
      value: ContentModalExport.capitalize(
        ContentModalExport.tryGetValueOrDefault(
          [dadosCertificado?.pessoa?.pessoaFisica?.nome],
          '',
        ),
      ),
    },
    {
      id: 2,
      label: ContentModalExport.MODAL_APORTE.certificado,
      value: dadosCertificado?.certificadoNumero ?? '--',
    },
    {
      id: 3,
      label: ContentModalExport.MODAL_APORTE.modalidade,
      value: `${
        dadosCertificado?.produto?.modalidade ?? ''
      }${ContentModalExport.getTernaryResult(
        dadosCertificado?.regimeTributario === undefined,
        '',
        dadosCertificado?.regimeTributario,
      )}`,
    },
    {
      id: 4,
      label: ContentModalExport.MODAL_APORTE.formaPagamento,
      value: ContentModalExport.getTernaryResult(
        filtroTabelaFundos.formaPagamento ===
          ContentModalExport.FORMA_PAGAMENTO.debito,
        ContentModalExport.FORMA_PAGAMENTO_MODAL.credito,
        ContentModalExport.FORMA_PAGAMENTO.boleto,
      ),
    },
    {
      id: 5,
      label: ContentModalExport.MODAL_APORTE.valor,
      value: ContentModalExport.formatarValorPadraoBrasileiro(
        filtroTabelaFundos.valorContribuicao ?? 0,
      ),
    },
  ];
};
