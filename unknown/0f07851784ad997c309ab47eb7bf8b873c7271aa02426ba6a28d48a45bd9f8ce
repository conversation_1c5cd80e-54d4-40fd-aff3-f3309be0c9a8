import {
  ITheme,
  TUseObterCoberturasResponseReturn,
} from '@src/features/layoutPrevidencia/exports';

export interface ICertificadoContentFactory {
  certificado?: {
    certificadoNumero?: string;
    emissao?: string;
    aposentadoria?: string;
  };
  response: TUseObterCoberturasResponseReturn | null;
  theme: ITheme;
}

export interface ICertificadoContentFactoryReturn {
  id: number;
  icon: React.ReactNode;
  label: string;
  value: string;
  width?: number;
}
