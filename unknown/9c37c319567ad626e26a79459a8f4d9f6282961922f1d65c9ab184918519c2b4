import * as CertificadoContentExport from '@src/features/layoutPrevidencia/exports';

export interface ICertificadoContentProps {
  certificado?: {
    certificadoNumero?: string;
    emissao?: string;
    aposentadoria?: string;
  };
  response: CertificadoContentExport.TUseObterCoberturasResponseReturn | null;
}

const CertificadoContent: React.FC<ICertificadoContentProps> = ({
  certificado,
  response,
}) => {
  const theme = CertificadoContentExport.useTheme();
  const items = CertificadoContentExport.CertificadoContentFactory({
    certificado,
    response,
    theme,
  });
  return (
    <CertificadoContentExport.Grid style={{ alignItems: 'center', gap: '5px' }}>
      {items.map(item => (
        <CertificadoContentExport.React.Fragment key={item.id}>
          <CertificadoContentExport.GridItemPersonalizado
            style={{
              margin: item.id === 0 ? '0px 0px 0px 7px' : 0,
              width: item.width,
            }}
          >
            {item.icon}
            <CertificadoContentExport.Text
              variant="text-standard-400"
              marginBottom="0"
              marginTop="0"
            >
              {item.label} {item.value}
            </CertificadoContentExport.Text>
          </CertificadoContentExport.GridItemPersonalizado>
          {item.id < items.length - 1 && (
            <CertificadoContentExport.Separator orientation="vertical" />
          )}
        </CertificadoContentExport.React.Fragment>
      ))}
    </CertificadoContentExport.Grid>
  );
};

export default CertificadoContent;
