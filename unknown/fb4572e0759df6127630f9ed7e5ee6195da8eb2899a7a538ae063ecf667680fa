import {
  ASSINATURA_SESSION_KEY,
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  EEtapasAporte,
  efetuarAporteRequestFactory,
  enumTipoContaBancaria,
  FORMA_PAGAMENTO,
  getSessionItem,
  IAssinaturaResponse,
  ICertificadoPrevidenciaResponse,
  IEfetuarAporteResponse,
  PrevidenciaContext,
  useAporteContext,
  useAporteServiceContext,
  useContext,
  OPERACOES_PREVIDENCIA,
  useConfirmarOperacaoAssinaturaCaixa,
} from '@src/features/financeiro/aporte/exports';

type TUseModalConfirmarAporte = () => {
  etapa: EEtapasAporte;
  efetuarAporteLoading: boolean;
  certificadoAtivo: ICertificadoPrevidenciaResponse;
  assinatura: boolean;
  handleProximaEtapa: () => void;
  handleEtapaAnterior: () => void;
};

export const useModalConfirmarAporte: TUseModalConfirmarAporte = () => {
  const { certificadoAtivo, isClientePep } = useContext(PrevidenciaContext);
  const {
    etapa,
    filtroTabelaFundos,
    fundoSelecionado,
    contaBancariaSelecionada,
    origemRecurso,
    setAporte,
    setEtapa,
  } = useAporteContext();

  const { efetuarAporte } = useAporteServiceContext();
  const { confirmarAssinatura } = useConfirmarOperacaoAssinaturaCaixa();
  const { tipoContaBancaria, assinatura } = useAporteContext();

  const numeroCertificado = certificadoAtivo?.certificadoNumero;

  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const efetuarAporteLoading = efetuarAporte.loading;

  const efetuarAporteRequest = efetuarAporteRequestFactory({
    cpfCnpj,
    numeroCertificado,
    fundoSelecionado,
    contaBancariaSelecionada,
    filtroTabelaFundos,
    isClientePep,
    origemRecurso,
  });

  const confirmarAssinaturaAporte = async (
    aporteResponse?: IEfetuarAporteResponse,
  ) => {
    const assinaturaResponse = getSessionItem<IAssinaturaResponse>(
      ASSINATURA_SESSION_KEY,
    );

    if (
      !assinaturaResponse?.resposta?.token ||
      !aporteResponse ||
      filtroTabelaFundos.formaPagamento !== FORMA_PAGAMENTO.debito
    )
      return;

    await confirmarAssinatura({
      metaDadoConfirmacao: assinaturaResponse.resposta.token,
      codigoSolicitacao: aporteResponse.numeroSolicitacao,
      tipoOperacao: OPERACOES_PREVIDENCIA.APORTE,
    });
  };

  const realizarAporte = async () => {
    const response = await efetuarAporte.invocarApiGatewayCvpComToken(
      efetuarAporteRequest,
    );

    await confirmarAssinaturaAporte(response?.entidade);

    setAporte(response?.entidade);

    if (
      checkIfAllItemsAreTrue([!!response?.entidade, !efetuarAporte.loading])
    ) {
      setEtapa(EEtapasAporte.ComprovanteAporte);
    }
    if (!response?.entidade) {
      setEtapa(EEtapasAporte.FundosDistribuicaoAporte);
    }
  };

  const handleProximaEtapa = () => {
    const isDebitoComAssinatura = checkIfAllItemsAreTrue([
      filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito,
      assinatura,
    ]);

    const isBoleto =
      filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.boleto;

    if (checkIfSomeItemsAreTrue([isDebitoComAssinatura, isBoleto])) {
      realizarAporte();
    }
  };

  const handleEtapaAnterior = () => {
    if (
      checkIfAllItemsAreTrue([
        filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito,
        tipoContaBancaria === enumTipoContaBancaria.CONTA_BANCARIA_NOVA,
      ])
    ) {
      setEtapa(EEtapasAporte.NovaContaDebitoAporte);
    } else if (
      checkIfAllItemsAreTrue([
        filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito,
        tipoContaBancaria === enumTipoContaBancaria.CONTA_BANCARIA_EXISTENTE,
      ])
    ) {
      setEtapa(EEtapasAporte.ContaDebitoAporte);
    } else {
      setEtapa(EEtapasAporte.FundosDistribuicaoAporte);
    }
  };

  return {
    etapa,
    efetuarAporteLoading,
    certificadoAtivo,
    assinatura,
    handleProximaEtapa,
    handleEtapaAnterior,
  };
};
