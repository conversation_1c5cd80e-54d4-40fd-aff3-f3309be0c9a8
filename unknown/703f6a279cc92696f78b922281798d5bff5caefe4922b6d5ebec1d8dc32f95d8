import {
  Card,
  Checkbox,
  CheckboxLabel,
  CONSTS,
  GridItem,
  React,
  S,
  useAlteracaoRegimeTributarioContext,
} from '../exports';

const CheckAlteracaoRegimeTributario: React.FC = () => {
  const { isDisabled, handleConfirmarAlteracaoRegimeTributario } =
    useAlteracaoRegimeTributarioContext();
  return (
    <S.GridItemContent xl="1">
      <Card.Root variant="stroke">
        <GridItem margin="33px 16px">
          <Checkbox
            onChange={checked =>
              handleConfirmarAlteracaoRegimeTributario(checked)
            }
            rightLabel={
              <CheckboxLabel variant="text-large-400">
                {CONSTS.ALERTS.CONFIRMACAO_REGIME_TRIBUTACAO}
              </CheckboxLabel>
            }
            disabled={!isDisabled}
            required
          />
        </GridItem>
      </Card.Root>
    </S.GridItemContent>
  );
};

export default CheckAlteracaoRegimeTributario;
