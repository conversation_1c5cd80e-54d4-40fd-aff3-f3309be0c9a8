import * as SinistroHistoricoExports from '@src/features/consultaSinistro/exports';

const SinistroHistorico: React.FC = () => {
  const {
    loading,
    response: dadosSinistro,
    error,
  } = SinistroHistoricoExports.useListarSinistro();
  const navigate = SinistroHistoricoExports.useNavigate();

  return (
    <SinistroHistoricoExports.LayoutPlataforma
      primaryTitle={SinistroHistoricoExports.PREVIDENCIA_CONSTANTS.primaryTitle}
      buttonsRedirect={SinistroHistoricoExports.REDIRECT_BUTTONS('sinistros')}
      secondaryTitle={
        SinistroHistoricoExports.PREVIDENCIA_CONSTANTS.sinistroTitle
      }
      filterBykey="situacao"
      profileFilters={{}}
      habilitarPesquisa={false}
      exibirFiltros={false}
      userProfile="OPERADOR"
    >
      <SinistroHistoricoExports.SwitchCase fallback={null}>
        <SinistroHistoricoExports.Match when={loading}>
          <SinistroHistoricoExports.Grid margin="30px" justify="center">
            <SinistroHistoricoExports.LoadingSpinner size="medium">
              {SinistroHistoricoExports.LOADING}
            </SinistroHistoricoExports.LoadingSpinner>
          </SinistroHistoricoExports.Grid>
        </SinistroHistoricoExports.Match>
        <SinistroHistoricoExports.Match
          when={SinistroHistoricoExports.checkIfAllItemsAreTrue([
            !loading,
            error,
          ])}
        >
          <SinistroHistoricoExports.Alert
            variant="warning-01"
            icon={
              <SinistroHistoricoExports.IconWarningRound
                size="medium"
                color="#CA9804"
              />
            }
          >
            Não foi possível carregar os sinistros. Tente novamente mais tarde.
          </SinistroHistoricoExports.Alert>
        </SinistroHistoricoExports.Match>
        <SinistroHistoricoExports.Match
          when={SinistroHistoricoExports.checkIfAllItemsAreTrue([
            !loading,
            !dadosSinistro.length,
          ])}
        >
          <SinistroHistoricoExports.Alert
            variant="information-01"
            icon={
              <SinistroHistoricoExports.IconInfoRound
                size="medium"
                color="#038299"
              />
            }
          >
            Não foi encontrado nenhum sinistro para este cliente
          </SinistroHistoricoExports.Alert>
        </SinistroHistoricoExports.Match>
        <SinistroHistoricoExports.Match
          when={[!loading, !!dadosSinistro.length]}
        >
          <SinistroHistoricoExports.S.ContainerSinistrosStyled>
            <SinistroHistoricoExports.S.SinistrosGridStyled>
              <SinistroHistoricoExports.For each={dadosSinistro}>
                {sinistro => (
                  <SinistroHistoricoExports.SinistroCard
                    key={sinistro.numeroAvisoSinistro}
                    {...sinistro}
                  />
                )}
              </SinistroHistoricoExports.For>
            </SinistroHistoricoExports.S.SinistrosGridStyled>
          </SinistroHistoricoExports.S.ContainerSinistrosStyled>
        </SinistroHistoricoExports.Match>
      </SinistroHistoricoExports.SwitchCase>
      <SinistroHistoricoExports.S.GoBackButtonContainerStyled>
        <SinistroHistoricoExports.Button
          variant="secondary"
          onClick={() => navigate(SinistroHistoricoExports.ROUTES.INICIO)}
        >
          Voltar
        </SinistroHistoricoExports.Button>
      </SinistroHistoricoExports.S.GoBackButtonContainerStyled>
    </SinistroHistoricoExports.LayoutPlataforma>
  );
};

export default SinistroHistorico;
