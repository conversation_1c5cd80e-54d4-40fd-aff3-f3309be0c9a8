import * as BotoesApoliceExports from '@src/features/layoutPrevidencia/exports';

const BotoesApolice: React.FC = () => {
  const theme = BotoesApoliceExports.useTheme();
  const { modalImprDocDispatch } = BotoesApoliceExports.useContext(
    BotoesApoliceExports.ModalImprimirDocumentosContext,
  );
  const { permissoesMatrizAcesso } = BotoesApoliceExports.useContext(
    BotoesApoliceExports.MatrizAcessoContext,
  );

  const botaoImprimirDocumentosEstaDesabilitado =
    !BotoesApoliceExports.verificarSeMatrizAcessoPossuiAlgumaPermissao(
      permissoesMatrizAcesso,
      BotoesApoliceExports.retornarTodasPermissoesDaCategoria(
        BotoesApoliceExports.IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES,
      ),
    );

  const botaoCancelarApoliceEstaDesabilitado =
    !BotoesApoliceExports.verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      permissoesMatrizAcesso,
      BotoesApoliceExports.BOTOES_APOLICES_PERMISSOES.botao_cancelar_certificado
        .permissions,
    );

  return (
    <BotoesApoliceExports.Grid margin="0">
      <div style={{ width: 236 + 5 + 5 + 1 }}>
        <BotoesApoliceExports.Button
          variant="secondary-text"
          leftIcon={<BotoesApoliceExports.PDFIcon size="big" />}
          style={{
            background: 'none',
            margin: 0,
            padding: '2px',
          }}
          onClick={() =>
            modalImprDocDispatch({
              type: BotoesApoliceExports.EModalImprDocActionType.ABRIR_MODAL,
            })
          }
          disabled={botaoImprimirDocumentosEstaDesabilitado}
        >
          Imprimir documentos
        </BotoesApoliceExports.Button>
      </div>
      <div style={{ width: 208 + 5 + 5 + 1 }}>
        <BotoesApoliceExports.Button
          variant="secondary-text"
          leftIcon={
            <BotoesApoliceExports.IconCancelOutlinedSharp size="small" />
          }
          style={{ background: 'none', margin: 0, padding: '2px' }}
          disabled={
            AppConfig?.MFE_ENV !== 'development'
              ? true
              : botaoCancelarApoliceEstaDesabilitado
          }
        >
          Cancelar apólice
        </BotoesApoliceExports.Button>
      </div>
      <BotoesApoliceExports.Button
        variant="secondary-text"
        color={theme.color.palette.grayscale['70']}
        leftIcon={<BotoesApoliceExports.DocumentIcon size="big" />}
        style={{
          background: 'none',
          margin: 0,
          padding: '2px',
          color: theme.color.palette.grayscale['70'],
        }}
      >
        Solicitar ASC
      </BotoesApoliceExports.Button>
    </BotoesApoliceExports.Grid>
  );
};

export default BotoesApolice;
