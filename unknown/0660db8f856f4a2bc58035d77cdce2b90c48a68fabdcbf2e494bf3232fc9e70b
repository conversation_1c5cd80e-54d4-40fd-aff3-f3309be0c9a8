import * as DadosParticipante from '../exports';

export const ControlesDadosParticipante: React.FC = () => {
  const { editando, salvando, assinaturaValida, buscandoCep } =
    DadosParticipante.React.useContext(
      DadosParticipante.DadosParticipanteContext,
    );
  const { permissoesMatrizAcesso } = DadosParticipante.React.useContext(
    DadosParticipante.MatrizAcessoContext,
  );
  const dispatch = DadosParticipante.React.useContext(
    DadosParticipante.DadosParticipanteDispatchContext,
  );
  const { submitForm } = DadosParticipante.useFormikContext();

  const ambienteIntegrado = window.location.href.includes(
    'sipnc-host-microfront-des-esteiras.nprd2.caixa',
  );

  return (
    <DadosParticipante.Grid justify="flex-end">
      <DadosParticipante.SwitchCase>
        <DadosParticipante.Match when={editando}>
          <DadosParticipante.GridItem>
            <DadosParticipante.Button
              variant="secondary-outlined"
              leftIcon={
                <DadosParticipante.IconCancelOutlinedSharp size="small" />
              }
              type="button"
              onClick={() =>
                dispatch({
                  type: DadosParticipante.EDadosParticipanteActionKind
                    .CANCELAR_EDICAO_DADOS_PARTICIPANTE,
                })
              }
            >
              Cancelar
            </DadosParticipante.Button>
          </DadosParticipante.GridItem>
          <DadosParticipante.GridItem>
            <DadosParticipante.Button
              leftIcon={<DadosParticipante.SaveIcon size="small" />}
              variant="secondary"
              type="button"
              disabled={DadosParticipante.Utils.checkIfSomeItemsAreTrue([
                salvando,
                buscandoCep,
                DadosParticipante.Utils.checkIfAllItemsAreTrue([
                  ambienteIntegrado,
                  !assinaturaValida,
                ]),
              ])}
              onClick={submitForm}
            >
              <DadosParticipante.ConditionalRenderer
                condition={!salvando}
                fallback={<DadosParticipante.LoadingSpinner color="#9EB2B8" />}
              >
                Salvar
              </DadosParticipante.ConditionalRenderer>
            </DadosParticipante.Button>
          </DadosParticipante.GridItem>
        </DadosParticipante.Match>
        <DadosParticipante.Match when={!editando}>
          <DadosParticipante.ConditionalRenderer
            condition={AppConfig.MFE_ENV === 'development'}
          >
            <DadosParticipante.MatrizAcessoRenderizadorTodasPermissoes
              matrizAcesso={permissoesMatrizAcesso}
              permissoesComponente={
                DadosParticipante.ALTERACOES_COMPONENTES_PERMISSOES
                  .alteracao_dados_participante.permissions
              }
            >
              <DadosParticipante.Button
                leftIcon={
                  <DadosParticipante.IconEditOutlinedSharp size="small" />
                }
                type="button"
                variant="secondary-outlined"
                onClick={() =>
                  dispatch({
                    type: DadosParticipante.EDadosParticipanteActionKind
                      .EDITAR_DADOS_PARTICIPANTE,
                  })
                }
              >
                Editar
              </DadosParticipante.Button>
            </DadosParticipante.MatrizAcessoRenderizadorTodasPermissoes>
          </DadosParticipante.ConditionalRenderer>
        </DadosParticipante.Match>
      </DadosParticipante.SwitchCase>
    </DadosParticipante.Grid>
  );
};
