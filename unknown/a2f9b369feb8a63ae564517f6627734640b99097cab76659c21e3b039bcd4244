import * as CertificadoInfoExports from '@src/features/layoutPrevidencia/exports';

type TICertificadoInfoProps = {
  certificado: CertificadoInfoExports.ICertificadoPrevidenciaResponse;
};
const CertificadoInfo: React.FC<TICertificadoInfoProps> = ({ certificado }) => {
  const { response } = CertificadoInfoExports.useObterCoberturas({
    NumeroCertificado: certificado?.certificadoNumero,
    Cpf: String(CertificadoInfoExports.Utils.getSessionItem('cpfCnpj')),
  });

  return (
    <CertificadoInfoExports.GridItem xs="1" style={{ margin: 0, padding: 0 }}>
      <CertificadoInfoExports.Text variant="text-big-600" lineheight="farther">
        {CertificadoInfoExports.Utils.capitalize(
          CertificadoInfoExports.Utils.tryGetValueOrDefault(
            [
              `${certificado.produto.modalidade} | ${certificado.produto.descricaoProduto}`,
            ],
            '',
          ),
        )}
      </CertificadoInfoExports.Text>
      <CertificadoInfoExports.CertificadoContent
        certificado={certificado}
        response={response}
      />
    </CertificadoInfoExports.GridItem>
  );
};

export default CertificadoInfo;
