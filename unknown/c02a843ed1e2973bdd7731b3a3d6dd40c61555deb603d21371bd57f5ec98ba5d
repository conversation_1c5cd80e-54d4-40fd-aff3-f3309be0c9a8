import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  CONSTS,
  useAlteracaoRegimeTributarioContext,
  useConsultarRegimeTributario,
  useEffect,
  TUseFluxoAlteracaoRegimeTributario,
  useObterTabelasAliquotas,
  useSolicitarAlteracaoRegimeTributario,
} from '../exports';

const useFluxoAlteracaoRegimeTributario =
  (): TUseFluxoAlteracaoRegimeTributario => {
    const { podeExibirAssinatura, setIsOpen, setDadosTabelaAliquotas } =
      useAlteracaoRegimeTributarioContext();

    const {
      handleSolicitarAlteracaoPerfilTributario,
      assinaturaValida,
      isLoadingSolicitarAlteracaoPerfilTributario,
      loading: isLoadingFluxoAlteracao,
      obterAssinatura,
      urlAssinatura,
    } = useSolicitarAlteracaoRegimeTributario();
    const { obterTabelasAliquotas, loading: loadingObterAliquotas } =
      useObterTabelasAliquotas();

    const {
      loading: isLoadingConsultarRegimeTributario,
      podeAlterarRegimeTributario,
    } = useConsultarRegimeTributario();

    const iniciaFluxoAlteracaoRegimeTributario = async () => {
      if (
        checkIfAllItemsAreTrue([
          !isLoadingConsultarRegimeTributario,
          podeAlterarRegimeTributario === CONSTS.REGIME_TRIBUTARIO.PODE_ALTERAR,
        ])
      ) {
        const tabelasAliquotas = await obterTabelasAliquotas();

        setDadosTabelaAliquotas({
          aliquotasProgressivas: tabelasAliquotas.progressivo,
          aliquotasRegressivas: tabelasAliquotas.regressivo,
        });
      } else if (
        checkIfAllItemsAreTrue([
          !isLoadingConsultarRegimeTributario,
          podeAlterarRegimeTributario ===
            CONSTS.REGIME_TRIBUTARIO.NAO_PODE_ALTERAR,
        ])
      ) {
        setIsOpen({
          modal: {
            openModal: true,
            mensagemModal: CONSTS.ALERTS.AVISO_ALTERACAO_IRRETRATAVEL,
          },
        });
      }
    };

    useEffect(() => {
      iniciaFluxoAlteracaoRegimeTributario();
    }, [podeAlterarRegimeTributario]);

    return {
      isLoading: checkIfSomeItemsAreTrue([
        isLoadingConsultarRegimeTributario,
        loadingObterAliquotas,
        isLoadingFluxoAlteracao,
      ]),
      urlAssinatura,
      assinaturaValida,
      isLoadingSolicitarAlteracaoPerfilTributario,
      podeExibirAssinatura,
      obterAssinatura,
      handleSolicitarAlteracaoPerfilTributario,
    };
  };

export default useFluxoAlteracaoRegimeTributario;
