import {
  Assinatura,
  CONSTS,
  getSessionItem,
  GridItem,
  IAssinaturaResponse,
  Match,
  PrevidenciaContext,
  Separator,
  Text,
  tryGetValueOrDefault,
  useAlteracaoRegimeTributarioContext,
  useContext,
} from '../exports';

interface IAssinaturaAlteracaoRegimeTributario {
  obterAssinatura: (response: IAssinaturaResponse) => void;
}

const AssinaturaAlteracaoRegimeTributario: React.FC<
  IAssinaturaAlteracaoRegimeTributario
> = ({ obterAssinatura }) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { podeExibirAssinatura } = useAlteracaoRegimeTributarioContext();
  const cpfCnpj = String(getSessionItem('cpfCnpj'));

  return (
    <Match when={podeExibirAssinatura}>
      <Separator bgColor="gray-04" orientation="horizontal" size="4" />
      <GridItem xs="1" lg="2/3">
        <Text
          variant="text-standard-600"
          fontColor="content-neutral-05"
          marginTop="20px"
          marginBottom="20px"
        >
          {CONSTS.TITULOS.AUTENTICACAO}
        </Text>
        <Assinatura
          dados={{
            cpfCnpj: tryGetValueOrDefault([cpfCnpj], ''),
            numeroCertificado: tryGetValueOrDefault(
              [certificadoAtivo.certificadoNumero],
              '',
            ),
          }}
          callback={obterAssinatura}
        />
      </GridItem>
    </Match>
  );
};

export default AssinaturaAlteracaoRegimeTributario;
