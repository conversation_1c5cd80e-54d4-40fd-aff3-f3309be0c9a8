import {
  CONSTS,
  INavigateFunction,
  useAlteracaoRegimeTributarioContext,
  useNavigate,
} from '../exports';

type TUseModalAvisoAlteracaoAliquota = () => {
  navigate: INavigateFunction;
  handleConfirmarAliquota: (event: React.MouseEvent<HTMLButtonElement>) => void;
  controlarModalAvisoAlteracaoAliquota: (open?: boolean) => void;
};

const useModalAvisoAlteracaoAliquota: TUseModalAvisoAlteracaoAliquota = () => {
  const navigate = useNavigate();

  const { isOpen, setIsOpen, setDisabled } =
    useAlteracaoRegimeTributarioContext();

  const mensagem = isOpen.modal.mensagemModal;

  const handleConfirmarAliquota = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    setIsOpen({ modal: { mensagemModal: '', openModal: false } });
    const confirmouAlteracaoAliquota =
      event.currentTarget.id === CONSTS.BOTOES.CONFIRMAR;

    if (confirmouAlteracaoAliquota) setDisabled(true);
  };

  const controlarModalAvisoAlteracaoAliquota = (open?: boolean): void => {
    if (open) {
      setIsOpen({
        modal: {
          openModal: open,
          mensagemModal: mensagem,
        },
      });
    }

    setIsOpen({
      ...isOpen,
      modal: {
        ...isOpen.modal,
        openModal: !isOpen.modal.openModal,
      },
    });
  };
  return {
    navigate,
    handleConfirmarAliquota,
    controlarModalAvisoAlteracaoAliquota,
  };
};

export default useModalAvisoAlteracaoAliquota;
