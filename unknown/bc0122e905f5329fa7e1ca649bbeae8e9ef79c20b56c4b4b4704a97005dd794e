import * as DadosModalConfirmarAporteExport from '@src/features/financeiro/aporte/exports';

interface IDadosModalConfirmarAporte {
  filtroTabelaFundos: DadosModalConfirmarAporteExport.IFiltroTabelaAporte;
  dadosCertificado:
    | DadosModalConfirmarAporteExport.ICertificadoPrevidenciaResponse
    | undefined;
}

const DadosModalConfirmarAporte: React.FC<IDadosModalConfirmarAporte> = ({
  filtroTabelaFundos,
  dadosCertificado,
}) => {
  const responseFactory =
    DadosModalConfirmarAporteExport.contentModalConfirmarAporteFactory({
      filtroTabelaFundos,
      dadosCertificado,
    });
  return (
    <DadosModalConfirmarAporteExport.Grid>
      <DadosModalConfirmarAporteExport.GridItem xs="1">
        <DadosModalConfirmarAporteExport.Text
          variant="text-big-600"
          fontColor="content-neutral-06"
          textAlign="center"
        >
          {DadosModalConfirmarAporteExport.MODAL_APORTE.titulo}
        </DadosModalConfirmarAporteExport.Text>
      </DadosModalConfirmarAporteExport.GridItem>
      {responseFactory.map(item => (
        <DadosModalConfirmarAporteExport.ContentConfirmarAporte
          key={item.id}
          label={item.label}
          value={item.value}
        />
      ))}
    </DadosModalConfirmarAporteExport.Grid>
  );
};

export default DadosModalConfirmarAporte;
