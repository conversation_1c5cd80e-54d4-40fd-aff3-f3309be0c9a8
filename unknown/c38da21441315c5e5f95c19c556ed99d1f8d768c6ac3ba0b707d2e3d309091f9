export { default as styled, css } from 'styled-components';

export {
  Grid,
  Text,
  GridItem,
  TextField,
  Button,
  Select,
  LoadingSpinner,
  Alert,
  ConditionalRenderer,
  IconChevronLeftSharp,
  IconChevronRightSharp,
  IconFirstPageSharp,
  IconLastPageSharp,
  IconEditOutlinedSharp,
  IconExpandLessSharp,
  IconExpandMoreSharp,
  Paginator,
  Table,
  useTheme,
  IconInfoOutlined,
  ToolTip,
  DialogInfo,
} from '@cvp/design-system-caixa';

export {
  Match,
  SwitchCase,
  usePaginatedData,
  usePaginator,
} from '@cvp/componentes-posvenda';

export type { IPagamentoRegularPrevidencia } from '@src/corporativo/types/consultaCertificado/Response/IPagamentoRegularPrevidencia';

export * from '@src/features/financeiro/dadosPagamento/types/AlterarDadosPagamento';
export type { IResponseAlteracaoDiaVencimento } from '@src/features/financeiro/types/AlteracaoDiaVencimento';

export { EditOutlined, CancelOutlined, SaveRounded } from '@mui/icons-material';

export * from '@src/features/financeiro/dadosPagamento/style';
export * from '@src/features/financeiro/dadosPagamento/factory';
export * from '@src/features/financeiro/dadosPagamento/constants';

export type { IResponseDadosCertificadosPorCpf } from '@src/features/financeiro/types/DadosCertificadoPorCpf';
export type { IResponseListaTiposContasBancarias } from '@src/features/financeiro/types/ListaTiposContaBancarias';

export { useState, useContext, useEffect, useCallback } from 'react';
export type { PropsWithChildren } from 'react';

export { useNavigate } from 'react-router-dom';
export {
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
  getTernaryResult,
  checkIfSomeItemsAreTrue,
  getSessionItem,
} from '@cvp/utils';

export { converterFormaPagamento } from '@src/features/financeiro/dadosPagamento/utils/contribuicoes';
export { obterCodigoOperacaoENumeroConta } from '@src/features/financeiro/utils/previdencia';

export { HistoricoPagamentosContainer } from '@src/features/financeiro/dadosPagamento/views/HistoricoPagamentosContainer';
export { default as BotoesExpandir } from '@src/features/financeiro/dadosPagamento/views/BotoesExpandir';
export { default as FormaPagamento } from '@src/features/financeiro/dadosPagamento/views/FormaPagamento';
export { default as HistoricoPagamentos } from '@src/features/financeiro/dadosPagamento/views/HistoricoPagamentos';
export { default as ModalAlerta } from '@src/features/financeiro/dadosPagamento/views/ModalAlerta';
export { default as BotaoImprimir } from '@src/features/financeiro/dadosPagamento/views/BotaoImprimir';

export { default as AlterarDadosPagamento } from '@src/features/financeiro/dadosPagamento/views/AlterarDadosPagamento';
export { default as AlertaMensagens } from '@src/features/financeiro/dadosPagamento/views/AlertaMensagens';
export { default as SelectFormaPagamento } from '@src/features/financeiro/dadosPagamento/views/SelectFormaPagamento';
export { default as SelectContaBancaria } from '@src/features/financeiro/dadosPagamento/views/SelectContaBancaria';
export { default as SelectDiaDebito } from '@src/features/financeiro/dadosPagamento/views/SelectDiaDebito';
export { default as SelectOperacao } from '@src/features/financeiro/dadosPagamento/views/SelectOperacao';
export { default as CamposNovaConta } from '@src/features/financeiro/dadosPagamento/views/CamposNovaConta';
export { default as CampoTexto } from '@src/features/financeiro/dadosPagamento/views/CampoTexto';
export { default as Acoes } from '@src/features/financeiro/dadosPagamento/views/Acoes';
export { default as MatrizAcessoRenderizadorAlgumaPermissao } from '@src/corporativo/components/MatrizAcessoRenderizadorAlgumaPermissao';

export { useAlterarDadosPagamento } from '@src/features/financeiro/dadosPagamento/hook/useAlterarDadosPagamento';
export { useObterContribuicoes } from '@src/features/financeiro/dadosPagamento/hook/useObterContribuicoes';
export { useObterBoleto } from '@src/features/financeiro/dadosPagamento/hook/useObterBoleto';
export { useConsultaCertificadosPorCpf } from '@src/corporativo/infra/financeiro/dadosPagamento/alterarDadosPagamento/useConsultaCertificadosPorCpf';
export { useListarContasBancarias } from '@src/corporativo/infra/financeiro/dadosPagamento/alterarDadosPagamento/useListarContasBancarias';
export { AlterarDadosPagamentoProvider } from '@src/corporativo/context/AlterarDadosPagamento';
export { tipoEmailConstants } from '@src/corporativo/infra/email/tipoEmail';
export { MatrizAcessoContext } from '@src/corporativo/context/MatrizAcessoContext';
export { default as PDFIcon } from '@src/corporativo/components/PDFIcon';
export { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
export { verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias } from '@src/corporativo/utils/matrizAcesso';
export { ALTERACOES_COMPONENTES_PERMISSOES } from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';
export type { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
export type { IHandleReponseResult } from '@cvp/componentes-posvenda';
export type { IValidarContaResponse } from '@src/shared/types/IUseValidarConta';
export type { IVariantAlert } from '@cvp/design-system-caixa/dist/atoms/Alert/Alert.types';
export { useAlertaDadosPagamento } from '@src/features/financeiro/dadosPagamento/hook/useAlertaDadosPagamento';

export { default as useAtualizarDataVencimento } from '@src/corporativo/infra/financeiro/dadosPagamento/alterarDadosPagamento/useAtualizarDataVencimento';
export { useAtualizarFormaPagamento } from '@src/corporativo/infra/financeiro/dadosPagamento/alterarDadosPagamento/useAtualizarFormaDePagamento';
export { useRecuperarContribuicoesCertificado } from '@src/corporativo/infra/financeiro/dadosPagamento/alterarDadosPagamento/useRecuperarContribuicoesCertificado';
export { NUMERO_BANCO_CAIXA } from '@src/shared/constants/DadosPagamento';
export { useValidarConta } from '@src/shared/infra/useValidarConta';
export { useAlterarConta } from '@src/features/financeiro/dadosPagamento/hook/useAlterarConta';
export { changeEventBuilder } from '@src/features/financeiro/dadosPagamento/utils/changeEventBuilder';

export * from '@src/features/financeiro/dadosPagamento/types';
export * from '@src/corporativo/constants/transferencias/constants/FluxoDeTransferencia';
