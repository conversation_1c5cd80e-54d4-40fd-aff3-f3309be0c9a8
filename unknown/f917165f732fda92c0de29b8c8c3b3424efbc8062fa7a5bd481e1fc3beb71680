import {
  BOTOES,
  FILTRO_APORTE,
  GridItem,
  IFiltroTabelaAporte,
  Select,
  Text,
  FILTRO_APORTE_SELECT,
  TFormikProps,
} from '@src/features/financeiro/aporte/exports';

interface IFormaDePagamento {
  formik: TFormikProps<IFiltroTabelaAporte>;
}

const FormaDePagamento: React.FC<IFormaDePagamento> = ({ formik }) => {
  return (
    <GridItem xs="1 / 4">
      <Text variant="text-standard-600" fontColor="content-neutral-05">
        {FILTRO_APORTE.selectFormaPagamento}
      </Text>

      <Select
        id="formaPagamento"
        onChange={event =>
          formik.setFieldValue('formaPagamento', event[0].value)
        }
        options={FILTRO_APORTE_SELECT}
        placeholder={BOTOES.select}
        size="standard"
        textVariant="text-small-400"
        sizeWidth="large"
        variant="box-classic"
      />
    </GridItem>
  );
};

export default FormaDePagamento;
