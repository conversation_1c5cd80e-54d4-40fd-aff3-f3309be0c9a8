import * as ModalVerificarCamposExport from '@src/features/financeiro/aporte/exports';

const ModalVerificarCamposAporte: React.FC = () => {
  const {
    formik,
    disabledForm,
    certificado,
    validarCamposAporteLoading,

    etapa,
    filtroTabelaFundos,
    handleProximaEtapa,
    handleFundosDistribuicaoAporte,
  } = ModalVerificarCamposExport.useModalVerificarCamposAporte();

  return (
    <ModalVerificarCamposExport.Dialog
      open={
        etapa ===
        ModalVerificarCamposExport.EEtapasAporte.ModalVerificarCamposAporte
      }
      onOpenChange={handleFundosDistribuicaoAporte}
    >
      <ModalVerificarCamposExport.Dialog.Content>
        <ModalVerificarCamposExport.Dialog.Header variant="highlight">
          <ModalVerificarCamposExport.Text
            variant="text-big-400"
            fontColor="content-neutral-01"
          >
            {ModalVerificarCamposExport.MODAL_APORTE.cabecalho}
          </ModalVerificarCamposExport.Text>
        </ModalVerificarCamposExport.Dialog.Header>

        <ModalVerificarCamposExport.Dialog.Body>
          <ModalVerificarCamposExport.DadosModalConfirmarAporte
            filtroTabelaFundos={filtroTabelaFundos}
            dadosCertificado={certificado}
          />

          <ModalVerificarCamposExport.ValidacaoAporte formik={formik} />
        </ModalVerificarCamposExport.Dialog.Body>
        <ModalVerificarCamposExport.Dialog.Footer>
          <ModalVerificarCamposExport.Dialog.Cancel asChild>
            <ModalVerificarCamposExport.Button
              disabled={validarCamposAporteLoading}
              onClick={handleFundosDistribuicaoAporte}
              variant="secondary-outlined"
            >
              <ModalVerificarCamposExport.ButtonContainer>
                {ModalVerificarCamposExport.BOTOES.cancelar}
              </ModalVerificarCamposExport.ButtonContainer>
            </ModalVerificarCamposExport.Button>
          </ModalVerificarCamposExport.Dialog.Cancel>

          <ModalVerificarCamposExport.Button
            disabled={disabledForm}
            onClick={handleProximaEtapa}
            variant="primary"
            size="standard"
          >
            <ModalVerificarCamposExport.ButtonContainer>
              {ModalVerificarCamposExport.getTernaryResult(
                validarCamposAporteLoading,
                <ModalVerificarCamposExport.LoadingSpinner />,
                <p>{ModalVerificarCamposExport.BOTOES.confirmar}</p>,
              )}
            </ModalVerificarCamposExport.ButtonContainer>
          </ModalVerificarCamposExport.Button>
        </ModalVerificarCamposExport.Dialog.Footer>
      </ModalVerificarCamposExport.Dialog.Content>
    </ModalVerificarCamposExport.Dialog>
  );
};

export default ModalVerificarCamposAporte;
