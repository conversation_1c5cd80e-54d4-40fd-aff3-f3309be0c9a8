import {
  CheckAlteracaoRegimeTributario,
  Grid,
  Aliquotas,
  useFluxoAlteracaoRegimeTributario,
  AssinaturaAlteracaoRegimeTributario,
  AlertAlteracaoRegimeTributario,
  AlteracaoRegimeTributarioBotoes,
} from '../exports';

const FluxoAlteracaoRegimeTributario: React.FC = () => {
  const {
    isLoading,
    assinaturaValida,
    isLoadingSolicitarAlteracaoPerfilTributario,
    urlAssinatura,
    podeExibirAssinatura,
    obterAssinatura,
    handleSolicitarAlteracaoPerfilTributario,
  } = useFluxoAlteracaoRegimeTributario();

  return (
    <>
      <Grid container>
        <AlertAlteracaoRegimeTributario
          assinaturaValida={assinaturaValida}
          isLoadingSolicitarAlteracaoPerfilTributario={
            isLoadingSolicitarAlteracaoPerfilTributario
          }
          urlAssinatura={urlAssinatura}
        />

        <Aliquotas loading={isLoading} />
        <CheckAlteracaoRegimeTributario />

        <AssinaturaAlteracaoRegimeTributario
          obterAssinatura={obterAssinatura}
        />
      </Grid>

      <AlteracaoRegimeTributarioBotoes
        isLoading={isLoading}
        podeExibirAssinatura={podeExibirAssinatura}
        assinaturaValida={assinaturaValida}
        handleSolicitarAlteracaoPerfilTributario={
          handleSolicitarAlteracaoPerfilTributario
        }
        urlAssinatura={urlAssinatura}
      />
    </>
  );
};
export default FluxoAlteracaoRegimeTributario;
