import {
  checkIfSomeItemsAreTrue,
  ITabelaAliquotas,
  useListarAlicotasAgrupadas,
  TAliquotaRecord,
  obterDadosTabelaAliquotas,
  useObterNumeroResgate,
  TUseObterTabelasAliquotas,
} from '@src/features/financeiro/alteracaoRegimeTributario/exports';

export const useObterTabelasAliquotas = (): TUseObterTabelasAliquotas => {
  const { obterNumeroResgate, loading } = useObterNumeroResgate();
  const {
    listarAliquotasAgrupadas,
    listarFundosAliquotas,
    isLoadingListarAlicotasAgrupadas,
  } = useListarAlicotasAgrupadas();

  const obterTabelasAliquotas = async (): Promise<
    TAliquotaRecord<ITabelaAliquotas[]>
  > => {
    const numeroResgateAliquotas = await obterNumeroResgate();

    const dadosListaAliquotasAgrupadasRegressivo =
      await listarAliquotasAgrupadas({
        numeroResgate: numeroResgateAliquotas.regressivo,
      });

    const dadosListaAliquotasAgrupadasProgressivo =
      await listarAliquotasAgrupadas({
        numeroResgate: numeroResgateAliquotas.progressivo,
      });

    const listaFundosAliquotaProgressiva = listarFundosAliquotas(
      dadosListaAliquotasAgrupadasProgressivo,
    );
    const listaFundosAliquotaRegressiva = listarFundosAliquotas(
      dadosListaAliquotasAgrupadasRegressivo,
    );

    return {
      progressivo: obterDadosTabelaAliquotas(listaFundosAliquotaProgressiva),
      regressivo: obterDadosTabelaAliquotas(listaFundosAliquotaRegressiva),
    };
  };

  return {
    obterTabelasAliquotas,
    loading: checkIfSomeItemsAreTrue([
      loading,
      isLoadingListarAlicotasAgrupadas,
    ]),
  };
};
