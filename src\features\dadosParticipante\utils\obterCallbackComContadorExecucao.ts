import {
  checkErroSalvarDadosParticipante,
  IHandleReponseResult,
} from '../exports';

export function obterCallbackComContadorExecucao(
  contadorExecucao: VoidFunction,
) {
  return async function executarCallbackComParametroSeCondicaoIgualTrue<T>(
    condicao: boolean,
    callback: (payload: T) => Promise<IHandleReponseResult<unknown>>,
    payload: T,
  ): Promise<boolean> {
    if (condicao) {
      const response = await callback(payload);
      contadorExecucao();
      return checkErroSalvarDadosParticipante(response);
    }
    return false;
  };
}
