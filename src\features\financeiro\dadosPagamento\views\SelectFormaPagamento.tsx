import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const SelectFormaPagamento: React.FC = () => {
  const { handleNovaContaChange } = DadosPagamento.useAlterarDadosPagamento();
  const { permissoesMatrizAcesso } = DadosPagamento.useContext(
    DadosPagamento.MatrizAcessoContext,
  );

  const isSelectFormaPagamentoDisabled =
    !DadosPagamento.verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      permissoesMatrizAcesso,
      DadosPagamento.ALTERACOES_COMPONENTES_PERMISSOES
        .alterar_forma_dados_pagamento.permissions,
    );

  return (
    <DadosPagamento.GridItem xs="1 / 2">
      <DadosPagamento.Text variant="text-large-700">
        Forma de Pagamento
      </DadosPagamento.Text>
      <DadosPagamento.Select
        options={DadosPagamento.FORMA_DE_PAGAMENTO}
        size="standard"
        onChange={([item]) =>
          handleNovaContaChange(
            DadosPagamento.changeEventBuilder('formaPagamento', item.value),
          )
        }
        selectedValues={[
          DadosPagamento.tryGetValueOrDefault(
            [DadosPagamento.TEXT_DEBITO_CONTA],
            '-',
          ),
        ]}
        variant="box-classic"
        sizeWidth={undefined}
        disabled={isSelectFormaPagamentoDisabled}
      />
    </DadosPagamento.GridItem>
  );
};

export default SelectFormaPagamento;
