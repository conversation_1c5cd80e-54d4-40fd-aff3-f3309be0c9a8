import * as DADOS_PAGAMENTO_TYPES from './DadosPagamento';

export interface IRequestListarContasBancarias {
  cpfCnpj: string;
  numeroCertificado: string;
}

export interface IRequestResponsavelFinanceiro {
  cpfCnpj: string;
  numeroCertificado: string;
}

export interface IRequestRecuperarContribuicoesCertificado {
  cpfCnpj: string;
  numeroCertificado: string;
  categoria: string;
}

export interface IRequestCriarCanalPagamento {
  idPessoa: string;
  metodoPagamento: string;
  cpfCnpj: string;
  dadosPagamento: DADOS_PAGAMENTO_TYPES.TDadosPagamento | undefined;
  canalId: string;
}
export interface IResponseCriarCanalPagamento {
  canalId: string;
}
export interface IRequestAtualizarFormaPagamento {
  cpfCnpj: string;
  numeroCertificado: string;
  tipoContribuicao: string;
  canalId: string;
}

export interface IRequestValidarConta {
  cpfCnpj: string;
  codigoAgencia: string;
  codigoOperacao: string;
  digitoVerificador: string;
  numeroBanco: string;
  numeroConta: string;
}

export interface IResponseValidarConta {
  codigoRetorno: string;
  descricaoMensagemAmigavel: string;
  descricaoMensagemSistema: string;
}
