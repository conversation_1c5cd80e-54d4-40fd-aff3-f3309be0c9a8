import {
  checkIfSomeItemsAreTrue,
  EEtapasAporte,
  IDadosFundosExistentesAporte,
  IDadosFundosNovosAporte,
  tryGetValueOrDefault,
  useAporteContext,
} from '@src/features/financeiro/aporte/exports';

type TUseFundosDistribuicao = {
  fundosDistribuicaoExistentesFiltrado: IDadosFundosExistentesAporte;
  fundosDistribuicaoNovosFiltrado: IDadosFundosNovosAporte;
  valorContribuicaoRestante: number;
  somaDistribuicaoEntreFundos: number;
  disableButton: boolean;
  etapa: EEtapasAporte;
  setEtapa: React.Dispatch<React.SetStateAction<EEtapasAporte>>;
};

export const useFundosDistribuicao = (): TUseFundosDistribuicao => {
  const {
    etapa,
    fundos,
    filtroTabelaFundos,
    fundoSelecionado,
    formikValorDistribuido,
    setEtapa,
  } = useAporteContext();

  const fundosDistribuicaoExistentes = fundos?.dadosFundosExistentes;
  const fundosDistribuicaoNovos = fundos?.dadosFundosNovos;

  const somaDistribuicaoEntreFundos = fundoSelecionado.reduce(
    (acc, item) => acc + parseFloat(item.valorContribuicao),
    0,
  );

  const valorContribuicaoRestante =
    parseFloat(filtroTabelaFundos.valorContribuicao) -
    somaDistribuicaoEntreFundos;

  const filtrarFundosPorPerfil = <T extends { descPerfilFundo: string }>(
    dadosFundos: T[] | undefined,
    perfisInvestidor: string[],
  ): T[] =>
    tryGetValueOrDefault(
      [
        dadosFundos?.filter(item =>
          perfisInvestidor.includes(item.descPerfilFundo.toLowerCase()),
        ),
      ],
      [],
    );

  const fundosDistribuicaoExistentesFiltrado = {
    ...fundosDistribuicaoExistentes,
    dadosFundos: tryGetValueOrDefault(
      [
        filtrarFundosPorPerfil(
          fundosDistribuicaoExistentes?.dadosFundos,
          filtroTabelaFundos.perfilInvestidor,
        ),
      ],
      [],
    ),
  };

  const fundosDistribuicaoNovosFiltrado = {
    ...fundosDistribuicaoNovos,
    dadosFundos: tryGetValueOrDefault(
      [
        filtrarFundosPorPerfil(
          fundosDistribuicaoNovos?.dadosFundos,
          filtroTabelaFundos.perfilInvestidor,
        ),
      ],
      [],
    ),
  };

  const disableButton = checkIfSomeItemsAreTrue([
    fundoSelecionado.length === 0,
    somaDistribuicaoEntreFundos !==
      parseFloat(filtroTabelaFundos.valorContribuicao),
    !formikValorDistribuido.isValid,
  ]);

  return {
    fundosDistribuicaoExistentesFiltrado,
    fundosDistribuicaoNovosFiltrado,
    valorContribuicaoRestante,
    somaDistribuicaoEntreFundos,
    disableButton,
    etapa,
    setEtapa,
  };
};
