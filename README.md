# Microfrontend de Previdência para o segmento de PosVenda no Plataforma Caixa
Um parágrafo da descrição do projeto vai aqui


## 🚀 Começando
Essas instruções permitirão que você obtenha uma cópia do projeto em operação na sua máquina local para fins de desenvolvimento e teste.


### 📋 Pré-requisitos
De que coisas você precisa para instalar o software e como instalá-lo?

```
Dar exemplos
```


### 🔧 Instalação
Uma série de exemplos passo-a-passo que informam o que você deve executar para ter um ambiente de desenvolvimento em execução.

Diga como essa etapa será:

```
Dar exemplos
```

E repita:

```
Até finalizar
```

Termine com um exemplo de como obter dados do sistema ou como usá-los para uma pequena demonstração.


## ⚙️ Executando os testes
Explicar como executar os testes automatizados para este sistema.


### 🔩 Analise os testes de ponta a ponta
Explique que eles verificam esses testes e porquê.

```
Dar exemplos
```


### ⌨️ E testes de estilo de codificação
* [React Test Library](https://testing-library.com/docs/react-testing-library/intro/)
* [Jest](https://jestjs.io/pt-BR/)
* [MirageJS](https://miragejs.com/)
* [FakerJs](https://www.npmjs.com/package/Faker)

```
Dar exemplos
```


## 📦 Desenvolvimento
Adicione notas adicionais sobre como implantar isso em um sistema ativo


## 🛠️ Construído com
* React
* [Typescript](https://www.typescriptlang.org/)
* [React Query](https://react-query.tanstack.com/)
* [Styled Components](https://styled-components.com/)
* [Design System](https://devops.caixaseguradora.com.br/CVP/GEFEN.Applications/_packaging?_a=package&feed=NPM-Local%40Local&package=%40gcs%2Fdesign-system&protocolType=Npm&version=1.5.2)


## ✒️ Autores
Mencione todos aqueles que ajudaram a levantar o projeto desde o seu início

* **Um desenvolvedor** - *Trabalho Inicial* - [umdesenvolvedor](https://github.com/linkParaPerfil)
* **Fulano De Tal** - *Documentação* - [fulanodetal](https://github.com/linkParaPerfil)


## 📄 Licença
Este projeto está sob a licença (sua licença) - veja o arquivo [LICENSE.md](https://github.com/usuario/projeto/licenca) para detalhes.


## 🎁 Expressões de gratidão
* Conte a outras pessoas sobre este projeto 📢
* Convide alguém da equipe para uma cerveja 🍺 
* Obrigado publicamente 🤓.
* etc.

Um parágrafo da descrição do projeto vai aqui

## 🚀 Começando

Essas instruções permitirão que você obtenha uma cópia do projeto em operação na sua máquina local para fins de desenvolvimento e teste.

### 📋 Pré-requisitos

De que coisas você precisa para instalar o software e como instalá-lo?

```
Dar exemplos
```

### 🔧 Instalação

Uma série de exemplos passo-a-passo que informam o que você deve executar para ter um ambiente de desenvolvimento em execução.

Diga como essa etapa será:

```
Dar exemplos
```

E repita:

```
Até finalizar
```

Termine com um exemplo de como obter dados do sistema ou como usá-los para uma pequena demonstração.

## ⚙️ Executando os testes

Explicar como executar os testes automatizados para este sistema.

### 🔩 Analise os testes de ponta a ponta

Explique que eles verificam esses testes e porquê.

```
Dar exemplos
```

### ⌨️ E testes de estilo de codificação

- [React Test Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Jest](https://jestjs.io/pt-BR/)
- [MirageJS](https://miragejs.com/)
- [FakerJs](https://www.npmjs.com/package/Faker)

```
Dar exemplos
```

## 📦 Desenvolvimento

Adicione notas adicionais sobre como implantar isso em um sistema ativo

## 🛠️ Construído com

- React
- [Typescript](https://www.typescriptlang.org/)
- [React Query](https://react-query.tanstack.com/)
- [Styled Components](https://styled-components.com/)
- [Design System](https://devops.caixaseguradora.com.br/CVP/GEFEN.Applications/_packaging?_a=package&feed=NPM-Local%40Local&package=%40gcs%2Fdesign-system&protocolType=Npm&version=1.5.2)

## ✒️ Autores

Mencione todos aqueles que ajudaram a levantar o projeto desde o seu início

- **Um desenvolvedor** - _Trabalho Inicial_ - [umdesenvolvedor](https://github.com/linkParaPerfil)
- **Fulano De Tal** - _Documentação_ - [fulanodetal](https://github.com/linkParaPerfil)

## 📄 Licença

Este projeto está sob a licença (sua licença) - veja o arquivo [LICENSE.md](https://github.com/usuario/projeto/licenca) para detalhes.

## 🎁 Expressões de gratidão

- Conte a outras pessoas sobre este projeto 📢
- Convide alguém da equipe para uma cerveja 🍺
- Obrigado publicamente 🤓.
- etc.
