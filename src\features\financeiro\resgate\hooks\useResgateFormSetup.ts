import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useResgateFormSetup = (): Resgate.IUseResgateFormSetup => {
  const { isLoadingListaFundosParaResgate, listaFundosParaResgate } =
    Resgate.useListarFundosParaResgate();

  const {
    valorMinimoResgate,
    valorMinimoPermanencia,
  }: Partial<Resgate.IListarFundosParaResgateLimitesCertificado> =
    Resgate.tryGetValueOrDefault<
      | Record<string, number>
      | Resgate.IListarFundosParaResgateLimitesCertificado
    >([listaFundosParaResgate?.limitesCertificado], {});

  const saldoTotal: number = Resgate.tryGetValueOrDefault(
    [listaFundosParaResgate?.saldo?.saldoTotal],
    0,
  );

  const initialStateFormik: Resgate.IFormikValuesSimulacaoResgate = {
    ...Resgate.INITIAL_STATE_FORMIK,
    fundosParaResgate: Resgate.tryGetValueOrDefault(
      [listaFundosParaResgate?.fundosDisponiveis],
      [],
    ),
  };

  const validationSchema = Resgate.validarResgateSchemaFactory();

  const formik: Resgate.FormikProps<Resgate.IFormikValuesSimulacaoResgate> =
    Resgate.useFormik({
      enableReinitialize: true,
      initialValues: initialStateFormik,
      validateOnMount: true,
      validationSchema,
      validate: values =>
        Resgate.validarFundosParaResgate(
          values.fundosParaResgate,
          valorMinimoResgate,
        ),
      onSubmit: () => undefined,
    });

  return {
    isLoadingListaFundosParaResgate,
    listaFundosParaResgate,
    valorMinimoResgate,
    valorMinimoPermanencia,
    saldoTotal,
    formik,
  };
};
