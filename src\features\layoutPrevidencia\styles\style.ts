import { getTernaryResult, tryGetValueOrDefault } from '@cvp/utils';
import styled from 'styled-components';

interface IBadgeProps {
  $height?: string;
  $transparent?: boolean;
}

export const Badge = styled.div<IBadgeProps>(({ $height, $transparent }) => ({
  height: tryGetValueOrDefault([$height], '56px'),
  width: '85px',
  float: 'left',
  marginRight: '10px',
  background: getTernaryResult(!!$transparent, 'none', '#d3dee345'),
}));

export const CertificadoPrevidenciaWrapper = styled.div``;

export const CertificadoContentLoadingContainer = styled.div`
  display: flex;
  height: 12.5rem;
  align-items: center;
  justify-content: center;
`;
