import { GridItem, Utils } from '@src/features/layoutPrevidencia/exports';
import styled from 'styled-components';

interface IBadgeProps {
  $height?: string;
  $transparent?: boolean;
}

export const Badge = styled.div<IBadgeProps>(({ $height, $transparent }) => ({
  height: Utils.tryGetValueOrDefault([$height], '56px'),
  width: '85px',
  float: 'left',
  marginRight: '10px',
  background: Utils.getTernaryResult(!!$transparent, 'none', '#d3dee345'),
}));

export const CertificadoPrevidenciaWrapper = styled.div``;

export const CertificadoContentLoadingContainer = styled.div`
  display: flex;
  height: 12.5rem;
  align-items: center;
  justify-content: center;
`;

export const GridItemPersonalizado = styled(GridItem)`
  align-items: center;
  display: flex;
  gap: 5px;
`;
