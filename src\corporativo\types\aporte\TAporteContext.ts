import {
  EEtapasAporte,
  IDadosBancariosAporte,
  IEfetuarAporteResponse,
  IFiltroTabelaAporte,
  IItensExtrato,
  IObterFundosResponse,
  IValorContribuicao,
  TFormikProps,
} from '@src/features/financeiro/aporte/exports';
import { IFundosAporte } from '../financeiro/aporte/IDadosTabelaFundos';

export type TAporteDataContext = {
  fundos: IObterFundosResponse | undefined;
  fundoSelecionado: IFundosAporte[];
  filtroTabelaFundos: IFiltroTabelaAporte;
  etapa: EEtapasAporte;
  origemRecurso: string;
  contaBancariaSelecionada: IDadosBancariosAporte;
  contasBancarias: IDadosBancariosAporte[];
  aporte: IEfetuarAporteResponse | undefined;
  tipoContaBancaria: string;
  formikFiltrosTabelaAporte: TFormikProps<IFiltroTabelaAporte>;
  itensExtrato: IItensExtrato[];
  formikValorDistribuido: TFormikProps<IValorContribuicao>;
  assinatura: boolean;
};

export type TAporteContext = TAporteDataContext & {
  setFundos: React.Dispatch<
    React.SetStateAction<IObterFundosResponse | undefined>
  >;
  setFundoSelecionado: React.Dispatch<React.SetStateAction<IFundosAporte[]>>;
  setFiltroTabelaAporte: React.Dispatch<
    React.SetStateAction<IFiltroTabelaAporte>
  >;
  setEtapa: React.Dispatch<React.SetStateAction<EEtapasAporte>>;
  setOrigemRecurso: React.Dispatch<React.SetStateAction<string>>;
  setContaBancariaSelecionada: React.Dispatch<
    React.SetStateAction<IDadosBancariosAporte>
  >;
  setContasBancarias: React.Dispatch<
    React.SetStateAction<IDadosBancariosAporte[]>
  >;
  setAporte: React.Dispatch<
    React.SetStateAction<IEfetuarAporteResponse | undefined>
  >;
  setTipoContaBancaria: React.Dispatch<React.SetStateAction<string>>;
  setItensExtrato: React.Dispatch<React.SetStateAction<IItensExtrato[]>>;
  setAssinatura: React.Dispatch<React.SetStateAction<boolean>>;
};
