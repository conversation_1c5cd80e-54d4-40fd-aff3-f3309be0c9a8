import * as AtivacaoSuspensao from '../exports';

export const useAssinaturaAtivacaoSuspensao = () => {
  const [assinando, setAssinando] = AtivacaoSuspensao.useState(false);
  const [assinado, setAssinado] = AtivacaoSuspensao.useState(false);

  const assinarDocumento = async () => {
    setAssinando(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setAssinado(true);
    } catch (error) {
      console.error('Erro ao assinar documento:', error);
    } finally {
      setAssinando(false);
    }
  };

  const resetarAssinatura = () => {
    setAssinado(false);
    setAssinando(false);
  };

  return {
    assinando,
    assinado,
    assinarDocumento,
    resetarAssinatura,
  };
};
