import { INomeSocialPayload } from '@src/corporativo/types/dadosParticipante/consultar/IUseAtualizarNomeSocial';

export const obterPayloadNomeSocial = (
  nomeSocialForm?: string,
  nomeSocialOriginal?: string,
): INomeSocialPayload | null => {
  if (nomeSocialForm?.length && !nomeSocialOriginal?.length) {
    return {
      indUsarNomeSocial: 'S',
      nomeSocial: nomeSocialForm,
      tipoOperacao: 'INCLUSAO',
    };
  }

  if (!nomeSocialForm?.length && nomeSocialOriginal?.length) {
    return {
      indUsarNomeSocial: 'N',
      nomeSocial: nomeSocialOriginal,
      tipoOperacao: 'EXCLUSAO',
    };
  }

  if (nomeSocialForm?.length && nomeSocialOriginal?.length) {
    return {
      indUsarNomeSocial: 'S',
      nomeSocial: nomeSocialForm,
      tipoOperacao: 'ALTERACAO',
    };
  }

  return null;
};
