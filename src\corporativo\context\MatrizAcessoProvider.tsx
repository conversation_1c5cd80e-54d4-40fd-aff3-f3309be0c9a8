import {
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { TTabItem } from '@src/features/layoutPrevidencia/types/tabsTypes';
import { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
import { MatrizAcessoContext } from './MatrizAcessoContext';
import useMatrizAcesso from '../hooks/useMatrizAcesso';
import { PrevidenciaContext } from './PrevidenciaContext';

const MatrizAcessoProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const [permissoesMatrizAcesso, setPermissoesMatrizAcesso] = useState<
    string[]
  >([]);
  const [certificadoTabs, setCertificadoTabs] = useState<TTabItem[]>([
    { label: '', value: '' },
  ]);
  const [
    modalImprimirDocumentosPrimeiroSelectOpcoes,
    setModalImprimirDocumentosPrimeiroSelectOpcoes,
  ] = useState<SelectItem[]>([{ text: '', value: '' }]);
  const {
    isMatrizAcessoLoading,
    fetchMatrizAcesso,
    setPermissoesUsuarioContexto,
  } = useMatrizAcesso();

  useEffect(() => {
    if (certificadoAtivo && certificadoAtivo.certificadoNumero) {
      fetchMatrizAcesso().then(matrizAcesso =>
        setPermissoesUsuarioContexto(
          matrizAcesso,
          certificadoAtivo,
          setPermissoesMatrizAcesso,
          setModalImprimirDocumentosPrimeiroSelectOpcoes,
          setCertificadoTabs,
        ),
      );
    }
  }, [certificadoAtivo]);

  const matrizAcessoProviderValue = useMemo(
    () => ({
      permissoesMatrizAcesso,
      certificadoTabs,
      modalImprimirDocumentosPrimeiroSelectOpcoes,
      isMatrizAcessoLoading,
    }),
    [
      permissoesMatrizAcesso,
      certificadoTabs,
      modalImprimirDocumentosPrimeiroSelectOpcoes,
      isMatrizAcessoLoading,
    ],
  );

  return (
    <MatrizAcessoContext.Provider value={matrizAcessoProviderValue}>
      {children}
    </MatrizAcessoContext.Provider>
  );
};

export default MatrizAcessoProvider;
