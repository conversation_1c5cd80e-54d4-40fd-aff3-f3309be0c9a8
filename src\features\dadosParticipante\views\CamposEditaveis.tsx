import * as DadosParticipante from '../exports';

interface ICamposEditaveisProps {
  dadosParticipante: DadosParticipante.IDadosParticipanteFormEntidade;
  editando: boolean;
}

export const CamposEditaveis: React.FC<ICamposEditaveisProps> = ({
  dadosParticipante,
  editando,
}) => {
  return (
    <DadosParticipante.For each={DadosParticipante.camposDadosParticipante()}>
      {(linhaDadosParticipante, index) => (
        <DadosParticipante.Grid key={index}>
          <DadosParticipante.For each={linhaDadosParticipante}>
            {campo => (
              <DadosParticipante.SwitchCase
                fallback={
                  <DadosParticipante.CampoTextoDadosParticipante
                    fracao={campo.fracao}
                    label={campo.label}
                    text={DadosParticipante.Utils.tryGetValueOrDefault(
                      [dadosParticipante[campo.campo as 'nome' | 'email']],
                      '',
                    )}
                  />
                }
              >
                <DadosParticipante.Match when={editando && campo.editavel}>
                  <DadosParticipante.CampoDadosParticipante
                    editavel={Boolean(campo.editavel)}
                    key={campo.label}
                    name={campo.campo}
                    fracao={campo.fracao}
                    label={campo.label}
                    mask={DadosParticipante.Utils.tryGetValueOrDefault(
                      [campo.mask],
                      val => val,
                    )}
                    disabled={campo.disabled}
                  />
                </DadosParticipante.Match>
              </DadosParticipante.SwitchCase>
            )}
          </DadosParticipante.For>
          <DadosParticipante.Divider />
        </DadosParticipante.Grid>
      )}
    </DadosParticipante.For>
  );
};
