import {
  IUseObterComprovanteResgate,
  IUseValidarContaRetorno,
  TUseConsultarDadosPep,
  TUseEfetuarAporte,
  TUseObterDadosBancarios,
  TUseObterDatas,
  TUseObterExtratoUnificado,
  TUseObterFundosDistribuicao,
  TUseValidarCampoAporte,
} from '@src/features/financeiro/aporte/exports';

export type TAporteServiceContext = {
  clientePep: TUseConsultarDadosPep;
  fundosDistribuicao: TUseObterFundosDistribuicao;
  validarCamposAporte: TUseValidarCampoAporte;
  efetuarAporte: TUseEfetuarAporte;
  obterDadosBancarios: TUseObterDadosBancarios;
  obterComprovanteResgate: IUseObterComprovanteResgate;
  validarConta: IUseValidarContaRetorno;
  datasDebito: TUseObterDatas;
  extratoUnificado: TUseObterExtratoUnificado;
};
