import {
  FormikProps,
  IFormikValuesSimulacaoResgate,
  IListarFundosParaResgateResponse,
} from '@src/features/financeiro/resgate/exports';

export interface IUseResgateFormSetup {
  isLoadingListaFundosParaResgate: boolean;
  listaFundosParaResgate: IListarFundosParaResgateResponse;
  valorMinimoResgate: number | undefined;
  valorMinimoPermanencia: number | undefined;
  saldoTotal: number;
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
}
