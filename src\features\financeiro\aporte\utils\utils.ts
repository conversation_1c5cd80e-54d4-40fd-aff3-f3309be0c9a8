import {
  getTernaryResult,
  IFundosAporte,
  TIPO_OPERACAO,
  tryGetValueOrDefault,
  Yup,
} from '@src/features/financeiro/aporte/exports';

interface IFindFundoId {
  fundoSelecionado: IFundosAporte[];
  id: string;
}

export const obterOperacao = (operacao: string): string => {
  return tryGetValueOrDefault(
    [TIPO_OPERACAO.find(item => item.id === operacao)?.valor],
    '-',
  );
};

export const tipoDaConta = (value: number): 3 | 4 => {
  const tipoContaSidec = 3;
  const tipoContaNsgd = 4;

  if (value > 11) {
    return tipoContaNsgd;
  }
  return tipoContaSidec;
};

export const obterNumeroOperacao = (value: string): string => {
  return getTernaryResult(
    value.length > 0,
    value.substring(0, tipoDaConta(value.length)),
    '',
  );
};

export const obterNumeroContaSemOperacao = (value: string): string => {
  return getTernaryResult(
    value.length > 0,
    value.substring(tipoDaConta(value.length), value.length),
    '',
  );
};

export const validationSchemaFiltrosTabelaAporte = Yup.object({
  perfilInvestidor: Yup.array()
    .min(1, 'Selecione pelo menos um perfil de investidor')
    .required('Este campo é obrigatório'),
  valorContribuicao: Yup.number()
    .positive('O valor deve ser maior que 0')
    .min(50, 'O valor de contribuição deve ser maior que 50 reais')
    .required('O valor de contribuição é obrigatório'),
  dataDebito: Yup.string()
    .min(1, 'Selecione pelo menos uma data de débito')
    .required('Este campo é obrigatório'),
});

export const validacaoCadastrarNovaContaBancaria = Yup.object().shape({
  operacao: Yup.string().required('Campo obrigatório'),
  agencia: Yup.string()
    .required('Campo obrigatório')
    .matches(/^\d{1,4}$/, 'Digite no máximo 4 dígitos'),
  contaBancaria: Yup.string()
    .required('Campo obrigatório')
    .matches(/^\d{1,12}$/, 'Digite no máximo 12 dígitos'),
  digito: Yup.string()
    .required('Campo obrigatório')
    .matches(/^\d{1}$/, 'Digite no máximo 1 dígito'),
});

export const findFundoId = ({
  fundoSelecionado,
  id,
}: IFindFundoId): boolean => {
  return fundoSelecionado.some(prev => prev.fundoId === id);
};

export const ultimoDiaDoMes = (data: Date): Date => {
  const year = data.getFullYear();
  const month = data.getMonth();
  return new Date(year, month + 1, 0);
};
