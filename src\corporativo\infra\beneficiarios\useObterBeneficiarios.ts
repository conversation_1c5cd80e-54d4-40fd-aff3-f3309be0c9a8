import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { TUseObterBeneficiarios } from '@src/corporativo/types/beneficiarios/TUseObterBeneficiarios';
import {
  ICertificadoCoberturas,
  ICertificadoCoberturasResponse,
} from '@src/corporativo/types/consultaCertificado/Response/ICertificadoCoberturasResponse';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { useContext } from 'react';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

interface ICertificadosPrevidenciaPayload {
  Cpf: string | null;
  NumeroCertificado: string;
}
const ARRAY_VAZIO = {} as ICertificadoCoberturas;
const useObterBeneficiarios: TUseObterBeneficiarios = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      ICertificadosPrevidenciaPayload,
      ICertificadoCoberturasResponse
    >(PECOS.ObterCoberturas, {
      data: {
        Cpf: String(getSessionItem('cpfCnpj')),
        NumeroCertificado: certificadoAtivo?.certificadoNumero,
      },
      autoFetch: true,
    });

  return {
    loading,
    response: tryGetValueOrDefault([response?.entidade?.retorno], ARRAY_VAZIO),
    invocarApiGatewayCvpComToken,
  };
};

export default useObterBeneficiarios;
