import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';
import { PropsWithChildren } from 'react';

export const HistoricoPagamentosContainer: React.FC<
  PropsWithChildren<DadosPagamento.TPagamentosHistoricoProps>
> = ({ children, loading }) => {
  return (
    <DadosPagamento.ConditionalRenderer
      condition={DadosPagamento.checkIfAllItemsAreTrue([!loading])}
      fallback={
        <DadosPagamento.LoadingSpinner size="small">
          <DadosPagamento.Text
            variant="text-small-400"
            fontColor="brand-primary-09"
          >
            Carregando histórico de pagamentos...
          </DadosPagamento.Text>
        </DadosPagamento.LoadingSpinner>
      }
    >
      {children}
    </DadosPagamento.ConditionalRenderer>
  );
};
