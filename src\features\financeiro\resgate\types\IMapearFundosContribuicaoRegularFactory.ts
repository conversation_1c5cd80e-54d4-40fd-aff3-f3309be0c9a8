import { IConsultarContribuicaoRegularFundos } from '@src/features/financeiro/resgate/exports';

export interface IMapearFundosContribuicaoRegularFactory {
  fundos: IConsultarContribuicaoRegularFundos[] | undefined;
  selecionarFundoParaContribuicao: (codigoFundo: string) => void;
}

export interface IMapearFundosContribuicaoRegularFactoryReturn {
  selecionar: React.ReactElement;
  perfilFundo: React.ReactElement;
  rentabilidade: string;
  valorResgatado: React.ReactElement;
  codigoReserva: string;
  codigoFundo: string;
  descricaoFundo: string;
  saldo: number;
  perfilRisco: string;
  fundoEmCarencia: boolean;
  avisoCarencia: string;
  valorContribuicao?: number;
  selecionado?: boolean;
}
