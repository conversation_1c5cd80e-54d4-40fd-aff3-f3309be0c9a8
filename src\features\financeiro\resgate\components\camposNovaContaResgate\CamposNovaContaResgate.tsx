import * as Resgate from '@src/features/financeiro/resgate/exports';

export const CamposNovaContaResgate = ({
  onChange,
}: Resgate.ICamposNovaContaResgateProps): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();

  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <>
      <Resgate.GridItem xs="1" lg="3/10">
        <Resgate.ContainerSelectFormBancarioCustom>
          <Resgate.Text variant="text-standard-600">Banco</Resgate.Text>
          <Resgate.Select
            id="banco"
            searchable
            onChange={event => {
              formik.setValues({
                ...formik.values,
                novaConta: {
                  ...formik.values.novaConta,
                  banco: {
                    label: Resgate.tryGetValueOrDefault([event[0]?.text], ''),
                    value: Resgate.tryGetValueOrDefault([event[0]?.value], ''),
                  },
                },
              });
            }}
            options={Resgate.mapearSelectFormFactory({
              lista: resgateFeatureData.dadosBancarios,
              getText: banco => banco.nomeBanco.trim(),
              getValue: banco => banco.codigoBanco.toString(),
            })}
            placeholder="Selecione"
            size="standard"
            sizeWidth="standard"
            variant="box-classic"
          />
        </Resgate.ContainerSelectFormBancarioCustom>
      </Resgate.GridItem>
      {Resgate.LISTA_CAMPOS_NOVA_CONTA_RESGATE.map(field => (
        <Resgate.GridItem
          key={field}
          xs="1"
          lg={Resgate.getTernaryResult(
            field === Resgate.CAMPOS_NOVA_CONTA_RESGATE.digito,
            '1/10',
            '2/10',
          )}
        >
          <Resgate.ContainerInputFormBancarioCustom>
            <Resgate.Text variant="text-standard-600">
              {Resgate.capitalize(field)}
            </Resgate.Text>
            <Resgate.InputText
              type="text"
              placeholder=""
              size="small"
              variant="box-classic"
              name={field}
              id={field}
              onChange={event => onChange(field, event.target.value)}
              value={formik.values.novaConta[field]}
              arialabel={Resgate.capitalize(field)}
            />
          </Resgate.ContainerInputFormBancarioCustom>
        </Resgate.GridItem>
      ))}
    </>
  );
};
