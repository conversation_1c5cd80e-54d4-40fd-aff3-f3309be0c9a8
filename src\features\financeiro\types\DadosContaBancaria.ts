export type TNumeroContaECodigoOperacao = {
  numeroConta: string;
  operacao: string;
};

export type TResponseValidarConta = {
  codigoRetorno: string;
  descricaoMensagemAmigavel: string;
  descricaoMensagemSistema: string;
};

export interface IRequestValidarConta {
  cpfCnpj: string;
  codigoAgencia: string;
  codigoOperacao: string;
  digitoVerificador: string;
  numeroBanco: string;
  numeroConta: string;
}
