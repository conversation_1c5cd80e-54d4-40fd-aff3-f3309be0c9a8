import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  getTernaryResult,
  IFundosAporte,
  tryGetValueOrDefault,
} from '@src/features/financeiro/aporte/exports';

const useDesabilitarFundo =
  () =>
  (
    tipoFundo: string,
    quantidadeFundosSelecionados: number,
    quantidadeMaximaSelecao: string,
    fundosSelecionados: IFundosAporte[],
    isChecked?: boolean,
  ): ((fundo: IFundosAporte) => boolean) => {
    return (fundo: IFundosAporte): boolean => {
      const todosPossuemTipoFundo = fundosSelecionados?.every(
        fundoSelecionado => fundoSelecionado.tipoFundo !== undefined,
      );

      const fundoSelecionadoId = fundosSelecionados?.some(
        item => item.fundoId === fundo.fundoId,
      );

      const fundosDoMesmoTipo = fundosSelecionados?.filter(
        fundoSelecionado => fundoSelecionado.tipoFundo === tipoFundo,
      );

      const limiteMaximoSelecao = Number(
        tryGetValueOrDefault([quantidadeMaximaSelecao], ''),
      );

      const limiteAtingido =
        getTernaryResult(
          todosPossuemTipoFundo,
          tryGetValueOrDefault([fundosDoMesmoTipo.length], 0),
          tryGetValueOrDefault([quantidadeFundosSelecionados], 0),
        ) >= limiteMaximoSelecao;

      return checkIfSomeItemsAreTrue([
        getTernaryResult(
          checkIfAllItemsAreTrue([limiteAtingido, !fundoSelecionadoId]),
          true,
          false,
        ),
        tryGetValueOrDefault([isChecked], false),
      ]);
    };
  };

export default useDesabilitarFundo;
