import {
  CONSTANTES,
  Ds,
  Fac,
  Types,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

type TTabelaAtualizacoesProps = {
  data: Types.IResponseHistoricoAtualizacoes[];
  consultarDetalhes: (ano: string) => void;
};

export const TabelaAtualizacoes: React.FC<TTabelaAtualizacoesProps> = ({
  data,
  consultarDetalhes,
}) => {
  return (
    <Ds.TabelaPrevidencia
      themeTable="default"
      striped
      columns={Fac.obterColunasHistoricoAtualizacoes(row => {
        consultarDetalhes(row.numAno);
      })}
      data={data}
      noDataComponent={CONSTANTES.TEXTOS.SEM_DADOS}
    />
  );
};
