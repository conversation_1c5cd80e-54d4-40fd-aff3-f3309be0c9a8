import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { obterProximoMes } from '../utils/contribuicoes';
import {
  TPayloadContribuicoes,
  TResponseContribuicoes,
} from '../types/DadosPagamento';

const ARRAY_VAZIO = [] as TResponseContribuicoes[];

export const useObterContribuicoes = (
  numeroCertificado: string,
): {
  data: TResponseContribuicoes[];
  loading: boolean;
} => {
  const payload: TPayloadContribuicoes = {
    numeroCertificado,
    Cpf: String(getSessionItem('cpfCnpj')),
    DataVencimentoInicial: new Date('1900-01-01').toISOString(),
    DataVencimentoFinal: obterProximoMes(),
  };

  const { loading, response } = useApiGatewayCvpInvoker<
    TPayloadContribuicoes,
    TResponseContribuicoes[]
  >(PECOS.ObterPagamento, {
    data: payload,
    autoFetch: true,
  });

  return {
    data: tryGetValueOrDefault([response?.entidade], ARRAY_VAZIO),
    loading,
  };
};
