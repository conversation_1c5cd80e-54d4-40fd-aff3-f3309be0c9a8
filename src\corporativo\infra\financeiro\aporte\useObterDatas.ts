import {
  FILTRO_APORTE,
  getSessionItem,
  IObterDatasPayload,
  PECOS,
  PrevidenciaContext,
  tryGetValueOrDefault,
  TUseObterDatas,
  useApiGatewayCvpInvoker,
  useContext,
} from '@src/features/financeiro/aporte/exports';

export const useObterDatas = (): TUseObterDatas => {
  const {
    certificadoAtivo: { certificadoNumero },
  } = useContext(PrevidenciaContext);

  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload = {
    cpfCnpj,
    canalId: FILTRO_APORTE.parametroDataPagamento,
  };

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<IObterDatasPayload, string[]>(PECOS.ObterDatas, {
      data: payload,
      autoFetch: true,
      cache: true,
      cacheKey: certificadoNumero,
    });

  return {
    loading,
    response: tryGetValueOrDefault([response?.entidade], []),
    invocarApiGatewayCvpComToken,
  };
};
