import React from 'react';
import ReactDOMClient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import App from './App';
import { enableHMR } from './utils/hmr-helper';
import './utils/hmr-setup'; // Configuração global do HMR
import './utils/runtime-error-recovery'; // Sistema de recuperação de runtime errors
import './utils/error-recovery-test'; // Utilitários de teste (só em desenvolvimento)

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: App,
  errorBoundary(err, info) {
    console.error('🚨 ErrorBoundary Single-SPA:', err, info);

    // Em desenvolvimento, tenta recuperação automática
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Tentando recuperação automática em 3 segundos...');
      setTimeout(() => {
        window.location.reload();
      }, 3000);

      return React.createElement(
        'div',
        {
          style: {
            padding: '20px',
            backgroundColor: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '4px',
            margin: '20px',
          },
        },
        [
          React.createElement(
            'h2',
            { key: 'title' },
            '⚠️ Erro de Desenvolvimento',
          ),
          React.createElement(
            'p',
            { key: 'message' },
            'Um erro foi detectado. A página será recarregada automaticamente em 3 segundos.',
          ),
          React.createElement(
            'button',
            {
              key: 'reload-btn',
              onClick: () => window.location.reload(),
              style: {
                padding: '8px 16px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                marginRight: '10px',
              },
            },
            'Recarregar Agora',
          ),
          React.createElement(
            'details',
            {
              key: 'details',
              style: { marginTop: '10px' },
            },
            [
              React.createElement(
                'summary',
                { key: 'summary' },
                'Detalhes do erro',
              ),
              React.createElement(
                'pre',
                {
                  key: 'error-stack',
                  style: {
                    backgroundColor: '#f8f9fa',
                    padding: '10px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto',
                    maxHeight: '200px',
                  },
                },
                err.stack,
              ),
              React.createElement(
                'pre',
                {
                  key: 'component-stack',
                  style: {
                    backgroundColor: '#f8f9fa',
                    padding: '10px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto',
                    maxHeight: '200px',
                  },
                },
                info.componentStack,
              ),
            ],
          ),
        ],
      );
    }

    // Em produção, mostra erro mais amigável
    return React.createElement(
      'div',
      {
        style: {
          padding: '20px',
          backgroundColor: '#f8d7da',
          border: '1px solid #f5c6cb',
          borderRadius: '4px',
          margin: '20px',
        },
      },
      [
        React.createElement('h2', { key: 'title' }, 'Ops! Algo deu errado'),
        React.createElement(
          'p',
          { key: 'message' },
          'Ocorreu um erro inesperado. Tente recarregar a página.',
        ),
        React.createElement(
          'button',
          {
            key: 'reload-btn',
            onClick: () => window.location.reload(),
            style: {
              padding: '8px 16px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
            },
          },
          'Recarregar Página',
        ),
      ],
    );
  },
});

export const { bootstrap, mount, unmount } = lifecycles;

// Habilita HMR para desenvolvimento
enableHMR();
