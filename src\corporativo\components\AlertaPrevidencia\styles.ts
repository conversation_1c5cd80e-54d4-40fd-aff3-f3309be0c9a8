import styled, { keyframes } from 'styled-components';

const grow = keyframes`
  0% {
    height: 0%;
  }
  100% {
    height: 80px;
  }
`;

const shrink = keyframes`
  0% {
    height: 80px;
  }
  100% {
    height: 0%;
  }
`;

export const AlertaApoliceContainer = styled.div<{ visible: boolean }>`
  height: 0;
  animation: ${grow} 0.3s forwards, ${shrink} 0.3s 6.4s forwards;
  overflow-y: hidden;
`;
