import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { ICalcularResgateResponse } from './ICalcularResgateResponse';

export interface IUseCalcularResgateRetorno {
  dadosCalculoResgate: ICalcularResgateResponse;
  isLoadingCalculoResgate: boolean;
  calcularResgate: (
    dynamicPayload?: IObterPayloadCalcularResgateFactoryRetorno,
  ) => Promise<IHandleReponseResult<ICalcularResgateResponse> | undefined>;
}

export interface IObterPayloadCalcularResgateFactoryRetorno {
  tipoRegimeTributario: string;
  tipoResgate: string;
  detalhesFundos: unknown;
  valorResgateTotal?: number;
}
