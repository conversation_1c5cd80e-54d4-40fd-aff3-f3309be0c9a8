import { PropsWithChildren } from 'react';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
} from '@cvp/componentes-posvenda';
import {
  ThemeProvider as DesignSystemCaixaProvider,
  Grid,
  GridItem,
} from '@cvp/design-system-caixa';
import api from '@src/corporativo/infra/config/api/axiosConfig';
import { ConfigApiGatewayConstants } from '@src/corporativo/constants/ConfigApiGatewayConstants';
import PrevidenciaContextProvider from '@src/corporativo/context/PrevidenciaContext';
import ModalImprimirDocumentosProvider from '@src/corporativo/context/ModalImprimirDocumentosProvider';
import MatrizAcessoProvider from '@src/corporativo/context/MatrizAcessoProvider';
import { AlertasProvider } from './AlertasContext';

const AppProviders: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <ComponentesPosVendaProvider>
      <DesignSystemCaixaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: ConfigApiGatewayConstants.configure.authMode,
            usernameKey: ConfigApiGatewayConstants.configure.usernameKey,
            storageMode: ConfigApiGatewayConstants.configure.storageMode,
            tokenStorageKey:
              ConfigApiGatewayConstants.configure.tokenStorageKey,
            operationPath: ConfigApiGatewayConstants.configure.operationPath,
          }}
        >
          <PrevidenciaContextProvider>
            <AlertasProvider>
              <Grid alignitem="center" justify="space-between">
                <GridItem
                  xs="1"
                  style={{ margin: '1.5rem', boxShadow: 'revert' }}
                >
                  <MatrizAcessoProvider>
                    <ModalImprimirDocumentosProvider>
                      {children}
                    </ModalImprimirDocumentosProvider>
                  </MatrizAcessoProvider>
                </GridItem>
              </Grid>
            </AlertasProvider>
          </PrevidenciaContextProvider>
        </ApiGatewayCvpProvider>
      </DesignSystemCaixaProvider>
    </ComponentesPosVendaProvider>
  );
};

export default AppProviders;
