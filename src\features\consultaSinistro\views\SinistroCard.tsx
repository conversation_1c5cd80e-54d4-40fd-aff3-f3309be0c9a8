import {
  Card,
  DetalheSinistro,
  IconSinistro,
  InfoSinistro,
  S,
  IDadosSinistroEntidade,
} from '../exports';

const CardSinistro: React.FC<IDadosSinistroEntidade> = ({
  numeroAvisoSinistro,
  codigoCertificado,
  dataSinistro,
  statusAvisoSinitro,
  andamentos,
}) => {
  return (
    <Card.Root variant="stroke">
      <S.CardSinistroStyled>
        <Card.Header
          title={`${numeroAvisoSinistro}`}
          subtitle="Sinistro"
          symbol={<IconSinistro size="big" />}
        />
        <InfoSinistro
          codigoCertificado={codigoCertificado}
          dataSinistro={dataSinistro}
          statusAvisoSinitro={statusAvisoSinitro}
        />
        <DetalheSinistro
          codigoCertificado={codigoCertificado}
          numeroAvisoSinistro={numeroAvisoSinistro}
          andamentos={andamentos}
        />
      </S.CardSinistroStyled>
    </Card.Root>
  );
};

export default CardSinistro;
