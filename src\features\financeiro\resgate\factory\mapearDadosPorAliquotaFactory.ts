import {
  IMapearDadosSelecaoAliquotaFactoryRetorno,
  getTernaryResult,
  IConsultarDetalhesDaAliquotaFactoryRetorno,
} from '@src/features/financeiro/resgate/exports';

/**
 * Mapear os dados da alíquota selecionada (progressiva ou regressiva)
 *
 * @param {boolean} isAliquotaProgressiva - Indica se a alíquota selecionada é progressiva
 * @param {IMapearDadosSelecaoAliquotaFactoryRetorno | undefined} dadosSelecaoAliquota - Dados da seleção de alíquota
 * @returns {IConsultarDetalhesDaAliquotaFactoryRetorno} Objeto contendo os dados de cálculo, detalhamento e resumo
 * da alíquota selecionada (progressiva ou regressiva)
 */
export const mapearDadosPorAliquotaFactory = (
  isAliquotaProgressiva: boolean,
  dadosSelecaoAliquota: IMapearDadosSelecaoAliquotaFactoryRetorno | undefined,
): IConsultarDetalhesDaAliquotaFactoryRetorno => {
  return {
    calculo: getTernaryResult(
      isAliquotaProgressiva,
      dadosSelecaoAliquota?.calculoAliquotaProgressiva,
      dadosSelecaoAliquota?.calculoAliquotaRegressiva,
    ),
    detalhado: getTernaryResult(
      isAliquotaProgressiva,
      dadosSelecaoAliquota?.detalhamentoAliquotaProgressiva,
      dadosSelecaoAliquota?.detalhamentoAliquotaRegressiva,
    ),
    resumo: getTernaryResult(
      isAliquotaProgressiva,
      dadosSelecaoAliquota?.resumoAliquotaProgressiva,
      dadosSelecaoAliquota?.resumoAliquotaRegressiva,
    ),
  };
};
