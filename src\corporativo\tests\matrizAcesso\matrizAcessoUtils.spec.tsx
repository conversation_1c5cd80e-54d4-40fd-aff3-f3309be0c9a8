import {
  verificarSeMatrizAcessoPossuiAlgumaPermissao,
  verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias,
} from '@src/corporativo/utils/matrizAcesso';

describe('verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias', () => {
  it('Deve retornar verdadeiro ao possuir todas as permissões necessárias', () => {
    const resultado = verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      ['permissao1', 'permissao2'],
      ['permissao1', 'permissao2'],
    );
    expect(resultado).toBe(true);
  });

  it('Deve retornar falso ao faltar alguma permissão necessária', () => {
    const resultado = verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      ['permissao1'],
      ['permissao1', 'permissao2'],
    );
    expect(resultado).toBe(false);
  });
});

describe('verificarSeMatrizAcessoPossuiAlgumaPermissao', () => {
  it('Deve retornar verdadeiro ao possuir pelo menos uma permissão', () => {
    const resultado = verificarSeMatrizAcessoPossuiAlgumaPermissao(
      ['permissao1'],
      ['permissao1', 'permissao2'],
    );
    expect(resultado).toBe(true);
  });

  it('Deve retornar falso se não possuir nenhuma permissão necessária', () => {
    const resultado = verificarSeMatrizAcessoPossuiAlgumaPermissao(
      ['permissao1'],
      ['permissao2', 'permissao3'],
    );
    expect(resultado).toBe(false);
  });
});
