import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Formata um número de conta bancária com sua operação, preenchendo com zeros à esquerda quando necessário
 *
 * @param {string} operacao - Código da operação bancária da Caixa
 * @param {string} numeroConta - Número da conta bancária sem formatação
 * @param {number} tamanhoPadrao - Tamanho padrão que o número da conta deve ter após formatação
 * @returns {string} Conta bancária formatada com a operação concatenada
 */
const formatarContaComOperacao = (
  operacao: string,
  numeroConta: string,
  tamanhoPadrao: number,
): string => {
  const numeroContaFormatado: string = Resgate.getTernaryResult(
    numeroConta.trim().length < tamanhoPadrao,
    numeroConta.trim().padStart(tamanhoPadrao, '0'),
    numeroConta.trim(),
  );

  return operacao.trim() + numeroContaFormatado;
};

/**
 * Formata o número da conta bancária de acordo com as regras específicas do Banco Caixa
 *
 * Esta função verifica se o banco é a Caixa Econômica Federal e, em caso positivo,
 * aplica a formatação específica exigida pelo banco, incluindo a concatenação
 * da operação com o número da conta, com tratamento diferente para contas NSGD e SIDEC.
 *
 * @param {Object} params - Parâmetros para formatação da conta
 * @param {string} params.numeroConta - Número da conta bancária
 * @param {string} params.operacao - Código da operação bancária da Caixa
 * @param {string} params.agencia - Número da agência bancária
 * @param {string} params.codigoBanco - Código do banco
 * @returns {string} Número da conta formatado de acordo com as regras do banco
 */
export const formatarContaSeBancoCaixa = ({
  numeroConta,
  operacao,
  agencia,
  codigoBanco,
}: Resgate.IFormatarContaSeBancoCaixa): string => {
  if (codigoBanco !== Resgate.CODIGO_BANCO_CAIXA) return numeroConta.trim();

  const isParametrosValidos: boolean = Resgate.checkIfAllItemsAreTrue([
    !!operacao?.trim().length,
    !!agencia?.trim().length,
    !!numeroConta?.trim().length,
  ]);

  if (!isParametrosValidos) return '';

  const tamanhoOperacao: number = operacao.trim().length;

  const isOperacaoNSGD: boolean =
    tamanhoOperacao ===
    Number(Resgate.DADOS_BANCO_CONTA.QTD_CHAR_INPUT.OPERACAO_NSGD);

  if (isOperacaoNSGD) {
    return formatarContaComOperacao(
      operacao,
      numeroConta,
      Number(Resgate.DADOS_BANCO_CONTA.QTD_CHAR_INPUT.NSGD),
    );
  }

  return formatarContaComOperacao(
    operacao,
    numeroConta,
    Number(Resgate.DADOS_BANCO_CONTA.QTD_CHAR_INPUT.SIDEC),
  );
};
