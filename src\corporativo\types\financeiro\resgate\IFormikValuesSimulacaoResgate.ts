import { IConsultarContribuicaoRegularFundos } from '@src/corporativo/types/financeiro/resgate/IConsultarContribuicaoRegular';
import { IListarFundosParaResgateFundosDisponiveis } from '@src/corporativo/types/financeiro/resgate/IListarFundosParaResgate';
import {
  IContaExistente,
  INovaConta,
} from '@src/corporativo/types/financeiro/resgate/IFormContaBancaria';

export interface IFormikValuesSimulacaoResgate {
  tipoResgate: string;
  fundosParaResgate: IListarFundosParaResgateFundosDisponiveis[];
  aliquotaParaResgateSelecionada: string;
  motivoResgate: string;
  contaExistente: IContaExistente;
  isNovaConta: boolean;
  novaConta: INovaConta;
  fundosParaContribuicao: IConsultarContribuicaoRegularFundos[];
}
