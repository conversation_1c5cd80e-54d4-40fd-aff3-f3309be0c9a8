import styled from 'styled-components';

export const FooterConsulta = styled.footer`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4rem;
  padding: 1rem;
  justify-content: space-between;
  align-items: center;
  margin-top: 3rem;
  > span {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  > div {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    gap: 1.5rem;

    span {
      display: flex;
      flex: 1;
      gap: 1.5rem;
      justify-content: end;
    }
  }
`;
