import { useContext } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IObterComprovanteResgateResponse,
  IUseObterComprovanteResgate,
  IObterComprovanteResgatePayload,
} from '@src/shared/types/ObterComprovanteResgate/IObterComprovanteResgate';

export const useObterComprovante = (): IUseObterComprovanteResgate => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  const payload = {
    cpfCnpj,
    numeroCertificado: certificadoAtivo?.certificadoNumero,
  };

  const {
    loading: isLoadingComprovanteResgate,
    response: comprovanteResgate,
    invocarApiGatewayCvpComToken: obterComprovanteResgate,
  } = useApiGatewayCvpInvoker<
    Partial<IObterComprovanteResgatePayload>,
    IObterComprovanteResgateResponse
  >(PECOS.ObterComprovanteResgate, {
    data: payload,
  });

  return {
    isLoadingComprovanteResgate,
    comprovanteResgate: tryGetValueOrDefault(
      [comprovanteResgate?.entidade],
      {} as IObterComprovanteResgateResponse,
    ),
    obterComprovanteResgate,
  };
};
