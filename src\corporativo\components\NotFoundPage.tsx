import { Grid, GridItem, Text, Button } from '@cvp/design-system-caixa';

import { Link } from 'react-router-dom';

const LinkToHome = () => <Link to="/" />;
const NotFoundPage: React.FC = () => {
  return (
    <Grid alignitem="center" justify="space-between">
      <GridItem xs="1">
        <Text
          display="block"
          fontColor="content-danger-02"
          lineheight="distant"
          textAlign="left"
          variant="text-huge-400"
        >
          404 - Página Não Encontrada
        </Text>
        <Text
          display="block"
          fontColor="brand-primary-09"
          lineheight="distant"
          textAlign="center"
          variant="heading-tiny-700"
        >
          <PERSON><PERSON>l<PERSON>, a página que você está procurando não foi encontrada.
        </Text>
        <Button onClick={LinkToHome} variant="secondary">
          Voltar
        </Button>
      </GridItem>
    </Grid>
  );
};

export default NotFoundPage;
