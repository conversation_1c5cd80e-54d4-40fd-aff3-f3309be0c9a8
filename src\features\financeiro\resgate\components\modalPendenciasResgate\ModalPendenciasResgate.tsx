import * as Resgate from '@src/features/financeiro/resgate/exports';

export const ModalPendenciasResgate = ({
  isOpenModalPendencia,
  controlarModalPendencia,
}: Resgate.IModalPendenciasResgateProps): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();
  return (
    <Resgate.Dialog
      open={isOpenModalPendencia}
      onOpenChange={controlarModalPendencia}
    >
      <Resgate.DialogContent $initialHeight>
        <Resgate.Dialog.Header variant="highlight">
          <Resgate.Text variant="heading-small-600">
            Pendências no resgate
          </Resgate.Text>
        </Resgate.Dialog.Header>
        <Resgate.Dialog.Body>
          <Resgate.Text variant="text-standard-400">
            Não foi possível concluir a solicitação de resgate.
          </Resgate.Text>

          <Resgate.Text variant="text-standard-400">
            {
              resgateFeatureData.dadosRetornoConfirmacaoResgate
                ?.motivoPendenciaResgate
            }
          </Resgate.Text>
        </Resgate.Dialog.Body>
        <Resgate.Dialog.Footer>
          <Resgate.Button
            onClick={() => controlarModalPendencia()}
            size="standard"
            variant="secondary"
          >
            Certo entendi
          </Resgate.Button>
        </Resgate.Dialog.Footer>
      </Resgate.DialogContent>
    </Resgate.Dialog>
  );
};
