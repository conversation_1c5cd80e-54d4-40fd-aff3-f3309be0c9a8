import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IUseFetchImprimirDadosExtratoCotasPrevidenciaPayload {
  cpfCnpj: string;
  dataInicial: string;
  dataFinal: string;
  numeroCertificado: string;
}

export interface IUseFetchImprimirDadosExtratoCotasPrevidenciaReturn {
  isLoadingImprimirDadosExtratoCotasPrevidencia: boolean;
  fetchImprimirDadosExtratoCotasPrevidencia: (
    payload?: IUseFetchImprimirDadosExtratoCotasPrevidenciaPayload,
  ) => Promise<IHandleReponseResult<{ blob: Blob }>>;
}
