import {
  Grid,
  Button,
  IconCancelOutlinedSharp,
} from '@cvp/design-system-caixa';
import { useContext } from 'react';
import { ModalImprimirDocumentosContext } from '@src/corporativo/context/ModalImprimirDocumentosContext';
import {
  DocumentIcon,
  IconPDF,
  MatrizAcessoContext,
} from '@src/features/layoutPrevidencia/exports';
import { EModalImprDocActionType } from '@src/features/layoutPrevidencia/types/modalImprDocTypes';
import {
  retornarTodasPermissoesDaCategoria,
  verificarSeMatrizAcessoPossuiAlgumaPermissao,
  verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias,
} from '@src/corporativo/utils/matrizAcesso';
import {
  BOTOES_APOLICES_PERMISSOES,
  IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES,
} from '@src/corporativo/constants/MatrizAcessoComponentesPermissoes';

const BotoesApolice: React.FC = () => {
  const { modalImprDocDispatch } = useContext(ModalImprimirDocumentosContext);
  const { permissoesMatrizAcesso } = useContext(MatrizAcessoContext);

  const botaoImprimirDocumentosEstaDesabilitado =
    !verificarSeMatrizAcessoPossuiAlgumaPermissao(
      permissoesMatrizAcesso,
      retornarTodasPermissoesDaCategoria(IMPRIMIR_DOCUMENTOS_OPCOES_PERMISSOES),
    );

  const botaoCancelarApoliceEstaDesabilitado =
    !verificarSeMatrizAcessoPossuiTodasPermissoesNecessarias(
      permissoesMatrizAcesso,
      BOTOES_APOLICES_PERMISSOES.botao_cancelar_certificado.permissions,
    );

  return (
    <Grid margin="0">
      <div style={{ width: 236 + 5 + 5 + 1 }}>
        <Button
          variant="secondary-text"
          leftIcon={<IconPDF size="big" />}
          style={{
            background: 'none',
            margin: 0,
            padding: '2px',
          }}
          onClick={() =>
            modalImprDocDispatch({
              type: EModalImprDocActionType.ABRIR_MODAL,
            })
          }
          disabled={botaoImprimirDocumentosEstaDesabilitado}
        >
          Imprimir documentos
        </Button>
      </div>
      <div style={{ width: 208 + 5 + 5 + 1 }}>
        <Button
          variant="secondary-text"
          leftIcon={<IconCancelOutlinedSharp size="small" />}
          style={{ background: 'none', margin: 0, padding: '2px' }}
          disabled={
            AppConfig?.MFE_ENV !== 'development'
              ? true
              : botaoCancelarApoliceEstaDesabilitado
          }
        >
          Cancelar apólice
        </Button>
      </div>
      <Button
        variant="secondary-text"
        color="#9EB2B8"
        leftIcon={<DocumentIcon size="big" />}
        style={{
          background: 'none',
          margin: 0,
          padding: '2px',
          color: '#9EB2B8',
        }}
      >
        Solicitar ASC
      </Button>
    </Grid>
  );
};

export default BotoesApolice;
