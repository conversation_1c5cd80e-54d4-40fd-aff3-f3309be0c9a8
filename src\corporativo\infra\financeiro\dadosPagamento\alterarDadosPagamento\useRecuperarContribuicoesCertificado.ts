import { useContext } from 'react';
import { DadosTipoPagamentoPadrao } from '@src/features/financeiro/types/enum';
import * as REQUEST_TYPES from '@src/features/financeiro/dadosPagamento/types/AlteracaoFormaDadosPagamentoRequest';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { getSessionItem } from '@cvp/utils';
import { IResponseRecuperarContribuicoesCertificado } from '@src/features/financeiro/types/RecuperarContribuicoesCertificado';

export const useRecuperarContribuicoesCertificado = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj') ?? '';

  const {
    response: dadosRecuperacaoContribuicoesCertificado,
    loading: loadingDadosRecuperacaoContribuicoesCertificado,
    invocarApiGatewayCvpComToken: obterRecuperacaoContribuicoesCertificado,
  } = useApiGatewayCvpInvoker<
    REQUEST_TYPES.IRequestRecuperarContribuicoesCertificado,
    IResponseRecuperarContribuicoesCertificado
  >(PECOS.RecuperarContribuicoesCertificado, {
    autoFetch: true,
    data: {
      cpfCnpj: cpfCnpjSession,
      numeroCertificado: certificadoAtivo?.certificadoNumero,
      categoria: DadosTipoPagamentoPadrao.TIPO_RECUPERACAO_CONTRIBUICOES_RE,
    },
  });
  return {
    dadosRecuperacaoContribuicoesCertificado:
      dadosRecuperacaoContribuicoesCertificado?.entidade,
    loadingDadosRecuperacaoContribuicoesCertificado,
    obterRecuperacaoContribuicoesCertificado,
  };
};
