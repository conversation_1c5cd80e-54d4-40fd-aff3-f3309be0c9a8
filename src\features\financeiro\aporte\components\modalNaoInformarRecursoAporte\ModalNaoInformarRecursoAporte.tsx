import {
  DialogInfo,
  Text,
  Button,
  Grid,
  ORIGEM_RECURSOS_DECLARACAO,
  BOTOES,
  ReactElement,
} from '@src/features/financeiro/aporte/exports';

interface IModalNaoInformarRecursoAporte {
  open: boolean;
  toggle: (open: boolean) => void;
  children: ReactElement;
}

const ModalNaoInformarRecursoAporte: React.FC<
  IModalNaoInformarRecursoAporte
> = ({ open, toggle, children }) => {
  return (
    <DialogInfo
      open={open}
      onOpenChange={toggle}
      title={
        <Text
          display="block"
          lineheight="distant"
          textAlign="left"
          variant="text-big-400"
        >
          {ORIGEM_RECURSOS_DECLARACAO.declaracaoTitulo}
        </Text>
      }
      body={<Grid>{ORIGEM_RECURSOS_DECLARACAO.declaracaoNaoInforma}</Grid>}
      footer={
        <>
          <DialogInfo.Cancel asChild>
            <Button size="standard" variant="secondary">
              {BOTOES.cancelar}
            </Button>
          </DialogInfo.Cancel>
          <DialogInfo.Cancel asChild>
            <Button size="standard" variant="primary">
              {BOTOES.confirmar}
            </Button>
          </DialogInfo.Cancel>
        </>
      }
      headerSize="16px 24px"
      justifyContentFooter="flex-end"
      maxHeight="85vh"
      maxWidth="600px"
      notCloseOnOutsideClick
      trigger={children}
    />
  );
};

export default ModalNaoInformarRecursoAporte;
