import {
  IListarFundosParaResgateAliquota,
  IListarFundosParaResgateAliquotaOpcoes,
} from '@src/features/financeiro/resgate/exports';

export interface ICabecalhoEscolhaTabelaAliquotaProps {
  tipoAliquota: IListarFundosParaResgateAliquotaOpcoes;
  aliquota: IListarFundosParaResgateAliquota;
  selecionarOpcaoAliquota: (opcaoSelecionada: string) => void;
  validarAliquotaDesabilitada: (codigoAliquota: string) => boolean;
}
