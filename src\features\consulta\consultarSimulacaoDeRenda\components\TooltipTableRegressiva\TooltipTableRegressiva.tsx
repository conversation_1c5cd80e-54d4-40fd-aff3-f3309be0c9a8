import {
  ALIQUOTA_REGRESSIVA_TOOLTIP,
  COLUMNS_TABLE_TOOLTIP,
  DATA_TABLE_TOOLTIP,
  IconInfoOutlined,
  Table,
  Text,
  ToolTip,
} from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const TooltipTableRegressiva: React.FC = () => {
  return (
    <div>
      {ALIQUOTA_REGRESSIVA_TOOLTIP.title}
      <ToolTip
        width="400px"
        text={
          (
            <>
              <Text variant="text-small-600">
                {ALIQUOTA_REGRESSIVA_TOOLTIP.title}
              </Text>
              <Text variant="text-small-600">
                {ALIQUOTA_REGRESSIVA_TOOLTIP.text}
              </Text>
              <Table
                themeTable="cvp-01"
                columns={COLUMNS_TABLE_TOOLTIP}
                data={DATA_TABLE_TOOLTIP}
              />
            </>
          ) as unknown as string
        }
      >
        <IconInfoOutlined size="small" />
      </ToolTip>
    </div>
  );
};
