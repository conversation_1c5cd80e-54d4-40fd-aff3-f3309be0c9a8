import styled from 'styled-components';
import {
  DateField,
  Table,
} from '@src/features/consulta/historicoSolicitacoes/exports';

export const ItemFiltroContainer = styled.div`
  label {
    font-size: 0.875rem !important;
    font-weight: bold !important;
  }
`;

export const ItemFiltroSubmitContainer = styled.div`
  display: grid;
  align-items: end;
`;

export const InputDate = styled(DateField)`
  min-width: 200px;
`;

export const Container = styled.section`
  border: 1px solid #ebf1f2;
  border-radius: 10px;
  padding: 5px;
  margin-top: 0.5rem;
`;

export const Header = styled.div`
  padding: 0.5rem;
`;

export const Content = styled.div`
  padding: 1rem;
  padding-top: 0;
`;

export const DataTable = styled(Table)`
  margin-bottom: 1rem;
  .rdt_TableHead {
    border: none;

    .rdt_TableHeadRow {
      background: #edf4f6;
      border: none;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;

      .rdt_TableCol_Sortable {
        font-weight: 600;
      }
    }
  }

  .rdt_TableBody {
    .rdt_TableRow:nth-child(2n) {
      background-color: #edf4f6;
    }
  }
`;

export const ButtonComprovante = styled.span`
  color: #005ca9;
  cursor: pointer;
`;
