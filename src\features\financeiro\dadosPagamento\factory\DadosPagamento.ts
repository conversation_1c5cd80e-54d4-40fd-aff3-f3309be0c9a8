import {
  IResponseDadosCertificadosPorCpf,
  DIA_MAXIMO_VENCIMENTO,
  DIA_MINIMO_VENCIMENTO,
  checkIfSomeItemsAreTrue,
  MENSAGENS_TRADUZIDAS,
} from '@src/features/financeiro/dadosPagamento/exports';

export const traduzirMensagem = (mensagem: string): string => {
  return (
    MENSAGENS_TRADUZIDAS[mensagem as keyof typeof MENSAGENS_TRADUZIDAS] ??
    mensagem
  );
};

export const gerarOpcoesDiasVencimento = () => {
  const options = [];
  for (
    let dia = DIA_MINIMO_VENCIMENTO;
    dia <= DIA_MAXIMO_VENCIMENTO;
    dia += 1
  ) {
    options.push({
      text: `${dia}`,
      value: `${dia}`,
    });
  }
  return options;
};

export const mapearContasBancarias = (
  dados: IResponseDadosCertificadosPorCpf[],
) => {
  if (checkIfSomeItemsAreTrue([!dados, !Array.isArray(dados)])) {
    return [];
  }

  return dados
    .map(conta => {
      const {
        numeroAgencia = '',
        numeroConta = '',
        digitoConta = '',
        codigoCanalRecuperacao,
      } = conta;
      if (
        checkIfSomeItemsAreTrue([!numeroAgencia, !numeroConta, !digitoConta])
      ) {
        return null;
      }
      return {
        text: `${numeroAgencia} - ${numeroConta} - ${digitoConta}`,
        value: codigoCanalRecuperacao,
      };
    })
    .filter(option => option !== null);
};

export const formatarContaConcatenada = (conta: {
  numeroAgencia?: string;
  numeroConta?: string;
  digitoConta?: string;
}): string => {
  if (!conta || typeof conta !== 'object') {
    return 'Dados inválidos';
  }

  const {
    numeroAgencia = 'N/A',
    numeroConta = 'N/A',
    digitoConta = 'N/A',
  } = conta;

  if (checkIfSomeItemsAreTrue([!numeroAgencia, !numeroConta, !digitoConta]))
    return '';

  return `${numeroAgencia} - ${numeroConta} - ${digitoConta}`;
};
