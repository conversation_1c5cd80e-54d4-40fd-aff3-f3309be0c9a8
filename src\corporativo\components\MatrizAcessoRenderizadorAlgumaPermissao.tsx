import { IMatrizAcessoRenderizadorAlgumaPermissao } from '@src/corporativo/types/matrizAcesso/IMatrizAcessoRenderizadorAlgumaPermissao';
import { verificarSeMatrizAcessoPossuiAlgumaPermissao } from '@src/corporativo/utils/matrizAcesso';

const MatrizAcessoRenderizadorAlgumaPermissao: React.FC<
  IMatrizAcessoRenderizadorAlgumaPermissao
> = ({ children, permissoesComponente, matrizAcesso }) => {
  const componentePossuiPermissao =
    verificarSeMatrizAcessoPossuiAlgumaPermissao(
      matrizAcesso,
      permissoesComponente,
    );

  if (!componentePossuiPermissao) return null;

  return children;
};

export default MatrizAcessoRenderizadorAlgumaPermissao;
