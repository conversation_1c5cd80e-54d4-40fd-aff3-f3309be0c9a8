import { ChangeEvent } from 'react';

import { TTipoEmail } from '@src/corporativo/infra/email/tipoEmail';

interface IModalEnvioObjetoEmail {
  parametrosEnvio: unknown;
  tipoEmail: TTipoEmail;
}

export interface IModalEnvioEmailProps {
  objetoEmail: IModalEnvioObjetoEmail;
  emailDefault?: string;
  label?: string;
  onEmailSent?: (email: string) => void;
  buttonText?: string;
}

export interface IUseModalEnvioEmail {
  toggleModal: () => void;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  handleSend: () => Promise<void>;
  isOpen: boolean;
  email: string | undefined;
  mensagens: Record<string, string>;
  isLoading: boolean;
  isDisabled: boolean;
}
