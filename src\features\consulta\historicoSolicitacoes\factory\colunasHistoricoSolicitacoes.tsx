import {
  But<PERSON>,
  IconPDF,
  Lo<PERSON><PERSON><PERSON>ner,
  STATUS_REQUISICAO_COMPROVANTE,
  Styles,
  TColunasHistoricoSolicitacoes,
  TEXTOS,
  formatarValorPadraoBrasileiro,
} from '@src/features/consulta/historicoSolicitacoes/exports';

export const colunasHistoricoSolicitacoes: TColunasHistoricoSolicitacoes = (
  obterComprovante,
  assinarMovimantacao,
  carregandoMovimentacoesIds,
) => {
  return [
    {
      name: 'Realizado',
      selector: row => row.dataRequisicao,
      cell: row => new Date(row.dataRequisicao).toLocaleDateString(),
      sortable: true,
      width: '150px',
    },
    {
      name: 'Serviço',
      selector: row => row.codigoRequisicao,
      compact: true,
      sortable: true,
      wrap: true,
      width: '300px',
    },
    {
      name: 'Canal',
      selector: row => row.canalRequisicao,
      sortable: true,
      compact: true,
    },
    {
      name: 'Status',
      selector: row => row.statusRequisicao,
      sortable: true,
      compact: true,
      center: true,
      cell: row => (
        <span style={{ textTransform: 'capitalize' }}>
          {row.statusRequisicao.toLocaleLowerCase()}
        </span>
      ),
    },
    {
      name: 'Valor',
      selector: row => row.valorRequisicao,
      sortable: true,
      compact: true,
      cell: row => formatarValorPadraoBrasileiro(row.valorRequisicao),
    },
    {
      name: 'Comprovante',
      compact: true,
      center: true,
      cell(row) {
        if (carregandoMovimentacoesIds?.includes(row.idRequisicao))
          return <LoadingSpinner size="small" />;

        if (row.statusRequisicao === STATUS_REQUISICAO_COMPROVANTE.CONCLUIDA)
          return (
            <Styles.ButtonComprovante onClick={() => obterComprovante(row)}>
              <IconPDF size="medium" />
            </Styles.ButtonComprovante>
          );

        if (
          row.statusRequisicao ===
          STATUS_REQUISICAO_COMPROVANTE.AGUARDANDO_ASSINATURA
        ) {
          return (
            <Button
              variant="secondary"
              size="small"
              onClick={() => assinarMovimantacao(row)}
            >
              {TEXTOS.ASSINAR}
            </Button>
          );
        }

        return '';
      },
    },
  ];
};
