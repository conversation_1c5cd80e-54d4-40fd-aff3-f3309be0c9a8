import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IUseFetchObterBeneficiariosPayload {
  cpf: string;
  ano: string;
}

export interface IPECOObterBeneficiariosEntity {
  nomePessoa: string;
  cpf: string;
}

export interface IUseFetchObterBeneficiariosReturn {
  isLoadingObterBeneficiarios: boolean;
  fetchObterBeneficiarios: (
    dynamicPayload?: IUseFetchObterBeneficiariosPayload,
  ) => Promise<
    IHandleReponseResult<IPECOObterBeneficiariosEntity[]> | undefined
  >;
}
