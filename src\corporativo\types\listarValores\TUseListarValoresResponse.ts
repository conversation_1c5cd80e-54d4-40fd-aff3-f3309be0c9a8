export type TUseListarValoresResponse = {
  fundosExistentes?: TFundoContribuicao;
  qtdeMaxFundos: string;
  qtdeFundosDisponiveis: string;
  possuiMensagemErro: string;
  possuiMensagemSucesso: string;
  vlMinContribuicao: string;
  fundosNovos?: TFundoContribuicao;
};

export type TUseListarValoresResponseReturn = {
  return: TUseListarValoresResponse;
  msgErroExcessao: string;
};

export type TFundoContribuicao = {
  fundos: TAlterarValorContribuicaoFundo[];
  qtdeFundosDisponiveis: string;
  qtdeMaxFundos: string;
};

export type TAlterarValorContribuicaoFundo = {
  saldoAtual: string;
  aviso: string;
  codReserva: string;
  codFundo: string;
  nomeFundo: string;
  rentabilidadeDozeMeses: string;
  vlContribuicaoAtual: string;
  vlContribuicaoTotalAtual: string;
  perfilFundo: string;
  vlMinEntrada: string;
  vlMinEntradaFamilia: string;
  selecionado: boolean;
};
