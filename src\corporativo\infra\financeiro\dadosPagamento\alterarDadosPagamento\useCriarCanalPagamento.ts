import * as REQUEST_TYPES from '@src/features/financeiro/dadosPagamento/types/AlteracaoFormaDadosPagamentoRequest';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { checkIfAllItemsAreTrue, getSessionItem } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

export const useCriarCanalPagamento = ({
  idPessoa,
  metodoPagamento,
  dadosPagamento,
}: Partial<REQUEST_TYPES.IRequestCriarCanalPagamento>) => {
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj') ?? '';

  const {
    response: dadosCanalPagamento,
    loading: loadingDadosCanalPagamento,
    invocarApiGatewayCvpComToken: criarCanalPagamento,
  } = useApiGatewayCvpInvoker<
    Partial<REQUEST_TYPES.IRequestCriarCanalPagamento>,
    REQUEST_TYPES.IResponseCriarCanalPagamento
  >(PECOS.CriarCanalPagamento, {
    data: {
      cpfCnpj: cpfCnpjSession,
      idPessoa,
      metodoPagamento,
      dadosPagamento,
    },
  });

  const isSuccessCriarCanalPagamento = checkIfAllItemsAreTrue([
    !!dadosCanalPagamento?.sucessoBFF,
    !!dadosCanalPagamento?.sucessoGI,
  ]);

  return {
    dadosCanalPagamento: dadosCanalPagamento?.entidade,
    loadingDadosCanalPagamento,
    criarCanalPagamento,
    isSuccessCriarCanalPagamento,
    canalPagamentoId: dadosCanalPagamento?.entidade?.canalId,
  };
};
