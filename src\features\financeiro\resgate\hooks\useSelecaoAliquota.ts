import * as Resgate from '@src/features/financeiro/resgate/exports';

export const useSelecaoAliquota = (
  simulacao: Resgate.IUseSimulacaoResgate,
): Resgate.IUseSelecaoAliquotaRetorno => {
  const theme = Resgate.useTheme();
  const { certificadoAtivo } = Resgate.useContext(Resgate.PrevidenciaContext);
  const { resgateFeatureData, handleResgateFeatureData } =
    Resgate.useResgateContext();
  const formik =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();
  const { listaFundosParaResgate } = Resgate.useResgateFormSetup();
  const { aliquotaAtual, indicadorPermiteEditarAliquota } =
    Resgate.tryGetValueOrDefault(
      [listaFundosParaResgate?.aliquota],
      {} as Resgate.IListarFundosParaResgateAliquota,
    );

  const { mensagens, configurarMensagem } = Resgate.useMensagensTemporarias();
  const { isLoadingCalculoResgate, calcularResgate } =
    Resgate.useCalcularResgate();
  const { isLoadingConsultaResumoAliquota, consultarResumoAliquota } =
    Resgate.useConsultarResumoAliquota();
  const { isLoadingConsultaDetalheCalculo, consultarDetalheCalculo } =
    Resgate.useConsultarDetalheCalculo();

  const [isOpen, setIsOpen] = Resgate.useState<boolean>(false);
  const [isOpenModalDetalhamento, setIsOpenModalDetalhamento] =
    Resgate.useState<boolean>(false);

  const [dadosResumoSelecionado, setDadosResumoSelecionado] = Resgate.useState<
    Resgate.IMontarResumoAliquotaFactoryRetorno[]
  >([]);
  const [opcaoTemporaria, setOpcaoTemporaria] = Resgate.useState<string>('');
  const [opcaoRegimeTributario, setOpcaoRegimeTributario] =
    Resgate.useState<string>('');

  const objetoEmail: Resgate.IObjetoEmailSimulacaoResgate =
    Resgate.criarObjetoEmailSimulacaoResgate({
      certificadoAtivo,
      resgateFeatureData,
    });

  const isLoadingConfirmacaoSimulacao: boolean =
    Resgate.checkIfSomeItemsAreTrue([
      isLoadingCalculoResgate,
      isLoadingConsultaResumoAliquota,
      isLoadingConsultaDetalheCalculo,
    ]);

  const resumoAliquotaProgressiva: Resgate.IMontarResumoAliquotaFactoryRetorno[] =
    Resgate.montarResumoAliquotaFactory({
      tipoAliquota: Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
      dadosSelecaoAliquota: resgateFeatureData?.dadosSelecaoAliquota,
      saldo: listaFundosParaResgate?.saldo,
    });

  const resumoAliquotaRegressiva: Resgate.IMontarResumoAliquotaFactoryRetorno[] =
    Resgate.montarResumoAliquotaFactory({
      tipoAliquota: Resgate.ALIQUOTA.TIPO_REGIME_REGRESSIVO,
      dadosSelecaoAliquota: resgateFeatureData?.dadosSelecaoAliquota,
      saldo: listaFundosParaResgate?.saldo,
    });

  const detalhadoAliquotaProgressiva: Resgate.IMontarDetalhadoAliquotaFactoryRetorno[] =
    Resgate.montarDetalhadoAliquotaFactory({
      tipoAliquota: Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
      dadosSelecaoAliquota: resgateFeatureData?.dadosSelecaoAliquota,
      saldo: listaFundosParaResgate?.saldo,
    });

  const detalhadoAliquotaRegressiva: Resgate.IMontarDetalhadoAliquotaFactoryRetorno[] =
    Resgate.montarDetalhadoAliquotaFactory({
      tipoAliquota: Resgate.ALIQUOTA.TIPO_REGIME_REGRESSIVO,
      dadosSelecaoAliquota: resgateFeatureData?.dadosSelecaoAliquota,
      saldo: listaFundosParaResgate?.saldo,
    });

  const dadosTabelaValoresDetalhadosProgressivo: Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno[] =
    Resgate.montarDadosTabelaDetalheCalculoFactory({
      tipoAliquota: Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
      dadosSelecaoAliquota: resgateFeatureData?.dadosSelecaoAliquota,
    });

  const dadosTabelaValoresDetalhadosRegressivo: Resgate.IMontarDadosTabelaDetalheCalculoFactoryRetorno[] =
    Resgate.montarDadosTabelaDetalheCalculoFactory({
      tipoAliquota: Resgate.ALIQUOTA.TIPO_REGIME_REGRESSIVO,
      dadosSelecaoAliquota: resgateFeatureData?.dadosSelecaoAliquota,
    });

  const dadosResumoAliquotaSelecionada: Resgate.IObterResumoAliquotaSelecionadaRetorno =
    Resgate.obterResumoAliquotaSelecionada({
      indicadorPermiteEditarAliquota,
      aliquotaAtual: listaFundosParaResgate?.aliquota?.aliquotaAtual,
      resumoAliquotaProgressiva,
      resumoAliquotaRegressiva,
      dadosResumoSelecionado,
      opcaoRegimeTributario,
    });

  const confirmarSimulacao = async (): Promise<void> => {
    const parametrosPadraoCalculoAliquota = {
      formik,
      calcularResgate,
      consultarResumoAliquota,
      consultarDetalheCalculo,
      resultadoCalculoResgateRestante:
        simulacao.resultadoCalculoResgateRestante,
    };

    const dadosSelecaoAliquota =
      await Resgate.mapearDadosSelecaoAliquotaFactory(
        listaFundosParaResgate.aliquota,
        parametrosPadraoCalculoAliquota,
      );

    const hasDadosSimulacao: boolean = Object.values(dadosSelecaoAliquota).some(
      item => !Resgate.isEmptyObject(item),
    );

    if (hasDadosSimulacao) {
      handleResgateFeatureData({
        dadosSelecaoAliquota,
        hasDadosSimulacao,
      });
    } else {
      configurarMensagem(
        Resgate.MSG_TEMPORARIA.SIMULACAO_RESGATE.CHAVE,
        Resgate.MSG_TEMPORARIA.SIMULACAO_RESGATE.MENSAGEM,
      );
    }
  };

  const reiniciarSimulacao = (): void => {
    handleResgateFeatureData(Resgate.defaultContextResgateData);
    formik.resetForm();
    setOpcaoRegimeTributario('');
  };

  const toggleModalConfirmacaoAliquota = (): void => {
    setIsOpen(prevState => !prevState);
  };

  const toggleModalDetalhamento = (): void => {
    setIsOpenModalDetalhamento(prevState => !prevState);
  };

  const selecionarOpcaoAliquota = (opcaoSelecionada: string): void => {
    setIsOpen(true);
    setOpcaoTemporaria(opcaoSelecionada);
  };

  const cancelarEscolhaAliquota = (): void => {
    setOpcaoTemporaria('');
    toggleModalConfirmacaoAliquota();
  };

  const setDadosAliquotaSelecionada = (opcaoSelecionada: string): void => {
    formik.setValues({
      ...formik.values,
      aliquotaParaResgateSelecionada: opcaoSelecionada,
    });

    setDadosResumoSelecionado(
      Resgate.getTernaryResult(
        opcaoSelecionada === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
        resumoAliquotaProgressiva,
        resumoAliquotaRegressiva,
      ),
    );
  };

  const confirmarEscolhaAliquota = (): void => {
    setDadosAliquotaSelecionada(opcaoTemporaria);

    setOpcaoRegimeTributario(opcaoTemporaria);

    toggleModalConfirmacaoAliquota();
  };

  const obterDadosPorAliquota = (
    tipoAliquota?: string,
  ): Resgate.IObterDadosPorAliquotaRetorno => {
    const resumo = Resgate.getTernaryResult(
      tipoAliquota === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
      resumoAliquotaProgressiva,
      resumoAliquotaRegressiva,
    );

    const resumoEstendido = Resgate.getTernaryResult(
      opcaoRegimeTributario === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
      detalhadoAliquotaProgressiva,
      detalhadoAliquotaRegressiva,
    );

    const tabelaHistoricoContribuicao = Resgate.getTernaryResult(
      opcaoRegimeTributario === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
      dadosTabelaValoresDetalhadosProgressivo,
      dadosTabelaValoresDetalhadosRegressivo,
    );

    const isExibeTabelaAliquota = Resgate.getTernaryResult(
      indicadorPermiteEditarAliquota,
      true,
      tipoAliquota === listaFundosParaResgate?.aliquota?.aliquotaAtual,
    );

    return {
      resumo,
      resumoEstendido,
      tabelaHistoricoContribuicao,
      isExibeTabelaAliquota,
    };
  };

  const validarAliquotaDesabilitada = (codigoAliquota: string): boolean => {
    return Resgate.checkIfAllItemsAreTrue([
      !!opcaoRegimeTributario,
      opcaoRegimeTributario !== codigoAliquota,
    ]);
  };

  const customizarEstiloDataTableDetalhesAliquota = (
    style: Resgate.IConditionalRowStyleDetalhesAliquota,
  ): Resgate.IConditionalRowStyleDetalhesAliquota => ({
    ...style,
    style: {
      ...style.style,
      backgroundColor: theme.color.brand.primary['06'].toString(),
      color: theme.color.content.neutral['01'].toString(),
    },
  });

  const definirValorInicialAliquotaSelecionada = (): void => {
    const shouldAutoSelecionarAliquota: boolean =
      Resgate.checkIfAllItemsAreTrue([
        !indicadorPermiteEditarAliquota,
        !!aliquotaAtual,
        !!resgateFeatureData.hasDadosSimulacao,
      ]);

    if (shouldAutoSelecionarAliquota) {
      setDadosAliquotaSelecionada(aliquotaAtual);
    }
  };

  Resgate.useEffect(definirValorInicialAliquotaSelecionada, [
    indicadorPermiteEditarAliquota,
    aliquotaAtual,
    resgateFeatureData.hasDadosSimulacao,
  ]);

  return {
    isLoadingConfirmacaoSimulacao,
    isOpen,
    isOpenModalDetalhamento,
    mensagens,
    dadosResumoAliquotaSelecionada,
    objetoEmail,
    confirmarSimulacao,
    reiniciarSimulacao,
    toggleModalDetalhamento,
    toggleModalConfirmacaoAliquota,
    cancelarEscolhaAliquota,
    confirmarEscolhaAliquota,
    obterDadosPorAliquota,
    selecionarOpcaoAliquota,
    validarAliquotaDesabilitada,
    customizarEstiloDataTableDetalhesAliquota,
  };
};
