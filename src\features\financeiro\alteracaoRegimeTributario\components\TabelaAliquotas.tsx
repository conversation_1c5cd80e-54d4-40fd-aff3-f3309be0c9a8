import { ToolTip } from '@cvp/design-system-caixa';
import {
  checkIfSomeItemsAreTrue,
  CONSTS,
  getTernaryResult,
  IAliquotas,
  IconArrowDownRound,
  IconExpandLessRound,
  IconExpandMoreRound,
  IconPVExclamationCircle,
  RadioItem,
  RadioLabel,
  S,
  tabelaAliquotasDadosColunas,
  Text,
  useAlteracaoRegimeTributarioContext,
} from '../exports';

interface ITabelaAliquotas {
  aliquota: IAliquotas;
}

const TabelaAliquotas: React.FC<ITabelaAliquotas> = ({ aliquota }) => {
  const { isDisabled, dadosTabelaAliquotas } =
    useAlteracaoRegimeTributarioContext();
  return (
    <>
      <RadioItem
        id={aliquota.id}
        rightLabel={
          <>
            <p>
              <RadioLabel htmlFor={aliquota.id}>
                <Text variant="text-large-400">{aliquota.text}</Text>
              </RadioLabel>
            </p>

            <ToolTip
              text={CONSTS.MENSAGEM_TOOLTIPS[aliquota.id as 'regressiva']}
              maxWidth="344px"
            >
              <IconPVExclamationCircle size="small" />
            </ToolTip>
          </>
        }
        disabled={checkIfSomeItemsAreTrue([
          isDisabled,
          dadosTabelaAliquotas.aliquotasRegressivas.length === 0,
          dadosTabelaAliquotas.aliquotasProgressivas.length === 0,
        ])}
        value={aliquota.id}
        variant="black"
      />
      <S.TableContent
        columns={tabelaAliquotasDadosColunas}
        data={getTernaryResult(
          aliquota.id === CONSTS.ALIQUOTAS.REGRESSIVA.id,
          dadosTabelaAliquotas.aliquotasRegressivas,
          dadosTabelaAliquotas.aliquotasProgressivas,
        )}
        expandableIcon={{
          collapsed: <IconExpandMoreRound color="#005CA9" size="medium" />,
          expanded: <IconExpandLessRound color="#005CA9" size="medium" />,
        }}
        sortIcon={<IconArrowDownRound color="#EBF1F2" size="medium" />}
        themeTable="cvp-05"
        striped
        responsive
        noDataComponent={CONSTS.TITULOS.NO_DATA_REGIME_TRIBUTARIO}
      />
    </>
  );
};

export default TabelaAliquotas;
