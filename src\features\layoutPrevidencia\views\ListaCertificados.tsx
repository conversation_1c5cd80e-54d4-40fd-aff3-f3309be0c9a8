import { LoadingSpinner, MultiAccordion } from '@cvp/design-system-caixa';
import { WrapperNotFound } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault, checkIfAllItemsAreTrue } from '@cvp/utils';

import { useContext } from 'react';
import { TEXTOS_WRAPPER_NOT_FOUND } from '@src/corporativo/constants/wrapperNotFound/TextosWrapperNotFound';
import {
  CertificadoPrevidenciaWrapper,
  For,
  ICertificadoPrevidenciaResponse,
  LayoutConsulta,
  SwitchCase,
  Match,
  LOADING_INSURANCE,
  PrevidenciaContext,
  Grid,
} from '../exports';

interface IListaCertificadosProps {
  loading?: boolean;
  filteredResponse: ICertificadoPrevidenciaResponse[] | undefined;
  setCertificadoAtivo: (
    certificado: ICertificadoPrevidenciaResponse,
  ) => void | undefined;
}

const ListaCertificados: React.FC<IListaCertificadosProps> = ({
  loading = false,
  filteredResponse,
  setCertificadoAtivo,
}) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  return (
    <SwitchCase>
      <Match when={loading}>
        <Grid margin="50px" justify="center">
          <LoadingSpinner size="big">{LOADING_INSURANCE}</LoadingSpinner>{' '}
        </Grid>
      </Match>

      <Match when={filteredResponse?.length}>
        <MultiAccordion.Root
          defaultValue={tryGetValueOrDefault(
            [String(certificadoAtivo?.certificadoNumero)],
            '',
          )}
        >
          <For each={tryGetValueOrDefault([filteredResponse], [])}>
            {certificado => (
              <CertificadoPrevidenciaWrapper
                key={certificado.certificadoNumero}
                onClick={() => setCertificadoAtivo(certificado)}
              >
                <LayoutConsulta certificado={certificado} />
              </CertificadoPrevidenciaWrapper>
            )}
          </For>
        </MultiAccordion.Root>
      </Match>
      <Match
        when={checkIfAllItemsAreTrue([!filteredResponse?.length, !loading])}
      >
        <WrapperNotFound
          content={TEXTOS_WRAPPER_NOT_FOUND.PRODUTO_NAO_LOCALIZADO}
        />
      </Match>
    </SwitchCase>
  );
};

export default ListaCertificados;
