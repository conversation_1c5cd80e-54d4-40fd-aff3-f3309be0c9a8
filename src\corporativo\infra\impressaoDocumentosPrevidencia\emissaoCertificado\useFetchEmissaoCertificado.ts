import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { useContext } from 'react';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { getSessionItem } from '@cvp/utils';
import { IUseFetchEmissaoCertificadoResponse } from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchEmissaoCertificado';
import { TPayloadEmissaoCertificado } from '@src/corporativo/types/impressaoDocumentosPrevidencia/TPayloadEmissaoCertificado';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

const useFetchEmissaoCertificado: () => IUseFetchEmissaoCertificadoResponse =
  () => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const payload: TPayloadEmissaoCertificado = {
      numeroCertificado: certificadoAtivo.certificadoNumero,
      tipoCertificado: 'Individual',
      cpfCnpj: getSessionItem('cpfCnpj') ?? '',
    };

    const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
      TPayloadEmissaoCertificado,
      string
    >(PECOS.ObterCertificado, {
      data: payload,
      autoFetch: false,
    });

    return {
      fetchDataEmissaoCertificado: invocarApiGatewayCvpComToken,
      isLoadingEmissaoCertificado: loading,
    };
  };

export default useFetchEmissaoCertificado;
