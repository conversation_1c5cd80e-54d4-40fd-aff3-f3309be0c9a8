import {
  ContainerTooltipRegressiva,
  TEXTO_TOOLTIP_REGRESSIVA,
} from '@src/features/financeiro/resgate/exports';

export const TooltipAliquotaRegressiva = (): React.ReactElement => {
  return (
    <ContainerTooltipRegressiva>
      <p>{TEXTO_TOOLTIP_REGRESSIVA.PARAGRAFO}</p>
      <table>
        <thead>
          <tr>
            <th>Prazo de Acumulação</th>
            <th>Alíquotas Regressivas de Acumulação</th>
          </tr>
        </thead>
        <tbody>
          {TEXTO_TOOLTIP_REGRESSIVA.TABELA.map(({ prazo, aliquota }) => (
            <tr key={prazo}>
              <td>{prazo}</td>
              <td>{aliquota}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </ContainerTooltipRegressiva>
  );
};
