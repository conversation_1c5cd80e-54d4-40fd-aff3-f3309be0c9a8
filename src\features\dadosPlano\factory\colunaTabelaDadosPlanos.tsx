import { tryGetMonetaryValueOrDefault } from '@cvp/utils';
import { TColumn, TDadosPlano } from '../exports';

export const colunasTabelaDadosPlanos = (): TColumn[] => [
  {
    name: '<PERSON><PERSON><PERSON>',
    minWidth: '30%',
    maxWidth: '200px',
    selector: ({ rendaDescricao }: TDadosPlano) => rendaDescricao,
  },
  {
    name: 'Prazo (anos)',
    maxWidth: '350px',
    selector: ({ rendaTipo }: TDadosPlano) => rendaTipo,
  },
  {
    name: '<PERSON><PERSON> da contribuição',
    maxWidth: '350px',
    selector: ({ valorEsperado }: TDadosPlano) =>
      tryGetMonetaryValueOrDefault(valorEsperado),
  },
  {
    name: 'Valor do benefício',
    maxWidth: '350px',
    selector: ({ beneficioValor }: TDadosPlano) =>
      tryGetMonetaryValueOrDefault(beneficioValor),
  },
  {
    name: 'Status',
    maxWidth: '120px',
    selector: ({ situacao }: TDadosPlano) => situacao,
  },
];
