import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { checkIfAllItemsAreTrue, getSessionItem } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { useState } from 'react';
import {
  TPayloadSegundaViaBoleto,
  TResponseSegundaViaBoleto,
} from '../types/DadosPagamento';

export const useObterBoleto = (
  cobranca?: string,
): {
  baixando: boolean;
  erro: string | undefined;
  obterBoleto: (numeroCobranca: string) => Promise<string[]>;
} => {
  const payload: TPayloadSegundaViaBoleto = {
    cpf: String(getSessionItem('cpfCnpj')),
    numeroCobranca: cobranca,
  };

  const [erro, setErro] = useState<string>();

  const { loading: baixando, invocarApiGatewayCvpComToken: fetchData } =
    useApiGatewayCvpInvoker<
      TPayloadSegundaViaBoleto,
      TResponseSegundaViaBoleto
    >(PECOS.ObterSegundaVia, {
      data: payload,
      autoFetch: false,
    });

  const obterBoleto = async (numeroCobranca: string): Promise<string[]> => {
    const dados = await fetchData({
      cpf: String(getSessionItem('cpfCnpj')),
      numeroCobranca,
    });

    const errorMsg = dados?.mensagens?.[0].descricao;

    if (!dados?.sucessoGI) {
      setErro(errorMsg);
    }
    if (
      checkIfAllItemsAreTrue([
        !!dados,
        !!dados?.sucessoGI,
        !!dados?.entidade?.return,
      ])
    ) {
      return [dados!.entidade!.return];
    }

    return [];
  };

  return {
    baixando,
    erro,
    obterBoleto,
  };
};
