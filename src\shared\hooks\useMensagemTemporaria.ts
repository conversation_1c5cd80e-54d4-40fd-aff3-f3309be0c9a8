import { useState } from 'react';

const TIMER_DEFAULT = 3000;

export const useMensagemTemporaria = (): [
  string | undefined,
  (msg: string, timer?: number) => void,
] => {
  const [mensagem, setMensagemState] = useState<undefined | string>(undefined);

  const setMensagem = (msg: string, timer = TIMER_DEFAULT) => {
    setMensagemState(msg);

    setTimeout(() => {
      setMensagemState(undefined);
    }, timer);
  };

  return [mensagem, setMensagem];
};
