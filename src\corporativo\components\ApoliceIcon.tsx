import { Svg } from '@cvp/design-system-caixa';
import { ISvgProps } from '@cvp/design-system-caixa/dist/atoms/Svg';

const IconApolice: React.FC<ISvgProps> = ({ size }) => {
  return (
    <Svg width="16" height="16" viewBox="0 0 16 16" fill="none" size={size}>
      <path
        d="M13.8333 2.16667V13.8333H2.16667V2.16667H13.8333ZM13.8333 0.5H2.16667C1.25 0.5 0.5 1.25 0.5 2.16667V13.8333C0.5 14.75 1.25 15.5 2.16667 15.5H13.8333C14.75 15.5 15.5 14.75 15.5 13.8333V2.16667C15.5 1.25 14.75 0.5 13.8333 0.5Z"
        fill="#9EB2B8"
      />
      <path
        d="M9.66667 12.1667H3.83333V10.5H9.66667V12.1667ZM12.1667 8.83333H3.83333V7.16667H12.1667V8.83333ZM12.1667 5.5H3.83333V3.83333H12.1667V5.5Z"
        fill="#9EB2B8"
      />
    </Svg>
  );
};

export default IconApolice;
