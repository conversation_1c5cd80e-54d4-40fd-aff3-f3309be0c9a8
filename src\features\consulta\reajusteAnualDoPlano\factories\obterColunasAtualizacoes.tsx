import {
  Ds,
  Styles,
  Types,
  Utils,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

export const obterColunasHistoricoAtualizacoes: Types.TObterColunasHistoricoAtualizacoes =
  consultarDetalhes => [
    {
      selector: row => row.numAno,
      name: 'An<PERSON>',
    },
    {
      name: 'Detalhes',
      cell: row => (
        <Styles.DetalhesContainer
          onClick={() => consultarDetalhes(row)}
          style={{ textDecoration: 'underline' }}
        >
          <Ds.Text variant="text-standard-400">Detalhes</Ds.Text>
        </Styles.DetalhesContainer>
      ),
    },
  ];

export const obterColunasHistoricoPorAno: Types.TObterColunasHistoricoPorAno =
  () => [
    {
      selector: row => row.desTipoAtualizacao,
      name: 'Tipo de atualização',
      minWidth: '250px',
    },
    {
      selector: row => row.dthRecal,
      name: 'Data',
      cell: row => Utils.formatarDataHoraAmigavel(row.dthRecal),
    },
    {
      selector: row => row.vlrAnterior,
      name: 'Valor anterior',
      cell: row => Utils.tryGetMonetaryValueOrDefault(row.vlrAnterior),
    },
    {
      selector: row => row.pctAtualizacao,
      name: 'Atualização *',
      cell: row => `${row.pctAtualizacao} % ${row.codIndice}`,
    },
    {
      selector: row => row.vlrFinal,
      name: 'Valor final',
      cell: row => Utils.tryGetMonetaryValueOrDefault(row.vlrFinal),
    },
  ];
