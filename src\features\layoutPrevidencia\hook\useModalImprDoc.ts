import { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  getSessionItem,
  getTernaryResult,
} from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import {
  tipoEmailConstants,
  TTipoEmail,
} from '@src/corporativo/infra/email/tipoEmail';
import useFetchDeclaracaoQuitacaoAnual from '@src/corporativo/infra/impressaoDocumentosPrevidencia/declaracaoQuitacaoAnual/useFetchDeclaracaoQuitacaoAnual';
import useFetchEmissaoCertificado from '@src/corporativo/infra/impressaoDocumentosPrevidencia/emissaoCertificado/useFetchEmissaoCertificado';
import useFetchExtratoDetalhado from '@src/corporativo/infra/impressaoDocumentosPrevidencia/extratoDetalhado/useFetchExtratoDetalhado';
import useFetchInformeRendimentosBeneficiarios from '@src/corporativo/infra/impressaoDocumentosPrevidencia/impostoRenda/informeRendimentoBeneficiarios/useFetchInformeRendimentoBeneficiarios';
import useFetchInformeRendimentos from '@src/corporativo/infra/impressaoDocumentosPrevidencia/impostoRenda/informeRendimentos/useFetchInformeRendimentos';
import useFetchObterBeneficiarios from '@src/corporativo/infra/impressaoDocumentosPrevidencia/impostoRenda/obterBeneficiarios/useFetchObterBeneficiarios';
import useFetchImprimirDadosExtratoCotasPrevidencia from '@src/corporativo/infra/impressaoDocumentosPrevidencia/imprimirDadosExtratoCotasPrevidencia/useFetchImprimirDadosExtratoCotasPrevidencia';
import { TImpressao } from '@src/corporativo/types/impressaoDocumentosPrevidencia/TImpressao';
import {
  EModalImprDocActionType,
  IModalImprDocState,
  IUseModalImprDocReturn,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_EXTRATO_RENTABILIDADE_COTAS,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_INFO_GUIA_PREENCHIMENTO_IR,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_INFO_INFORME_RENDIMENTOS,
  MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_LEI_DECLARACAO_QUITACAO_ANUAL,
  MODAL_IMPRIMIR_DOCUMENTOS_URL_GUIA_DE_PREENCHIMENTO_IR,
  TGetPdfDocumentoSelecionadoResponse,
  TModalImprDocActionType,
  getOpcoesSegundoSelect,
  isValidDateRange,
  parseAAAAMMDDStringToDateISOString,
  retornaDateISOStringPeriodoUltimosDias,
  retornaPeriodoDateISOString,
  valuesSelectPrimeiraOpcao,
  valuesSelectSegundaOpcaoExtratoDetalhado,
  valuesSelectSegundaOpcaoExtratoRentabilidadeCotas,
} from '@src/features/layoutPrevidencia/exports';
import { ROUTES } from '@src/shared/constants/PrevidenciaRoutes';
import { ChangeEventHandler, useContext, useEffect } from 'react';
import { NavigateFunction } from 'react-router-dom';

const useModalImprDoc = (
  modalImprDocState: IModalImprDocState,
  modalImprDocDispatch: React.Dispatch<TModalImprDocActionType>,
): IUseModalImprDocReturn => {
  const { certificadoAtivo, setImpressao, impressao, setParametrosScroll } =
    useContext(PrevidenciaContext);
  const { fetchObterBeneficiarios, isLoadingObterBeneficiarios } =
    useFetchObterBeneficiarios();

  const getPECOErrorMessageOrDefault = (
    response: TGetPdfDocumentoSelecionadoResponse,
  ) => {
    if (!response || typeof response === 'string')
      return MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF;
    return response.mensagens?.length && response.mensagens[0].descricao
      ? response.mensagens[0].descricao
      : MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_DEFAULT_ERRO_OBTER_PDF;
  };

  const { fetchDeclaracaoQuitacaoAnual, isDeclaracaoQuitacaoAnualLoading } =
    useFetchDeclaracaoQuitacaoAnual({
      ano: modalImprDocState.selectSegundaOpcao.value,
      numeroCertificado: certificadoAtivo.certificadoNumero,
      cpfCnpj: getSessionItem('cpfCnpj') ?? '',
    });

  const { fetchDataEmissaoCertificado, isLoadingEmissaoCertificado } =
    useFetchEmissaoCertificado();

  const { fetchExtratoDetalhado, isLoadingExtratoDetalhado } =
    useFetchExtratoDetalhado({
      numeroCertificado: certificadoAtivo.certificadoNumero,
      cpfCnpj: getSessionItem('cpfCnpj') ?? '',
      dataInicio: modalImprDocState.selectDataInicio.value,
      dataFim: modalImprDocState.selectDataFim.value,
      fundo: '',
    });

  const { fetchInformeRendimentos, isLoadingInformeRendimentos } =
    useFetchInformeRendimentos({
      AnoBase: modalImprDocState.selectSegundaOpcao.value,
      Cpf: getSessionItem('cpfCnpj') ?? '',
      Email: '',
      TipoPessoa: 'PF',
    });

  const {
    fetchInformeRendimentosBeneficiarios,
    isLoadingInformeRendimentosBeneficiarios,
  } = useFetchInformeRendimentosBeneficiarios({
    AnoBase: modalImprDocState.selectSegundaOpcao.value,
    Cpf: modalImprDocState.selectTerceiraOpcao.value,
    Email: '',
    TipoPessoa: 'PF',
  });

  const {
    fetchImprimirDadosExtratoCotasPrevidencia,
    isLoadingImprimirDadosExtratoCotasPrevidencia,
  } = useFetchImprimirDadosExtratoCotasPrevidencia({
    cpfCnpj: getSessionItem('cpfCnpj') ?? '',
    dataInicial: modalImprDocState.selectDataInicio.value,
    dataFinal: modalImprDocState.selectDataFim.value,
    numeroCertificado: certificadoAtivo.certificadoNumero,
  });

  type TEmailProps = {
    tipoEmail: TTipoEmail;
    parametrosEnvio: unknown;
  };

  let email: TEmailProps = {
    tipoEmail: '' as TTipoEmail,
    parametrosEnvio: {},
  };

  const enviarDadosDeImpressaoParaContexto = async (
    response: TGetPdfDocumentoSelecionadoResponse,
  ) => {
    let responsePdf = '';
    if (typeof response === 'string') {
      setImpressao({
        tipoDocumento: modalImprDocState.selectSegundaOpcao.text,
        base64: [response],
        tipoEmail: email.tipoEmail,
        parametrosEnvio: email.parametrosEnvio,
      });

      return response;
    }

    if (typeof response?.entidade === 'string') {
      responsePdf = response.entidade;
    }

    if (
      response?.entidade &&
      typeof response?.entidade === 'object' &&
      'return' in response.entidade
    ) {
      responsePdf = response?.entidade.return;
    }

    if (
      response?.entidade &&
      typeof response?.entidade === 'object' &&
      'informe' in response.entidade
    ) {
      responsePdf = response?.entidade.informe;
    }

    if (
      response?.entidade &&
      typeof response?.entidade === 'object' &&
      'blob' in response.entidade
    ) {
      responsePdf = await response?.entidade.blob.text();
    }

    if (!responsePdf) {
      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_MENSAGEM_ERRO_OBTER_PDF,
        mensagemErroObterPdf: getPECOErrorMessageOrDefault(response),
      });

      return null;
    }

    setImpressao({
      tipoDocumento: modalImprDocState.selectPrimeiraOpcao.text,
      base64: [responsePdf],
      parametrosEnvio: email.parametrosEnvio,
      tipoEmail: email.tipoEmail,
    });

    return responsePdf;
  };

  const getPdfDocumentoSelecionado = async () => {
    let response: TGetPdfDocumentoSelecionadoResponse;

    switch (modalImprDocState.selectPrimeiraOpcao.value) {
      case valuesSelectPrimeiraOpcao.EMISSAO_CERTIFICADO:
        response = await fetchDataEmissaoCertificado();
        email = {
          tipoEmail: tipoEmailConstants.SEGUNDA_VIA_CERTIFICADO,
          parametrosEnvio: {
            numeroCertificado: certificadoAtivo.certificadoNumero,
            cpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
          },
        };
        break;
      case valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL:
        response = await fetchDeclaracaoQuitacaoAnual();
        email = {
          tipoEmail: tipoEmailConstants.QUITACAO_ANUAL_DEBITOS,
          parametrosEnvio: {
            Ano: modalImprDocState.selectSegundaOpcao.value,
            NumeroCertificado: certificadoAtivo.certificadoNumero,
            cpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
          },
        };
        break;
      case valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO:
        response = await fetchExtratoDetalhado();
        email = {
          tipoEmail: tipoEmailConstants.EXTRATO_PREVIDENCIA,
          parametrosEnvio: {
            numeroCertificado: certificadoAtivo.certificadoNumero,
            cpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
            dataInicial: modalImprDocState.selectDataInicio.value,
            dataFinal: modalImprDocState.selectDataFim.value,
            fundo: '',
          },
        };
        break;
      case valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR:
        response = MODAL_IMPRIMIR_DOCUMENTOS_URL_GUIA_DE_PREENCHIMENTO_IR;
        email = {
          tipoEmail: tipoEmailConstants.GUIA_PREENCHIMENTO_IR,
          parametrosEnvio: {
            numeroCertificado: certificadoAtivo.certificadoNumero,
            CpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
          },
        };
        break;
      case valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS:
        response = await fetchInformeRendimentos();
        email = {
          tipoEmail: tipoEmailConstants.CONSULTA_IR,
          parametrosEnvio: {
            anoBase: modalImprDocState.selectSegundaOpcao.value,
            cpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
            numeroCertificado: certificadoAtivo.certificadoNumero,
            tipoPessoa: 'PF',
          },
        };
        break;
      case valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS:
        response = await fetchInformeRendimentosBeneficiarios();
        email = {
          tipoEmail: tipoEmailConstants.CONSULTA_IR,
          parametrosEnvio: {
            anoBase: modalImprDocState.selectSegundaOpcao.value,
            cpfCnpj: String(getSessionItem('cpfCnpj') || ''),
            cpfCnpjBeneficiario: modalImprDocState.selectTerceiraOpcao.value,
            numeroCertificado: certificadoAtivo.certificadoNumero,
            tipoPessoa: 'PF',
          },
        };
        break;
      case valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS:
        response = await fetchImprimirDadosExtratoCotasPrevidencia();
        email = {
          tipoEmail: tipoEmailConstants.EXTRATO_COTAS_RENTABILIDADE,
          parametrosEnvio: {
            DataInicial: modalImprDocState.selectDataInicio.value,
            DataFinal: modalImprDocState.selectDataFim.value,
            NumeroCertificado: certificadoAtivo.certificadoNumero,
            CpfCnpj: String(getSessionItem('cpfCnpj')) ?? '',
          },
        };
        break;
      default:
        throw new Error('Opção de documento inválido');
    }

    return enviarDadosDeImpressaoParaContexto(response);
  };

  const onGenerateDoc = async (navigate: NavigateFunction) => {
    const generateDocResponse = await getPdfDocumentoSelecionado();
    const rota = window.location.pathname;
    const valorScroll = window.scrollY;

    if (generateDocResponse) {
      modalImprDocDispatch({ type: EModalImprDocActionType.FECHAR_MODAL });

      if (
        modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS
      ) {
        const wnd = window.open('about', '_blank');

        if (wnd) {
          wnd.document.write(generateDocResponse);
          wnd.document.close();
          wnd.print();
        }

        return;
      }
      if (
        modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR
      ) {
        window.open(generateDocResponse);

        return;
      }
      setParametrosScroll({
        rota,
        valorScroll,
      });
      navigate(ROUTES.IMPRIMIR);
    }
  };

  const showSecondSelect = !!modalImprDocState.optionsSegundaOpcao.length;

  const isRequestLoading = checkIfSomeItemsAreTrue([
    isLoadingEmissaoCertificado,
    isDeclaracaoQuitacaoAnualLoading,
    isLoadingExtratoDetalhado,
    isLoadingInformeRendimentos,
    isLoadingInformeRendimentosBeneficiarios,
    isLoadingObterBeneficiarios,
    isLoadingImprimirDadosExtratoCotasPrevidencia,
  ]);

  const showThirdSelect = checkIfSomeItemsAreTrue([
    modalImprDocState.selectSegundaOpcao.value ===
      valuesSelectSegundaOpcaoExtratoDetalhado.PERIODO_PERSONALIZADO,
    modalImprDocState.selectSegundaOpcao.value ===
      valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.CONSULTA_PERSONALIZADA,
    !!modalImprDocState.optionsTerceiraOpcao.length,
  ]);

  const thirdSelectType = getTernaryResult(
    checkIfSomeItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS,
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
    ]),
    'select',
    'period',
  );

  const onChangeSelectPrimary = (selectedOption: SelectItem[]) => {
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_SELECT_PRIMEIRA_OPCAO,
      selectPrimeiraOpcao: selectedOption[0],
    });

    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_OPTIONS_SEGUNDA_OPCAO,
      opcoesSegundoSelect: getOpcoesSegundoSelect(selectedOption[0]),
    });
  };

  const onChangeSelectSecondary = async (
    selectedOption: SelectItem[],
  ): Promise<void> => {
    const selectedSecondOption = selectedOption[0];
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_SELECT_SEGUNDA_OPCAO,
      selectSegundaOpcao: selectedSecondOption,
    });

    if (
      checkIfAllItemsAreTrue([
        modalImprDocState.selectPrimeiraOpcao.value ===
          valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
      ])
    ) {
      const beneficiarios = await fetchObterBeneficiarios({
        ano: selectedOption[0].value,
        cpf: getSessionItem('cpfCnpj') || '',
      });

      if (!beneficiarios?.entidade?.length) {
        modalImprDocDispatch({
          type: EModalImprDocActionType.SET_MENSAGEM_ERRO_OBTER_PDF,
          mensagemErroObterPdf: getPECOErrorMessageOrDefault(beneficiarios),
        });
        return;
      }

      const beneficiariosArray: SelectItem[] = beneficiarios.entidade.map(
        beneficiario => ({
          text: `${beneficiario.nomePessoa} - ${beneficiario.cpf}`,
          value: beneficiario.cpf,
        }),
      );

      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_OPTIONS_TERCEIRA_OPCAO,
        opcoesTerceiroSelect: beneficiariosArray,
      });
    }

    if (
      checkIfAllItemsAreTrue([
        modalImprDocState.selectPrimeiraOpcao.value ===
          valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO,
        selectedSecondOption.value !==
          valuesSelectSegundaOpcaoExtratoDetalhado.PERIODO_PERSONALIZADO,
      ])
    ) {
      const { dataInicio, dataFim } = retornaPeriodoDateISOString(
        selectedSecondOption.value,
      );

      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_SELECT_DATA_INICIO,
        selectDataInicio: {
          text: '',
          value: dataInicio,
        },
      });

      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_SELECT_DATA_FIM,
        selectDataFim: {
          text: '',
          value: dataFim,
        },
      });
    }

    if (
      checkIfAllItemsAreTrue([
        modalImprDocState.selectPrimeiraOpcao.value ===
          valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS,
        selectedOption[0].value !==
          valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.CONSULTA_PERSONALIZADA,
      ])
    ) {
      const { dataInicio, dataFim } = retornaDateISOStringPeriodoUltimosDias(
        selectedSecondOption.value,
      );

      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_SELECT_DATA_INICIO,
        selectDataInicio: {
          text: '',
          value: dataInicio,
        },
      });

      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_SELECT_DATA_FIM,
        selectDataFim: {
          text: '',
          value: dataFim,
        },
      });
    }
  };

  const disableButton = checkIfSomeItemsAreTrue([
    !modalImprDocState.selectPrimeiraOpcao.value,
    isRequestLoading,
    checkIfAllItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL,
      !modalImprDocState.selectSegundaOpcao.value,
    ]),
    !!modalImprDocState.mensagemErroSelectDataInicio,
    !!modalImprDocState.mensagemErroSelectDataFim,
    checkIfAllItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO,
      checkIfSomeItemsAreTrue([
        !modalImprDocState.selectDataInicio.value,
        !modalImprDocState.selectDataFim.value,
      ]),
    ]),
    checkIfAllItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS,
      !modalImprDocState.selectSegundaOpcao.value,
    ]),
    checkIfAllItemsAreTrue([
      modalImprDocState.selectSegundaOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
      !modalImprDocState.selectSegundaOpcao.value,
    ]),
    checkIfAllItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS,
      !modalImprDocState.selectSegundaOpcao.value,
    ]),
    checkIfAllItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
      !modalImprDocState.selectSegundaOpcao.value,
    ]),
    checkIfAllItemsAreTrue([
      modalImprDocState.selectPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
      !!modalImprDocState.selectSegundaOpcao.value,
      !modalImprDocState.selectTerceiraOpcao.value,
    ]),
    checkIfAllItemsAreTrue([
      modalImprDocState.selectSegundaOpcao.value ===
        valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.CONSULTA_PERSONALIZADA,
      checkIfSomeItemsAreTrue([
        !modalImprDocState.selectDataInicio.value,
        !modalImprDocState.selectDataFim.value,
      ]),
    ]),
  ]);

  const getModalImprDocMessage = () => {
    if (
      modalImprDocState.selectPrimeiraOpcao.value ===
      valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL
    )
      return MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_LEI_DECLARACAO_QUITACAO_ANUAL;

    if (
      modalImprDocState.selectPrimeiraOpcao.value ===
      valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR
    )
      return MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_INFO_GUIA_PREENCHIMENTO_IR;

    if (
      modalImprDocState.selectPrimeiraOpcao.value ===
      valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS
    )
      return MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_INFO_INFORME_RENDIMENTOS;

    if (
      modalImprDocState.selectPrimeiraOpcao.value ===
      valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS
    )
      return MODAL_IMPRIMIR_DOCUMENTOS_TEXTO_EXTRATO_RENTABILIDADE_COTAS;
    return '';
  };

  const onChangeDateFieldDataInicio: ChangeEventHandler<
    HTMLInputElement
  > = e => {
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_INICIO,
      mensagemErroDataInicio: '',
    });

    const isoStringDate = parseAAAAMMDDStringToDateISOString(e.target.value);
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_SELECT_DATA_INICIO,
      selectDataInicio: {
        text: 'DATA_INICIO',
        value: isoStringDate,
      },
    });

    const isValidFromDate = isValidDateRange(
      isoStringDate,
      modalImprDocState.selectDataFim.value,
      err => {
        modalImprDocDispatch({
          type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_INICIO,
          mensagemErroDataInicio: err,
        });
      },
    );

    if (isValidFromDate)
      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_FIM,
        mensagemErroDataFim: '',
      });
  };

  const onChangeDateFieldDataFim: ChangeEventHandler<HTMLInputElement> = e => {
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_FIM,
      mensagemErroDataFim: '',
    });

    const isoStringDate = parseAAAAMMDDStringToDateISOString(e.target.value);
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_SELECT_DATA_FIM,
      selectDataFim: {
        text: 'DATA_FIM',
        value: isoStringDate,
      },
    });

    const isValidToDate = isValidDateRange(
      modalImprDocState.selectDataInicio.value,
      isoStringDate,
      err => {
        modalImprDocDispatch({
          type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_FIM,
          mensagemErroDataFim: err,
        });
      },
    );

    if (isValidToDate)
      modalImprDocDispatch({
        type: EModalImprDocActionType.SET_MENSAGEM_ERRO_DATA_INICIO,
        mensagemErroDataInicio: '',
      });
  };

  const onChangeSelectThird = (selectedOption: SelectItem[]) => {
    modalImprDocDispatch({
      type: EModalImprDocActionType.SET_SELECT_TERCEIRA_OPCAO,
      selectTerceiraOpcao: selectedOption[0],
    });
  };

  useEffect(() => {
    if (
      checkIfAllItemsAreTrue([
        modalImprDocState.isModalImprDocOpen,
        !!impressao,
      ])
    )
      setImpressao({} as TImpressao);
  }, [modalImprDocState.isModalImprDocOpen, impressao]);

  return {
    onGenerateDoc,
    isRequestLoading,
    showSecondSelect,
    disableButton,
    showThirdSelect,
    getModalImprDocMessage,
    getPdfDocumentoSelecionado,
    thirdSelectType,
    onChangeSelectPrimary,
    onChangeSelectSecondary,
    onChangeSelectThird,
    onChangeDateFieldDataInicio,
    onChangeDateFieldDataFim,
  };
};

export default useModalImprDoc;
