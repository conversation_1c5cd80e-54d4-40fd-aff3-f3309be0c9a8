import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { TTiposDePerfis } from '@src/shared/types/TTiposDePerfis';

export interface IListarFundosParaResgatePayload {
  codigoCertificado: string;
}

export interface IListarFundosParaResgateAliquotaOpcoes {
  descricaoAliquota: string;
  codigoAliquota: string;
}

export interface IListarFundosParaResgateAliquota {
  indicadorPermiteEditarAliquota: boolean;
  aliquotaAtual: string;
  opcoesAliquotas: IListarFundosParaResgateAliquotaOpcoes[];
}

export interface IListarFundosParaResgateSaldo {
  saldoTotal: number;
  saldoTotalBloqueado: number;
  saldoDisponivelParaResgate: number;
}

export interface IListarFundosParaResgateLimitesCertificado {
  valorMinimoResgate: number;
  valorMaximoResgate: number;
  valorMinimoPermanencia: number;
}

export interface IListarFundosParaResgateFundosDisponiveis {
  permiteResgate: boolean;
  codigoReserva: string;
  descricaoReserva: string;
  codigoFundo: string;
  descricaoFundo: string;
  saldoTotal: number;
  saldoDisponivel: number;
  saldoBloqueado: number;
  valorMinimoPermanencia: number;
  valorRentabilidadeDozeMeses: number;
  perfilFundo: TTiposDePerfis;
  riscoFundo: string;
  exibeMensagem: boolean;
  mensagensFundo: IListarFundosParaResgateFundosDisponiveisMensagensFundo[];
  carencia: null;
  valorRetirar?: string;
  selecionado?: boolean;
  tipoResgate?: string;
}

export interface IListarFundosParaResgateFundosDisponiveisMensagensFundo {
  tipo: string;
  codigo: string;
  descricao: string;
}

export interface IListarFundosParaResgateMensagensCertificado {
  tipo: string;
  codigo: string;
  descricao: string | React.ReactElement;
}

export interface IListarFundosParaResgateResponse {
  numeroResgate: string;
  indicadorPermiteResgate: boolean;
  indicadorPrazoDeDiferimentoExpirado: boolean;
  indicadorPermiteAssinatura: boolean;
  aliquota: IListarFundosParaResgateAliquota;
  saldo: IListarFundosParaResgateSaldo;
  limitesCertificado: IListarFundosParaResgateLimitesCertificado;
  fundosDisponiveis: IListarFundosParaResgateFundosDisponiveis[];
  mensagensCertificado: IListarFundosParaResgateMensagensCertificado[];
}

export interface IUseListarFundosParaResgateReturn {
  listaFundosParaResgate: IListarFundosParaResgateResponse;
  isLoadingListaFundosParaResgate: boolean;
  listarFundosParaResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<IListarFundosParaResgateResponse> | undefined
  >;
}
