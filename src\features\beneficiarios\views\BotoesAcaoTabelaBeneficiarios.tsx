import {
  BeneficiariosDispatchContext,
  Button,
  EBeneficiariosActionKind,
  IconEditOutlinedSharp,
  Match,
  ModalNovoBeneficiario,
  S,
  SwitchCase,
  useBeneficiariosForm,
  useContext,
} from '../exports';

export const BotoesAcaoTabelaBeneficiarios: React.FC<{
  coberturaId: string;
  adicionarBeneficiario: (cpf: string, sexo: string) => void;
}> = ({ coberturaId, adicionarBeneficiario }) => {
  const {
    cancelarEdicaoCobertura,
    editarCobertura,
    verificarCoberturaEditando,
  } = useBeneficiariosForm();
  const editando = verificarCoberturaEditando(coberturaId);
  const beneficiariosDispatch = useContext(BeneficiariosDispatchContext);

  return (
    <S.ButtonsContainer>
      <SwitchCase>
        <Match when={editando}>
          <Button
            variant="secondary-outlined"
            leftIcon={<IconEditOutlinedSharp size="medium" />}
            onClick={() => {
              cancelarEdicaoCobertura(coberturaId);
            }}
            type="button"
          >
            Cancelar
          </Button>
        </Match>
        <Match when={!editando}>
          <Button
            variant="secondary-outlined"
            leftIcon={<IconEditOutlinedSharp size="medium" />}
            onClick={() => {
              beneficiariosDispatch({
                type: EBeneficiariosActionKind.EDITAR_BENEFICIARIOS,
              });
              editarCobertura(coberturaId);
            }}
            type="button"
          >
            Editar
          </Button>
        </Match>
      </SwitchCase>
      <ModalNovoBeneficiario
        adicionarBeneficiario={adicionarBeneficiario}
        coberturaId={coberturaId}
      />
    </S.ButtonsContainer>
  );
};
