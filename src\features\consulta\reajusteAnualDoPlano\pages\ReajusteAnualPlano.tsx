import {
  Ds,
  Hooks,
  Infra,
  Styles,
  Utils,
  Views,
  CONSTANTES,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

export const ReajusteAnualPlano: React.FC = () => {
  const { response, loading } = Infra.useObterHistorico();
  const periodoDetalhes = Hooks.usePeriodoHistorico();
  const [NOVOS_VALORES, TIPOS_ATUALIZACAO] = CONSTANTES.TEXTOS.HEADER_DESCRICAO;

  return (
    <Styles.Container>
      <Ds.Text variant="text-standard-400">{NOVOS_VALORES}</Ds.Text>

      <Ds.Text variant="text-standard-400">{TIPOS_ATUALIZACAO}</Ds.Text>

      <Views.CardExplicacao />

      <Ds.SwitchCase
        fallback={
          <>
            <Views.Alerta />

            <Views.TabelaAtualizacoes
              data={response}
              consultarDetalhes={periodoDetalhes.alterarAnoAtivo}
            />
          </>
        }
      >
        <Ds.Match
          when={Utils.checkIfSomeItemsAreTrue([
            loading,
            periodoDetalhes.loading,
          ])}
        >
          <Ds.Grid margin="30px" justify="center">
            <Ds.LoadingSpinner size="medium">Carregando...</Ds.LoadingSpinner>
          </Ds.Grid>
        </Ds.Match>

        <Ds.Match when={!!periodoDetalhes.anoAtivo.length}>
          <Views.FiltroPeriodo
            ano={periodoDetalhes.anoAtivo}
            atualizacoes={response}
            selecionarFiltro={periodoDetalhes.alterarAnoAtivo}
          />
          <Views.PeriodoExibido
            onClickVoltar={periodoDetalhes.voltarEtapa}
            data={periodoDetalhes.response}
          />
        </Ds.Match>
      </Ds.SwitchCase>
    </Styles.Container>
  );
};
