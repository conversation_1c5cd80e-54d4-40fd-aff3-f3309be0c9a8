import {
  formatarContaConcatenada,
  LABEL_NOVA_CONTA,
  mapearContasBancarias,
  useAlterarDadosPagamento,
  GridItem,
  Select,
  Text,
  tryGetValueOrDefault,
  ConditionalRenderer,
} from '@src/features/financeiro/dadosPagamento/exports';

const SelectContaBancaria: React.FC = () => {
  const { dadosCertificado, handleAlterarContaExistente } =
    useAlterarDadosPagamento();

  const primeiroCertificado = dadosCertificado?.[0];

  return (
    <GridItem xs="1 / 2">
      <Text variant="text-large-700">Agência e Conta</Text>
      <ConditionalRenderer
        condition={!!dadosCertificado?.length && !!primeiroCertificado}
      >
        <Select
          options={[
            LABEL_NOVA_CONTA,
            ...mapearContasBancarias(dadosCertificado),
          ]}
          placeholder={tryGetValueOrDefault(
            [formatarContaConcatenada(primeiroCertificado)],
            '-',
          )}
          size="standard"
          onChange={handleAlterarContaExistente}
          variant="box-classic"
          sizeWidth={undefined}
        />
      </ConditionalRenderer>
    </GridItem>
  );
};

export default SelectContaBancaria;
