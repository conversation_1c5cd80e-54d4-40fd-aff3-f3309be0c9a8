import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { IEndereco } from '@src/corporativo/types/dadosParticipante';
import { useContext } from 'react';
import { PECOS } from '../config/api/endpoints';

export interface IAlteracaoBase {
  numeroCertificado: string;
  cpfCnpj: string;
}

interface IPayloadAlterarDadosParticipante {
  consentimentoCaixa: boolean;
  consentimentoParceiros: boolean;
  dadosEmailParticipante?: {
    emailId: string | null;
    email: string;
    tipoEmail: string;
  } & IAlteracaoBase;
  dadosEnderecoParticipante?: IEndereco & IAlteracaoBase;
  dadosTelefoneParticipante?: {
    listaDadosTelefoneParticipante: {
      telefoneId: string;
      codigoArea: string;
      numeroTelefone: string;
      tipoTelefone: string;
      aceitarSms: string;
      telefonePrincipal: string;
      extensaoNumero: string;
      localTelefone: string;
    }[];
  } & IAlteracaoBase;
}

export const useAlterarDadosParticipante = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const { response, loading, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker(PECOS.AtualizarDadosParticipante, {
      data: {
        cpfCnpj: getSessionItem('cpfCnpj'),
        numeroCertificado: certificadoAtivo.certificadoNumero,
      },
      autoFetch: false,
    });

  const alterarDadosParticipante = (
    payload: IPayloadAlterarDadosParticipante,
  ) => invocarApiGatewayCvpComToken(payload);

  return {
    response: tryGetValueOrDefault([response?.entidade], null),
    alterarDadosParticipante,
    loading,
  };
};
