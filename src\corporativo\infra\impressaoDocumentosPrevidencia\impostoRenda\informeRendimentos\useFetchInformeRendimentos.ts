import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IUseFetchInformeRendimentosPayload,
  IUseFetchInformeRendimentosReturn,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchInformeRendimentos';

const useFetchInformeRendimentos = (
  payload?: IUseFetchInformeRendimentosPayload,
): IUseFetchInformeRendimentosReturn => {
  const { loading, invocarApiGatewayCvpComToken } = useApiGatewayCvpInvoker<
    IUseFetchInformeRendimentosPayload,
    { informe: string }
  >(PECOS.ObterInforme, {
    data: payload,
    autoFetch: false,
  });

  return {
    isLoadingInformeRendimentos: loading,
    fetchInformeRendimentos: invocarApiGatewayCvpComToken,
  };
};

export default useFetchInformeRendimentos;
