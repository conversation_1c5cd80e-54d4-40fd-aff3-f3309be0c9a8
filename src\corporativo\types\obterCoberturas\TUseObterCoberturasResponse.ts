export type TUseObterCoberturasResponse = {
  retorno: TUseObterCoberturasResponseReturn;
  msgErroExcessao: string;
};

export type TUseObterCoberturasResponseReturn = {
  empresaId: string;
  numCertificado: string;
  beneficios: TBeneficios[] | undefined;
  saldoTotal: string;
};

export type TBeneficios = {
  planoId: string;
  coberturaId: string;
  empresaId: string;
  descricaoCobertura: string;
  tipoBeneficio: string;
  situacao: string;
  subSituacao: string;
  valorBeneficio: string;
  valorContribuicao: string;
  numCnpjFundo: string;
  certificado: {
    empresaId: string;
    numCertificado: string;
  };
  termoDesejado: string;
  codTipoPagamentoOriginal: string;
  nomTipoPagamento: string;
  fatorRenda: string;
  vlrReversao: string;
  descBeneficiarioRecebeOriginal: string;
  beneficiarios: TBeneficiarios[] | undefined;
  tipoCobertura: string;
  descPeridoBeneficiarioRecebe: string;
};

export type TBeneficiarios = {
  tipoBeneficiario: string;
  pessoaFisica: {
    pessoaFisicaId: string;
    genero: string;
    dataNascimento: Date;
    nome: string;
    cpfCnpj: string;
  };
  percentualDistribuicao: string;
  situacao: string;
  grauParentescoId: string;
  descricaoGrauParentesco: string;
};
