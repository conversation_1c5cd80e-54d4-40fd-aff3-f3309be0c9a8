import { IHandleReponseResult } from '@cvp/componentes-posvenda';
import { TOperacoesConfirmarAssinatura } from '@src/corporativo/types/shared/operacoesPrevidencia';

export interface IConfirmarOperacaoAssinaturaPayload {
  codigoSolicitacao: string;
  metaDadoConfirmacao: string;
  tipoOperacao: TOperacoesConfirmarAssinatura;
}

export interface IConfirmarAssinaturaInput
  extends IConfirmarOperacaoAssinaturaPayload {
  codigoCertificado: string;
  codigoTipoAssinatura: string;
}

export interface IResponseConfirmarOperacaoAssinatura {
  mensagemNegocio: string;
  mensagemSistema: string;
  sucesso: boolean;
}

export interface IUseConfirmarOperacaoAssinaturaCaixa {
  response?: IResponseConfirmarOperacaoAssinatura;
  loading: boolean;
  confirmarAssinatura: (
    input: IConfirmarOperacaoAssinaturaPayload,
  ) => Promise<
    IHandleReponseResult<IResponseConfirmarOperacaoAssinatura> | undefined
  >;
}
