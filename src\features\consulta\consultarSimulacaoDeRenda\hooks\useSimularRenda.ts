import * as useConsultarRendaImports from '@src/features/consulta/consultarSimulacaoDeRenda/exports';

export const useConsultarRenda = ({
  dataUltimasSimulacoes,
  loadingUltimasSimulacoes,
}: useConsultarRendaImports.IUseConsultarRenda) => {
  const { certificadoAtivo } = useConsultarRendaImports.useContext(
    useConsultarRendaImports.PrevidenciaContext,
  );
  const { invocarApiGatewayCvpComToken, loading } =
    useConsultarRendaImports.useSimularRenda();
  const [data, setData] = useConsultarRendaImports.useState<
    useConsultarRendaImports.IHandleReponseResult<useConsultarRendaImports.ISimulacaoDeRendaResponse>
  >(
    {} as useConsultarRendaImports.IHandleReponseResult<useConsultarRendaImports.ISimulacaoDeRendaResponse>,
  );
  const [income, setIncome] = useConsultarRendaImports.useState(
    useConsultarRendaImports.INITIAL_VALUE_INCOME,
  );

  const [openModalCertificado, setOpenModalCertificado] =
    useConsultarRendaImports.useState(false);

  const { podeAlterarRegimeTributario, tipoRegime } =
    useConsultarRendaImports.useConsultarRegimeTributario();

  const showTableConsulta = useConsultarRendaImports.checkIfAllItemsAreTrue([
    !!Object.keys(data).length,
    !loading,
    !dataUltimasSimulacoes.length,
    !loadingUltimasSimulacoes,
  ]);

  const showTableUltimasSimulacoes =
    useConsultarRendaImports.checkIfAllItemsAreTrue([
      !!dataUltimasSimulacoes.length,
      !loadingUltimasSimulacoes,
    ]);

  const showFooterTable = useConsultarRendaImports.checkIfAllItemsAreTrue([
    !!Object.keys(data).length,
    !loading,
    !loadingUltimasSimulacoes,
  ]);

  const showSelectRendaReversivel =
    useConsultarRendaImports.checkIfAllItemsAreTrue([
      income.renda === useConsultarRendaImports.TIPO_DE_RENDA.reversivel,
      !Object.keys(data).length,
      !dataUltimasSimulacoes.length,
    ]);

  const tipoDeRendaValido = useConsultarRendaImports.checkIfSomeItemsAreTrue([
    income.renda === useConsultarRendaImports.TIPO_DE_RENDA.temporaria,
    income.renda === useConsultarRendaImports.TIPO_DE_RENDA.prazo_certo,
    income.renda === useConsultarRendaImports.TIPO_DE_RENDA.prazo_minimo,
  ]);

  const showSelectTemporariaPrazoCertoPrazoMinimo =
    useConsultarRendaImports.checkIfAllItemsAreTrue([
      tipoDeRendaValido,
      !Object.keys(data).length,
      !dataUltimasSimulacoes.length,
    ]);

  const verificaSePodeAlterarRegimeTributario =
    useConsultarRendaImports.checkIfAllItemsAreTrue([
      podeAlterarRegimeTributario !==
        useConsultarRendaImports.HABILITADO_PARA_ALTERAR_REGIME,
      !!income.renda,
    ]);

  const getOptionsSelect = (
    arraySelelect: Record<string, string>,
  ): useConsultarRendaImports.ISelectSimulacaoDeRendaProps[] => {
    return Object.keys(arraySelelect).map(id => ({
      value: id,
      text: arraySelelect[id],
    }));
  };

  const getOptionsSelectSexoBeneficiario = getOptionsSelect(
    useConsultarRendaImports.SEXO_BENEFICIARIO,
  );
  const getOptionsSelectPorcentagemRenda = getOptionsSelect(
    useConsultarRendaImports.PORCENTAGEM_REVERSIVEL_BENEFICIARIO,
  );

  const payloadMap =
    useConsultarRendaImports.useMemo<useConsultarRendaImports.TSimulacaoPayloadMap>(() => {
      const criarPayloadPrazoCertoTemporaria = (values?: {
        qtdPrazoAnos: string;
      }) => {
        return {
          tipoRenda: income.numeroDaRenda,
          qtdPrazoAnos: values?.qtdPrazoAnos,
          tipoTributacao: certificadoAtivo.opcaoImpostoTipo,
        };
      };

      return {
        [useConsultarRendaImports.TIPO_DE_RENDA.vitalicia]: () => ({
          tipoRenda: income.numeroDaRenda,
          tipoTributacao: certificadoAtivo.opcaoImpostoTipo,
        }),
        [useConsultarRendaImports.TIPO_DE_RENDA.temporaria]:
          criarPayloadPrazoCertoTemporaria,
        [useConsultarRendaImports.TIPO_DE_RENDA.prazo_minimo]: (values?) => ({
          tipoRenda: income.numeroDaRenda,
          qtdPrazoAnos: values?.qtdPrazoAnos,
          tipoTributacao: certificadoAtivo.opcaoImpostoTipo,
        }),
        [useConsultarRendaImports.TIPO_DE_RENDA.reversivel]: (values?: {
          qtdPrazoAnos: string;
          pctReversao: string;
          dthNascimentoConjuge: string;
          staGeneroConjuge: string;
        }) => ({
          tipoRenda: income.numeroDaRenda,
          pctReversao: values?.pctReversao,
          dthNascimentoConjuge: values?.dthNascimentoConjuge,
          staGeneroConjuge: values?.staGeneroConjuge,
          cpfBeneficiario: '0',
          tipoTributacao: certificadoAtivo.opcaoImpostoTipo,
        }),
        [useConsultarRendaImports.TIPO_DE_RENDA.prazo_certo]:
          criarPayloadPrazoCertoTemporaria,
      };
    }, [income.numeroDaRenda, certificadoAtivo.opcaoImpostoTipo]);

  const consultarRenda = useConsultarRendaImports.useCallback(
    async (values?: useConsultarRendaImports.TSimulacaoPayloadValues) => {
      const builder = payloadMap[income.renda];
      if (!builder) return;
      const body = builder(values);
      try {
        const resp = await invocarApiGatewayCvpComToken(body);
        if (!resp) throw new Error();
        setData(resp);
      } catch (err) {
        setData(
          {} as useConsultarRendaImports.IHandleReponseResult<useConsultarRendaImports.ISimulacaoDeRendaResponse>,
        );
      }
    },
    [payloadMap, income.renda, invocarApiGatewayCvpComToken],
  );

  const handleClearValuesData = () => {
    setData(
      {} as useConsultarRendaImports.IHandleReponseResult<useConsultarRendaImports.ISimulacaoDeRendaResponse>,
    );
  };

  const dadosCarregados = useConsultarRendaImports.checkIfAllItemsAreTrue([
    !Object.keys(data).length,
    loading,
  ]);

  const ultimasAtualizacoesCarregadas =
    useConsultarRendaImports.checkIfAllItemsAreTrue([
      !dataUltimasSimulacoes.length,
      loadingUltimasSimulacoes,
    ]);

  const deveExibirLoading = useConsultarRendaImports.checkIfSomeItemsAreTrue([
    dadosCarregados,
    ultimasAtualizacoesCarregadas,
  ]);

  const handleSetIncomeRenda = (
    value: useConsultarRendaImports.ISelectItem[],
  ) => {
    setIncome({
      renda: useConsultarRendaImports.validateAndNormalizeString(value[0].text),
      numeroDaRenda: value[0].value,
    });
  };

  useConsultarRendaImports.useEffect(() => {
    if (income.renda === useConsultarRendaImports.TIPO_DE_RENDA.vitalicia) {
      consultarRenda();
    }
    setOpenModalCertificado(verificaSePodeAlterarRegimeTributario);
  }, [income]);

  return {
    consultarRenda,
    setIncome,
    income,
    data,
    setData,
    loading,
    showTableConsulta,
    showTableUltimasSimulacoes,
    showFooterTable,
    showSelectRendaReversivel,
    showSelectTemporariaPrazoCertoPrazoMinimo,
    getOptionsSelectSexoBeneficiario,
    getOptionsSelectPorcentagemRenda,
    openModalCertificado,
    setOpenModalCertificado,
    tipoRegime,
    handleClearValuesData,
    deveExibirLoading,
    handleSetIncomeRenda,
  };
};
