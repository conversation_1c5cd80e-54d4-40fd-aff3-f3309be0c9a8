export const TEXTOS = {
  TITULO: 'Histórico de Solicitações',
  CABECALHO_TITULO:
    'Você pode visualizar as solicitações feitas para esse certificado por tipo de serviço ou por período. Escolha abaixo uma das opções:',
  TIPO_SOLICITACAO: 'Tipo de Solicitação',
  PERIODO_SOLICITACAO: 'Período',
  CONSULTAR: 'Consultar',
  EXIBICAO_PERIODO: 'Exibindo movimentações do preíodo de acumulação: ',
  SEM_DADOS: 'Não há histórico de solicitações para exibir',
  ERROS: {
    DATA_INVALIDA:
      'A data inicial não pode ser maior ou igual a final. Revise e tente novamente',
  },
  ASSINAR: 'Assinar',
};

export const VALORES_PERIODOS = {
  ULTIMO_MES: '30',
  ULTIMOS_3_MESES: '90',
  ULTIMOS_6_MESES: '180',
  ULTIMOS_9_MESES: '270',
  ULTIMOS_12_MESES: '365',
  PERIODO_PERSONALIZADO: '-1',
};

export const FILTRO_PERIODO = [
  { text: 'Último mês', value: VALORES_PERIODOS.ULTIMO_MES },
  { text: 'Últimos 3 meses', value: VALORES_PERIODOS.ULTIMOS_3_MESES },
  { text: 'Últimos 6 meses', value: VALORES_PERIODOS.ULTIMOS_6_MESES },
  { text: 'Últimos 9 meses', value: VALORES_PERIODOS.ULTIMOS_9_MESES },
  { text: 'Últimos 12 meses', value: VALORES_PERIODOS.ULTIMOS_12_MESES },
  {
    text: 'Período Personalizado',
    value: VALORES_PERIODOS.PERIODO_PERSONALIZADO,
  },
];

export const TIPO_SOLICITACAO = 'tipoSolicitacao';
export const PERIODO_VALOR = 'periodoValor';

export const FILTRO_TODOS = {
  text: 'Todos',
  value: 'Todos',
};

export const FILTRO_INICIAL = {
  periodoValor: {
    text: 'Últimos 12 meses',
    value: '365',
  },
  tipoSolicitacao: {
    text: 'Todos',
    value: 'Todos',
  },
};

export const STATUS_REQUISICAO_COMPROVANTE = {
  CONCLUIDA: 'CONCLUIDA',
  AGUARDANDO_ASSINATURA: 'AGUARDANDO ASSINATURA',
  EM_PAGAMENTO: 'EMPAGAMENTO',
  SOLICITADA: 'SOLICITADA',
};
