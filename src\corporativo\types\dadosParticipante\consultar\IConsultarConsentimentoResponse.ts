interface ICodigoDescription {
  codigo: number;
  descricao: string;
}

export interface ITiposConsentimento {
  macroProcessoNegocio: ICodigoDescription;
  processoNegocio: ICodigoDescription;
  subProcessoNegocio: ICodigoDescription;
  justificativa: ICodigoDescription;
  cliente: {
    codigo: string;
    identificacao: {
      codigo: string;
    };
  };
  evidencia: {
    codigo: string;
  };
  sistema: {
    codigo: string;
    nome: string;
    tipo: string;
    indice: string;
  };
  unidadeNegocio: {
    codigo: number;
    sigla: string;
    nome: string;
  };
  consentimento: {
    tipo: number;
    descricao: string;
    data: string;
  };
  esquecimento: {
    marca: string;
    data: string;
  };
  atualizacao: string;
  referencia: string;
}
export interface IConsultarConsentimentoResponse {
  consentimentos: ITiposConsentimento[];
}
