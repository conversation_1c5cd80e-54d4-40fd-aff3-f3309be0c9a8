import {
  COMPROVANTE_APORTE,
  enumNomeBancos,
  enumNumeroBancos,
  FILTRO_APORTE,
  FORMA_PAGAMENTO,
  formatarDataHoraAmigavel,
  GridItem,
  IDadosBancariosAporte,
  IFiltroTabelaAporte,
  Match,
  Text,
} from '@src/features/financeiro/aporte/exports';

interface IContaBancariaSelecionadaAporte {
  dadosAporte: IFiltroTabelaAporte;
  filtroTabelaFundos: IFiltroTabelaAporte;
  contaBancariaSelecionada: IDadosBancariosAporte;
}

const ContaBancariaSelecionadaAporte: React.FC<
  IContaBancariaSelecionadaAporte
> = ({ dadosAporte, filtroTabelaFundos, contaBancariaSelecionada }) => {
  return (
    <Match when={filtroTabelaFundos.formaPagamento === FORMA_PAGAMENTO.debito}>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.BANCO}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {enumNumeroBancos.CAIXA_ECONOMICA} -{enumNomeBancos.CAIXA_ECONOMICA}
        </Text>
      </GridItem>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.OPERACAO}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {contaBancariaSelecionada?.operacao}
        </Text>
      </GridItem>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.AGENCIA}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {contaBancariaSelecionada?.numeroAgencia}
        </Text>
      </GridItem>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {COMPROVANTE_APORTE.CONTA_BANCARIA}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {contaBancariaSelecionada?.conta}
        </Text>
      </GridItem>
      <GridItem xs="1/4">
        <Text variant="text-standard-600" fontColor="content-neutral-05">
          {FILTRO_APORTE.dataDebito}
        </Text>
        <Text variant="text-standard-400" fontColor="content-neutral-03">
          {formatarDataHoraAmigavel(dadosAporte.dataDebito)}
        </Text>
      </GridItem>
    </Match>
  );
};

export default ContaBancariaSelecionadaAporte;
