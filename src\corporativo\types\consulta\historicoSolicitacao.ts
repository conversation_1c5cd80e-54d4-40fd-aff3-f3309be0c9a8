export interface IResponseTipoSolicitacao {
  parametro: string;
  nomeParametro: string;
  valor: string;
}

export interface IResponseHistoricoSolicitacoes {
  canalRequisicao: string;
  codigoRequisicao: string;
  contaId: string;
  dataRequisicao: string;
  detalhesEvento: string;
  idRequisicao: string;
  statusRequisicao: string;
  tipoRequisicao: string;
  valorRequisicao: string;
  permiteCancelamento: string;
}

export interface IPayloadObterHistoricoSolicitacoes {
  cpfCnpj: string;
  numeroCertificado: string;
  dataInicial: string;
  dataFinal: string;
}
