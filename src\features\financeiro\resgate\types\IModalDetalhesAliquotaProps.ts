import {
  IConditionalRowStyleDetalhesAliquota,
  IObjetoEmailSimulacaoResgate,
  IObterDadosPorAliquotaRetorno,
} from '@src/features/financeiro/resgate/exports';

export interface IModalDetalhesAliquotaProps {
  objetoEmail: IObjetoEmailSimulacaoResgate;
  isOpenModalDetalhamento: boolean;
  toggleModalDetalhamento: () => void;
  obterDadosPorAliquota: (
    tipoAliquota?: string,
  ) => IObterDadosPorAliquotaRetorno;
  customizarLinhasTabela: (
    style: IConditionalRowStyleDetalhesAliquota,
  ) => IConditionalRowStyleDetalhesAliquota;
}
