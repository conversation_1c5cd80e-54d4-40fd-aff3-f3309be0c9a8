import {
  checkIfSomeItemsAreTrue,
  EEtapasAporte,
  IFiltroTabelaAporte,
  PrevidenciaContext,
  TFormikProps,
  useAporteContext,
  useContext,
} from '@src/features/financeiro/aporte/exports';

type TUseValorContribuicao = { handleBlur: () => void };

export const useValorContribuicao = (
  formik: TFormikProps<IFiltroTabelaAporte>,
): TUseValorContribuicao => {
  const { isClientePep } = useContext(PrevidenciaContext);

  const { setEtapa } = useAporteContext();

  const handleBlur = () => {
    if (
      checkIfSomeItemsAreTrue([
        parseFloat(formik.values.valorContribuicao) >= 100_000,
        isClientePep === true,
      ])
    ) {
      setEtapa(EEtapasAporte.OrigemRecursosAporte);
    }
  };
  return { handleBlur };
};
