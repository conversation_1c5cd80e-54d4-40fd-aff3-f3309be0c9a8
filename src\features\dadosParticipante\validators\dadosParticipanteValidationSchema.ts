import { QTD_MAXIMA_DIGITOS_CEP, VALIDACOES, Utils, Yup } from '../exports';

export const dadosParticipanteValidationSchema = Yup.object({
  cep: Yup.string()
    .required(VALIDACOES.MENSAGEM_OBRIGATORIO_CEP)
    .test({
      message: VALIDACOES.MENSAGEM_INVALIDO_CEP,
      test: (cep: string) =>
        Utils.cep.unmask(cep).length === QTD_MAXIMA_DIGITOS_CEP,
    }),
  endereco: Yup.string()
    .min(3, VALIDACOES.MENSAGEM_INVALIDO_ENDERECO)
    .required(VALIDACOES.MENSAGEM_OBRIGATORIO_ENDERECO),
  bairro: Yup.string()
    .min(3, VALIDACOES.MENSAGEM_INVALIDO_BAIRRO)
    .required(VALIDACOES.MENSAGEM_OBRIGATORIO_BAIRRO),
  numero: Yup.string(),
  complemento: Yup.string().optional(),
  nomeSocial: Yup.string().optional(),
  cidade: Yup.string().required(VALIDACOES.MENSAGEM_OBRIGATORIO_CIDADE),
  uf: Yup.string(),
  email: Yup.string()
    .email(VALIDACOES.MENSAGEM_INVALIDO_EMAIL)
    .matches(Utils.VALIDAR_EMAIL, VALIDACOES.MENSAGEM_INVALIDO_EMAIL)
    .max(40, VALIDACOES.MENSAGEM_MAX_LENGTH_EMAIL)
    .required(VALIDACOES.MENSAGEM_OBRIGATORIO_EMAIL),
  celular: Yup.string()
    .required(VALIDACOES.MENSAGEM_OBRIGATORIO_CELULAR)
    .min(9, VALIDACOES.MENSAGEM_QUANTIDADE_CARACTERES),

  telefone: Yup.string()
    .required(VALIDACOES.MENSAGEM_OBRIGATORIO_CELULAR)
    .min(9, VALIDACOES.MENSAGEM_QUANTIDADE_CARACTERES),
});

export type TFormDadosParticipanteSchema = Yup.InferType<
  typeof dadosParticipanteValidationSchema
>;
