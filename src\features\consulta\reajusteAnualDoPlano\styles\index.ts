import { Alert, Text } from '@cvp/design-system-caixa';
import styled from 'styled-components';

export const Container = styled.div`
  display: grid;
  gap: 1.5rem;
`;

export const DetalhesContainer = styled.div`
  cursor: pointer;
  text-decoration: underline;
`;

export const ExplicacaoCard = styled.div`
  border-radius: 10px;
  border: 1px solid #ddd;
`;

export const ExplicacaoContainer = styled.div`
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr 1px 1fr;
  padding: 2.5rem;
`;

export const ContainerSelect = styled.div`
  max-width: 283px;
`;

export const ExplicacaoContent = styled.div`
  display: grid;
  gap: 1.5rem;
`;

export const Typography = styled(Text)`
  display: inline;
`;

export const Footer = styled.footer`
  display: flex;
  justify-content: end;
`;

export const Alerta = styled(Alert)`
  margin: 0;
`;
