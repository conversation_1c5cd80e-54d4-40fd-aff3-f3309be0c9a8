import React from 'react';
import { Match, SwitchCase } from '@cvp/componentes-posvenda';
import { useTransferenciaServicosContext } from '@src/corporativo/context/financeiro/transferencias';
import * as Fundos from '.';

export const TransferenciaEntreFundosLayout: React.FC = () => {
  const { consultaOrigem } = useTransferenciaServicosContext();

  return (
    <>
      <SwitchCase fallback={undefined}>
        <Match when={!consultaOrigem.loading}>
          <Fundos.AlertasTransferencia />
        </Match>
      </SwitchCase>
      <Fundos.Origem />
      <Fundos.Destino />
      <Fundos.Footer />
    </>
  );
};
