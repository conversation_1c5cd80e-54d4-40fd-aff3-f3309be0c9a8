import { IHandleReponseResult } from '@cvp/componentes-posvenda';

export interface IConsultarDetalheCalculoPayload {
  codigoCertificado: string;
  numeroResgate: string;
}

export interface IConsultarDetalheCalculoLista {
  data: string;
  valorPrincipal: string;
  rendimentos: string;
  saldoAporte: string;
  valorSolicitado: string;
  saldoPrincipal: string;
  rendimento: string;
  valorCorrecao: string;
  carregamentoSaida: string;
  baseIrrf: string;
  aliquotaIrrf: string;
  valorIrrf: string;
  taxaSaida: string;
  valorLiquido: string;
  numeroResgate: string;
  codigoConta: string;
  id: string;
  codigoEmpresa: string;
  codigoFundo: string;
  descricaoFundo: string;
}

export interface IConsultarDetalheCalculoResponse {
  detalhes: IConsultarDetalheCalculoLista[];
  totalValorPrincipalAporte: string;
  totalRendimentosAporte: string;
  totalSaldoAporte: string;
  totalValorSolicitado: string;
  totalSaldoPrincipal: string;
  totalRendimento: string;
  totalValorCorrecao: string;
  totalCarregamentoSaida: string;
  totalBaseIrrf: string;
  totalAliquotaIrrf: string;
  totalValorIrrf: string;
  totalTaxaSaida: string;
  totalValorLiquido: string;
}

export interface IUseConsultarDetalheCalculoReturn {
  dadosConsultaDetalheCalculo: IConsultarDetalheCalculoResponse;
  isLoadingConsultaDetalheCalculo: boolean;
  consultarDetalheCalculo: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<IConsultarDetalheCalculoResponse> | undefined
  >;
}
