import {
  BOTOES,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontainer,
  DadosModalVerificarAporte,
  Dialog,
  EEtapasAporte,
  getTernaryResult,
  LoadingSpinner,
  MODAL_APORTE,
  Text,
  useModalVerificarCamposAporte,
  ValidacaoAporte,
} from '@src/features/financeiro/aporte/exports';

const ModalVerificarCamposAporte: React.FC = () => {
  const {
    formik,
    disabledForm,
    certificado,
    validarCamposAporteLoading,

    etapa,
    filtroTabelaFundos,
    handleProximaEtapa,
    setEtapa,
  } = useModalVerificarCamposAporte();

  return (
    <Dialog
      open={etapa === EEtapasAporte.ModalVerificarCamposAporte}
      onOpenChange={() => setEtapa(EEtapasAporte.FundosDistribuicaoAporte)}
    >
      <Dialog.Content>
        <Dialog.Header variant="highlight">
          <Text variant="text-big-400" fontColor="content-neutral-01">
            {MODAL_APORTE.cabecalho}
          </Text>
        </Dialog.Header>

        <Dialog.Body>
          <DadosModalVerificarAporte
            filtroTabelaFundos={filtroTabelaFundos}
            dadosCertificado={certificado}
          />

          <ValidacaoAporte formik={formik} />
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.Cancel asChild>
            <Button
              disabled={validarCamposAporteLoading}
              onClick={() => setEtapa(EEtapasAporte.FundosDistribuicaoAporte)}
              variant="secondary-outlined"
            >
              <ButtonContainer>{BOTOES.cancelar}</ButtonContainer>
            </Button>
          </Dialog.Cancel>

          <Button
            disabled={disabledForm}
            onClick={handleProximaEtapa}
            variant="primary"
            size="standard"
          >
            <ButtonContainer>
              {getTernaryResult(
                validarCamposAporteLoading,
                <LoadingSpinner />,
                <p>{BOTOES.confirmar}</p>,
              )}
            </ButtonContainer>
          </Button>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog>
  );
};

export default ModalVerificarCamposAporte;
