import { IConsultarDetalhesDaAliquotaFactoryRetorno } from '@src/features/financeiro/resgate/exports';

export const ALIQUOTA: Record<string, string> = {
  TIPO_REGIME_REGRESSIVO: 'R',
  TIPO_REGIME_PROGRESSIVO: 'P',
  TIPO_RESGATE_TOTAL: 'T',
  TIPO_RESGATE_PARCIAL: 'P',
  TRIBUTACAO_REGRESSIVA: 'TR',
  TRIBUTACAO_PROGRESSIVA: 'TP',
  PROGRESSIVO: 'Alíquota Progressiva',
  REGRESSIVO: 'Alíquota Regressiva',
  PROGRESSIVO_SHORT: 'Progressiva',
  REGRESSIVO_SHORT: 'Regressiva',
  TEXTO_CONFIRMAR: 'Confirmar',
  TEXTO_CANCELAR: 'Cancelar',
  TEXTO_PROGRESSIVO: 'Progressivo',
  TEXTO_REGRESSIVO: 'Regressivo',
  TEXTO_FORMA_PAGAMENTO: 'Conta',
  TEXTO_CONFIRMAR_MUDANCA_ALIQUOTA:
    'A alíquota escolhida não poderá mais ser alterada, sendo assim, definitiva. Tem certeza da escolha do modelo tributário?',
};

export const DADOS_ALIQUOTA_DEFAULT: IConsultarDetalhesDaAliquotaFactoryRetorno =
  {
    calculo: undefined,
    resumo: undefined,
    detalhado: undefined,
  };

export const RESUMO_ALIQUOTA_LABELS = {
  SALDO_EM: (dataFormatada: string): string => `Saldo em ${dataFormatada}:`,
  VALOR_BRUTO_RESGATE: 'Valor bruto a resgatar:',
  VALOR_IR: 'Valor de IR:',
  TAXA_CARREGAMENTO_SAIDA: 'Taxa de carregamento na saída:',
  VALOR_LIQUIDO: 'Valor líquido desejado:',
  SAIDA_APOS_RESGATE: 'Saída após o resgate:',
  MASCARA: {
    MONETARIO: 'money',
  },
};

export const DETALHADO_ALIQUOTA_LABELS = {
  NOME: 'Nome:',
  CERTIFICADO: 'Certificado:',
  PRODUTO_MODALIDADE: 'Produto e Modalidade:',
  DATA_HORA_SIMULACAO: 'Data e hora da simulação:',
  SALDO_EM: (data: string): string => `Saldo em ${data}:`,
  VALOR_NOMINAL_ESTIMADO: 'Valor nominal estimado:',
  VALOR_SIMULADO: 'Valor simulado:',
  VALOR_IR_ESTIMADO: 'Valor do IR estimado:',
  VALOR_LIQUIDO_ESTIMADO: 'Valor líquido estimado:',
  TAXA_CARREGAMENTO_ESTIMADA: 'Taxa de carregamento na saída estimada:',
  VALOR_MAXIMO_PERMITIDO: 'Valor máximo permitido:',
  VALOR_BLOQUEADO: 'Valor bloqueado:',
  VALOR_PADRAO_VAZIO: '0',
  MASCARA: {
    MONETARIO: 'money',
  },
};

export const DESCRICAO_ALIQUOTA_MAP: Record<string, string> = {
  [ALIQUOTA.TIPO_REGIME_PROGRESSIVO]: ALIQUOTA.PROGRESSIVO,
  [ALIQUOTA.TIPO_REGIME_REGRESSIVO]: ALIQUOTA.REGRESSIVO,
};
