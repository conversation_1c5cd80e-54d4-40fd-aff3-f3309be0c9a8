import { SelectItem } from '@cvp/design-system-caixa/dist/atoms/select/ISelect.types';
import { checkIfSomeItemsAreTrue } from '@cvp/utils';
import {
  indexMesAtual,
  anoAtual,
  retornaArrayUltimos5A<PERSON>,
  selecionarChavesObj,
} from '../utils';
import {
  SEGUNDO_SELECT_DECLARACAO_QUITACAO_ANUAL_LABEL,
  SEGUNDO_SELECT_DEFAULT_LABEL,
  SEGUNDO_SELECT_EXTRATO_DETALHADO_LABEL,
} from '../constants/imprDocModalTextos';
import { IModalImprDocState } from '../types/modalImprDocTypes';

export const valuesSelectPrimeiraOpcao = {
  EXTRATO_DETALHADO: 'EXTRATO_DETALHADO',
  EXTRATO_RENTABILIDADE_COTAS: 'EXTRATO_RENTABILIDADE_COTAS',
  DECLARACAO_QUITACAO_ANUAL: 'DECLARACAO_QUITACAO_ANUAL',
  IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR: 'IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR',
  IMPOSTO_RENDA_INFORME_RENDIMENTOS: 'IMPOSTO_RENDA_INFORME_RENDIMENTOS',
  IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS:
    'IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS',
  EMISSAO_CERTIFICADO: 'EMISSAO_CERTIFICADO',
};

export const valuesSelectSegundaOpcaoExtratoDetalhado = {
  MES_ATUAL: String(indexMesAtual),
  MES_ANTERIOR: String(indexMesAtual - 1),
  ANO_ATUAL: String(anoAtual),
  ANO_ANTERIOR: String(anoAtual - 1),
  PERIODO_PERSONALIZADO: 'PERIODO_PERSONALIZADO',
};

export const valuesSelectSegundaOpcaoExtratoRentabilidadeCotas = {
  ULTIMOS_7_DIAS: '7',
  ULTIMOS_15_DIAS: '15',
  ULTIMOS_30_DIAS: '30',
  ULTIMOS_60_DIAS: '60',
  ULTIMOS_90_DIAS: '90',
  CONSULTA_PERSONALIZADA: 'CONSULTA_PERSONALIZADA',
};

export const OpcoesPrimeiroSelect = [
  {
    text: 'Extrato detalhado',
    value: valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO,
  },
  {
    text: 'Extrato de Rentabilidade e Cotas',
    value: valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS,
  },
  {
    text: 'Declaração de Quitação Anual',
    value: valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL,
  },
  {
    text: 'Imposto de Renda - Guia de Preenchimento do IR',
    value: valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_GUIA_PREENCHIMENTO_IR,
  },
  {
    text: 'Imposto de Renda - Informe de Rendimentos',
    value: valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS,
  },
  {
    text: 'Imposto de Renda - Informe de Rendimento(s) de Beneficiário(s)',
    value:
      valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
  },
  {
    text: 'Emissão de Certificado',
    value: valuesSelectPrimeiraOpcao.EMISSAO_CERTIFICADO,
  },
];

export const getOpcoesSegundoSelect = (
  selectItemPrimeiraOpcao: SelectItem,
): SelectItem[] => {
  if (
    checkIfSomeItemsAreTrue([
      selectItemPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL,
      selectItemPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS,
      selectItemPrimeiraOpcao.value ===
        valuesSelectPrimeiraOpcao.IMPOSTO_RENDA_INFORME_RENDIMENTOS_BENEFICIARIOS,
    ])
  ) {
    return retornaArrayUltimos5Anos().map(ano => ({ text: ano, value: ano }));
  }

  if (
    selectItemPrimeiraOpcao.value ===
    valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO
  ) {
    return [
      {
        text: 'Mês atual',
        value: valuesSelectSegundaOpcaoExtratoDetalhado.MES_ATUAL,
      },
      {
        text: 'Mês anterior',
        value: valuesSelectSegundaOpcaoExtratoDetalhado.MES_ANTERIOR,
      },
      {
        text: 'Ano atual',
        value: valuesSelectSegundaOpcaoExtratoDetalhado.ANO_ATUAL,
      },
      {
        text: 'Ano anterior',
        value: valuesSelectSegundaOpcaoExtratoDetalhado.ANO_ANTERIOR,
      },
      {
        text: 'Período personalizado',
        value: valuesSelectSegundaOpcaoExtratoDetalhado.PERIODO_PERSONALIZADO,
      },
    ];
  }

  if (
    selectItemPrimeiraOpcao.value ===
    valuesSelectPrimeiraOpcao.EXTRATO_RENTABILIDADE_COTAS
  ) {
    return [
      {
        text: 'Últimos 7 dias',
        value: valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.ULTIMOS_7_DIAS,
      },
      {
        text: 'Últimos 15 dias',
        value:
          valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.ULTIMOS_15_DIAS,
      },
      {
        text: 'Últimos 30 dias',
        value:
          valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.ULTIMOS_30_DIAS,
      },
      {
        text: 'Últimos 60 dias',
        value:
          valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.ULTIMOS_60_DIAS,
      },
      {
        text: 'Últimos 90 dias',
        value:
          valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.ULTIMOS_90_DIAS,
      },
      {
        text: 'Consulta personalizada',
        value:
          valuesSelectSegundaOpcaoExtratoRentabilidadeCotas.CONSULTA_PERSONALIZADA,
      },
    ];
  }

  return [];
};

export const getSelectLabelSecond = (
  modalImprDocState: IModalImprDocState,
): string => {
  if (
    modalImprDocState.selectPrimeiraOpcao.value ===
    valuesSelectPrimeiraOpcao.EXTRATO_DETALHADO
  )
    return SEGUNDO_SELECT_EXTRATO_DETALHADO_LABEL;

  if (
    modalImprDocState.selectPrimeiraOpcao.value ===
    valuesSelectPrimeiraOpcao.DECLARACAO_QUITACAO_ANUAL
  )
    return SEGUNDO_SELECT_DECLARACAO_QUITACAO_ANUAL_LABEL;

  return SEGUNDO_SELECT_DEFAULT_LABEL;
};

export const modalImprDocInitialState: IModalImprDocState = {
  isModalImprDocOpen: false,
  optionsPrimeiraOpcao: OpcoesPrimeiroSelect,
  optionsSegundaOpcao: [],
  optionsTerceiraOpcao: [],
  selectPrimeiraOpcao: { text: '', value: '' },
  selectSegundaOpcao: { text: '', value: '' },
  selectTerceiraOpcao: { text: '', value: '' },
  selectDataInicio: { text: '', value: '' },
  selectDataFim: { text: '', value: '' },
  mensagemErroSelectDataInicio: '',
  mensagemErroSelectDataFim: '',
  mensagemErroObterPdf: '',
};

export const resetarValoresAcimaPrimeiroSelect = selecionarChavesObj(
  modalImprDocInitialState,
  [
    'selectSegundaOpcao',
    'selectTerceiraOpcao',
    'selectDataInicio',
    'selectDataFim',
    'optionsSegundaOpcao',
    'optionsTerceiraOpcao',
    'mensagemErroSelectDataInicio',
    'mensagemErroSelectDataFim',
    'mensagemErroObterPdf',
  ],
);

export const resetarValoresAcimaSegundoSelect = selecionarChavesObj(
  modalImprDocInitialState,
  [
    'selectTerceiraOpcao',
    'selectDataInicio',
    'selectDataFim',
    'optionsTerceiraOpcao',
    'mensagemErroSelectDataInicio',
    'mensagemErroSelectDataFim',
    'mensagemErroObterPdf',
  ],
);
