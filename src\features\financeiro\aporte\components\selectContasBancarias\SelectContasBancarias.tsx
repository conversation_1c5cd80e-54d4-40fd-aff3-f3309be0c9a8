import {
  CONTA_DEBITO,
  GridColumnContainer,
  GridItem,
  ITextoDadosBancarios,
  Select,
  SelectItem,
  Text,
} from '@src/features/financeiro/aporte/exports';

interface ISelectContasBancarias {
  dadosBancarios: ITextoDadosBancarios[];
  onChange: (option: SelectItem[]) => void;
}

const SelectContasBancarias: React.FC<ISelectContasBancarias> = ({
  dadosBancarios,
  onChange,
}) => {
  return (
    <GridColumnContainer margin="0px">
      <Text
        variant="text-big-400"
        fontColor="content-neutral-06"
        marginBottom="30px"
        marginTop="24px"
      >
        {CONTA_DEBITO.titulo}
      </Text>

      <GridItem xs="1/3">
        <Text fontColor="content-neutral-05" variant="text-large-600">
          {CONTA_DEBITO.conta}
        </Text>

        <Select
          errorProps={{
            children: '',
            show: false,
          }}
          onChange={onChange}
          options={dadosBancarios.map(item => ({
            text: item.text,
            value: item.id,
          }))}
          placeholder={CONTA_DEBITO.label}
          size="large"
          sizeWidth={undefined}
          textVariant="text-small-400"
          noWrap
          variant="box-classic"
        />
      </GridItem>
    </GridColumnContainer>
  );
};

export default SelectContasBancarias;
