import * as AtivacaoSuspensao from '../../exports';

interface ITabelaContribuicoesProps {
  contribuicoes: AtivacaoSuspensao.IContribuicaoItem[];
  onToggleContribuicao: (index: number, novoStatus: boolean) => void;
}

export const TabelaContribuicoes: React.FC<ITabelaContribuicoesProps> = ({
  contribuicoes,
  onToggleContribuicao,
}) => {
  const reservas = contribuicoes.filter(c => c.tipo === 'reserva');
  const cuidadoExtra = contribuicoes.filter(c => c.tipo === 'cuidadoExtra');

  const totalContribuicao = contribuicoes.reduce(
    (total, contrib) => total + contrib.valorContribuicao,
    0,
  );

  return (
    <AtivacaoSuspensao.Grid justify="space-between">
      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Alert
          variant="information-01"
          icon={<AtivacaoSuspensao.IconInfoRound size="medium" />}
        >
          <AtivacaoSuspensao.Text variant="text-standard-600">
            {AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.ALERT.TITULO}
          </AtivacaoSuspensao.Text>
          <AtivacaoSuspensao.Text variant="text-standard-400">
            {
              AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.ALERT
                .RESERVA_DESCRICAO
            }
          </AtivacaoSuspensao.Text>
          <AtivacaoSuspensao.Text variant="text-standard-400">
            {
              AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.ALERT
                .CUIDADO_EXTRA_DESCRICAO
            }
          </AtivacaoSuspensao.Text>
        </AtivacaoSuspensao.Alert>
      </AtivacaoSuspensao.GridItem>
      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Text
          variant="text-large-700"
          fontColor="brand-primary-05"
          marginBottom="16px"
        >
          {AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.SECOES.RESERVA}
        </AtivacaoSuspensao.Text>

        <AtivacaoSuspensao.For each={reservas}>
          {(reserva, index) => (
            <AtivacaoSuspensao.ContribuicaoCard key={index}>
              <AtivacaoSuspensao.Text
                variant="text-standard-600"
                marginBottom="8px"
              >
                {reserva.nome}
              </AtivacaoSuspensao.Text>

              <AtivacaoSuspensao.Grid
                alignitem="center"
                justify="space-between"
              >
                <AtivacaoSuspensao.GridItem xs="auto">
                  <AtivacaoSuspensao.Text variant="text-standard-400">
                    {
                      AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.LABELS
                        .VALOR_CONTRIBUICAO
                    }{' '}
                    {AtivacaoSuspensao.formatarValorPadraoBrasileiro(
                      reserva.valorContribuicao,
                    )}
                  </AtivacaoSuspensao.Text>
                </AtivacaoSuspensao.GridItem>

                <AtivacaoSuspensao.GridItem xs="auto">
                  <AtivacaoSuspensao.Text variant="text-standard-400">
                    {
                      AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.LABELS
                        .SALDO_ACUMULADO
                    }{' '}
                    {AtivacaoSuspensao.formatarValorPadraoBrasileiro(
                      reserva.saldoAcumulado || 0,
                    )}
                  </AtivacaoSuspensao.Text>
                </AtivacaoSuspensao.GridItem>

                <AtivacaoSuspensao.GridItem
                  xs="auto"
                  style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
                >
                  <AtivacaoSuspensao.Switch
                    variant="higlight"
                    checked={reserva.ativo}
                    onCheckedChange={() =>
                      onToggleContribuicao(
                        contribuicoes.findIndex(c => c === reserva),
                        !reserva.ativo,
                      )
                    }
                  />
                  <AtivacaoSuspensao.Text variant="text-standard-400">
                    {reserva.ativo ? 'Ativo' : 'Inativo'}
                  </AtivacaoSuspensao.Text>
                </AtivacaoSuspensao.GridItem>
              </AtivacaoSuspensao.Grid>
            </AtivacaoSuspensao.ContribuicaoCard>
          )}
        </AtivacaoSuspensao.For>
      </AtivacaoSuspensao.GridItem>

      <AtivacaoSuspensao.ConditionalRenderer
        condition={cuidadoExtra.length > 0}
      >
        <AtivacaoSuspensao.GridItem xs="1">
          <AtivacaoSuspensao.Separator
            orientation="horizontal"
            size="4"
            bgColor="gray-04"
          />
        </AtivacaoSuspensao.GridItem>
        <AtivacaoSuspensao.GridItem xs="1">
          <AtivacaoSuspensao.Separator
            orientation="horizontal"
            size="4"
            bgColor="gray-04"
          />
        </AtivacaoSuspensao.GridItem>

        <AtivacaoSuspensao.GridItem xs="1">
          <AtivacaoSuspensao.Text
            variant="text-large-700"
            fontColor="brand-primary-05"
            marginBottom="16px"
          >
            {
              AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.SECOES
                .CUIDADO_EXTRA
            }
          </AtivacaoSuspensao.Text>

          <AtivacaoSuspensao.For each={cuidadoExtra}>
            {(cuidado, index) => (
              <AtivacaoSuspensao.ContribuicaoCard key={index}>
                <AtivacaoSuspensao.Text
                  variant="text-standard-600"
                  marginBottom="8px"
                >
                  {cuidado.nome}
                </AtivacaoSuspensao.Text>

                <AtivacaoSuspensao.Grid
                  alignitem="center"
                  justify="space-between"
                >
                  <AtivacaoSuspensao.GridItem xs="auto">
                    <AtivacaoSuspensao.Text variant="text-standard-400">
                      {
                        AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.LABELS
                          .PRAZO_RECEBIMENTO
                      }{' '}
                      {cuidado.prazoRecebimento}
                    </AtivacaoSuspensao.Text>
                  </AtivacaoSuspensao.GridItem>

                  <AtivacaoSuspensao.GridItem xs="auto">
                    <AtivacaoSuspensao.Text variant="text-standard-400">
                      {
                        AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.LABELS
                          .VALOR_CONTRIBUICAO
                      }{' '}
                      {AtivacaoSuspensao.formatarValorPadraoBrasileiro(
                        cuidado.valorContribuicao,
                      )}
                    </AtivacaoSuspensao.Text>
                  </AtivacaoSuspensao.GridItem>

                  <AtivacaoSuspensao.GridItem xs="auto">
                    <AtivacaoSuspensao.Text variant="text-standard-400">
                      {
                        AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.LABELS
                          .VALOR_IDENIZACAO
                      }{' '}
                      {AtivacaoSuspensao.formatarValorPadraoBrasileiro(
                        cuidado.valorIdentizacao || 0,
                      )}
                    </AtivacaoSuspensao.Text>
                  </AtivacaoSuspensao.GridItem>

                  <AtivacaoSuspensao.GridItem
                    xs="auto"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <AtivacaoSuspensao.Switch
                      variant="higlight"
                      checked={cuidado.ativo}
                      onCheckedChange={() =>
                        onToggleContribuicao(
                          contribuicoes.findIndex(c => c === cuidado),
                          !cuidado.ativo,
                        )
                      }
                    />
                    <AtivacaoSuspensao.Text variant="text-standard-400">
                      {cuidado.ativo ? 'Ativo' : 'Inativo'}
                    </AtivacaoSuspensao.Text>
                  </AtivacaoSuspensao.GridItem>
                </AtivacaoSuspensao.Grid>
              </AtivacaoSuspensao.ContribuicaoCard>
            )}
          </AtivacaoSuspensao.For>
        </AtivacaoSuspensao.GridItem>
      </AtivacaoSuspensao.ConditionalRenderer>

      <AtivacaoSuspensao.GridItem xs="1">
        <AtivacaoSuspensao.Separator
          orientation="horizontal"
          size="4"
          bgColor="gray-04"
        />
        <AtivacaoSuspensao.TotalContainer>
          <AtivacaoSuspensao.Text variant="text-large-700">
            {
              AtivacaoSuspensao.ATIVACAO_SUSPENSAO_CONSTANTS.LABELS
                .TOTAL_CONTRIBUICAO
            }
          </AtivacaoSuspensao.Text>
          <AtivacaoSuspensao.Text
            variant="text-large-700"
            fontColor="brand-primary-06"
          >
            {AtivacaoSuspensao.formatarValorPadraoBrasileiro(totalContribuicao)}
          </AtivacaoSuspensao.Text>
        </AtivacaoSuspensao.TotalContainer>
      </AtivacaoSuspensao.GridItem>
    </AtivacaoSuspensao.Grid>
  );
};
