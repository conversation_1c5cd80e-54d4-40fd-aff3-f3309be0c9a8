import { useContext } from 'react';
import * as REQUEST_TYPES from '@src/features/financeiro/dadosPagamento/types/AlteracaoFormaDadosPagamentoRequest';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import { checkIfAllItemsAreTrue, getSessionItem } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

export const useAtualizarFormaPagamento = ({
  tipoContribuicao,
  canalId,
}: Partial<REQUEST_TYPES.IRequestAtualizarFormaPagamento>) => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpjSession = getSessionItem<string>('cpfCnpj') ?? '';

  const {
    response: responseAtualizacaoFormaPagamento,
    loading: loadingDadosAtualizacaoFormaPagamento,
    invocarApiGatewayCvpComToken: atualizarFormaPagamento,
    setResponse,
  } = useApiGatewayCvpInvoker<
    Partial<REQUEST_TYPES.IRequestAtualizarFormaPagamento>,
    undefined
  >(PECOS.AtualizarFormaPagamento, {
    data: {
      cpfCnpj: cpfCnpjSession,
      numeroCertificado: certificadoAtivo?.certificadoNumero,
      tipoContribuicao,
      canalId,
    },
    autoFetch: false,
  });

  const isSuccessAtualizarFormaPagamento = checkIfAllItemsAreTrue([
    !!responseAtualizacaoFormaPagamento?.sucessoBFF,
    !!responseAtualizacaoFormaPagamento?.sucessoGI,
  ]);

  return {
    responseAtualizacaoFormaPagamento,
    loadingDadosAtualizacaoFormaPagamento,
    atualizarFormaPagamento,
    isSuccessAtualizarFormaPagamento,
    setResponse,
  };
};
