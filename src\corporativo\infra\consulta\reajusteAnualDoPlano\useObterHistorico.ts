import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IObterHistoricoPayload,
  IResponseHistoricoAtualizacoes,
  IUseObterHistoricoAtualizacoes,
} from '@src/corporativo/types/consulta/IReajusteAnualPlano';
import { useContext } from 'react';

const DEFAULT_RETURN: IResponseHistoricoAtualizacoes[] = [];

export const useObterHistorico = (): IUseObterHistoricoAtualizacoes => {
  const cpf = String(getSessionItem('cpfCnpj'));
  const { certificadoAtivo } = useContext(PrevidenciaContext);

  const { loading, response } = useApiGatewayCvpInvoker<
    Partial<IObterHistoricoPayload>,
    { dados: IResponseHistoricoAtualizacoes[] }
  >(PECOS.ObterHistorico, {
    autoFetch: true,
    cacheKey: `${certificadoAtivo.certificadoNumero}_historico_atualizacoes`,
    cache: true,
    cacheTime: 60 * 60 * 1000,
    data: {
      cpf,
      numeroCertificado: certificadoAtivo.certificadoNumero,
    },
  });

  return {
    response: tryGetValueOrDefault([response?.entidade?.dados], DEFAULT_RETURN),
    loading,
  };
};
