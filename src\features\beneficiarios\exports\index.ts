export { useBlocker, type Blocker } from 'react-router-dom';

export {
  createContext,
  useContext,
  useReducer,
  useRef,
  useState,
  type PropsWithChildren,
  type ComponentProps,
} from 'react';
export {
  useFormikContext,
  Form,
  Formik,
  FastField,
  FieldArray,
  type FieldInputProps,
  type FieldProps,
  type FormikHelpers,
  type FormikContextType,
} from 'formik';

export {
  Button,
  ConditionalRenderer,
  DateField,
  Dialog,
  Grid,
  IconCheckCircleOutlinedRound,
  IconDeleteOutlinedRound,
  IconEditOutlinedSharp,
  IconInfoOutlined,
  IconInfoRound,
  IconWarningOutlinedRound,
  IconWarningSharp,
  InputText,
  LoadingSpinner,
  MultiAccordion,
  Select,
  TableEdit,
  Text,
  ToolTip,
} from '@cvp/design-system-caixa';

export { For, Match, SwitchCase } from '@cvp/componentes-posvenda';

export {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  cpfCnpjMask,
  formatarDataHoraAmigavel,
  getSessionItem,
  getTernaryResult,
  setSessionItem,
  tryGetValueOrDefault,
  validarCpf,
} from '@cvp/utils';

export { Add } from '@mui/icons-material';

export { useAlertaEdicao } from '@src/shared/hooks/useAlerta';
export { default as useValidarAssinatura } from '@src/shared/hooks/useValidarAssinatura';

export { default as Assinatura } from '@src/corporativo/components/Assinatura/Assinatura';
export { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';

export { default as AlertaPrevidencia } from '@src/corporativo/components/AlertaPrevidencia/AlertaPrevidencia';
export { default as AlertaTimeout } from '@src/corporativo/components/AlertaTimeout/AlertaTimeout';
export { default as useModificarBeneficiarios } from '@src/corporativo/infra/beneficiarios/useModificarBeneficiarios';
export { default as useObterBeneficiarios } from '@src/corporativo/infra/beneficiarios/useObterBeneficiarios';

export type { ICertificadoCoberturas } from '@src/corporativo/types/consultaCertificado/Response/ICertificadoCoberturasResponse';

export type { TColumn } from '@cvp/design-system-caixa/dist/atoms/Table/Table.types';
export type { IAlerta } from '@src/corporativo/types/alerta/IAlerta';
export type { IAssinaturaResponse } from '@src/corporativo/types/assinatura/IAssinaturaResponse';
export type {
  TAdicionarBeneficiario,
  TAlertaBeneficiarios,
  TAlterarBeneficiario,
  TAssinaturaBeneficiariosProps,
  TBeneficiariosFormData,
  TBotoesAcaoEditarBeneficiariosProps,
  TBotoesAcaoTabelaBeneficiariosProps,
  TCelulasEditaveis,
  TCelulaTabelaBeneficiario,
  TCelulaTabelaProps,
  TFormatterTableCellParams,
  TModalCancelarEdicaoBeneficiariosProps,
  TModalExcluirBeneficiarioProps,
  TModalNovoBeneficiario,
  TNovoBeneficiario,
  TRowData,
  TTableHeaderPorcentualProps,
  TTipoCelula,
  TUseInputCelulaBeneficiario,
  TUseModalNovoBeneficiario,
  TUseModificarBeneficiarios,
  TUseModificarBeneficiariosRequest,
  TUseObterBeneficiarios,
} from '@src/corporativo/types/beneficiarios';

export type { IBeneficiarios } from '@src/corporativo/types/consultaCertificado/Response/IBeneficiarios';
export type { IBeneficios } from '@src/corporativo/types/consultaCertificado/Response/IBeneficios';

export type { IAlertaIdentificado } from '@src/corporativo/context/AlertasContext';
export type { IBeneficiariosEdicao } from '@src/corporativo/types/beneficiarios';
export type { TCoberturaBeneficiariosSchema } from '../schema/coberturasSchema';
export type { TFormBeneficiarios } from '../types/TFormBeneficiarios';

export { LOADING } from '@src/shared/constants/api';

export {
  TIPO_CELULA,
  TIPOS_INPUT_CELULA,
  VOID_STRING,
} from '../constants/nomeCelulasBeneficiario';

export {
  ALERTA_CPF_EXISTENTE,
  ALERTA_CPF_INVALIDO,
  ALERTA_ERRO_EDICAO,
  ALERTA_HERDEIROS_LEGAIS,
  ALERTA_PORCENTAGEM_WARN,
  ALERTA_SUCESSO_EDICAO,
  TIPOS_ALERTA,
} from '../constants/alertasBeneficiarios';
export {
  ARRAY_VAZIO,
  BENEFICIARIOS_EXISTENTES_CAMPOS_EDITAVEIS,
  BENEFICIARIOS_NOVOS_CAMPOS_EDITAVEIS,
} from '../constants/camposBeneficiarios';
export { REGEX_REMOVER_LETRAS_MAIUSCULAS } from '../constants/regexRemoverLetras';

export { AMBIENTE_INTEGRADO_URL } from '../constants/ambiente';
export { TIPO_PARENTESCO, TIPOS_PARENTESCO } from '../constants/tipoParentesco';

export { colunasTabelaBeneficiarios } from '../factory/colunasTabelaBeneficiarios';
export { dadosBeneficiario } from '../factory/dadosBeneficiario';
export { montarCamposEditados } from '../factory/montarCamposEditados';

export { useModalNovoBeneficiario } from '../hooks/useModalNovoBeneficiario';

export { formatterTableCells } from '../utils/formatterTableCells';
export { validatePercentage } from '../utils/validatePercentage';
export {
  beneficiariosInitialState,
  beneficiariosReducer,
  EBeneficiariosActionKind,
  type TBeneficiariosActions,
  type TBeneficiariosState,
  criaBeneficiariosEstadoInicial,
} from '../hooks/beneficiariosReducer';

export {
  BeneficiariosContext,
  BeneficiariosDispatchContext,
  BeneficiariosProvider,
} from '../context/beneficiariosContext';

export { BENEFICIARIOS_CAMPOS_EDITAVEIS } from '../constants/beneficiariosCamposEditaveis';
export { HERDEIROS_LEGAIS } from '../constants/herdeirosLegais';
export { TEXTOS_BENEFICIARIOS } from '../constants/textosBeneficiarios';

export { useControlesAssinaturaBeneficiarios } from '../hooks/useControlesAssinaturaBeneficiarios';
export { useBeneficiariosForm } from '../hooks/useBeneficiariosForm';
export { useAccordionBeneficiarios } from '../hooks/useAccordionBeneficiarios';
export { InputsBeneficiarios } from '../factory/inputsBeneficiarios';

export * as S from '../styles/styles';

export { CelulaControleBeneficiarios } from '../views/CelulaControlesBeneficiario';
export { AcordionBeneficiarios } from '../views/AccordionBeneficiarios';
export { AssinaturaBeneficiarios } from '../views/AssinaturaBeneficiarios';
export { BotoesAcaoTabelaBeneficiarios } from '../views/BotoesAcaoTabelaBeneficiarios';
export { CelulaTabelaBeneficiario } from '../views/CelulaTabelaBeneficiario';
export { Cobertura } from '../views/Cobertura';
export { InputCelulaBeneficiario } from '../views/InputCelulaBeneficiario';
export { ModalCancelarEdicaoBeneficiarios } from '../views/ModalCancelarEdicaoBeneficiarios';
export { ModalExcluirBeneficiario } from '../views/ModalExcluirBeneficiario';
export { ModalNovoBeneficiario } from '../views/ModalNovoBeneficiario';
export { TableHeaderPorcentual } from '../views/TableHeaderPorcentual';
export { ContainerCoberturas } from '../views/ContainerCoberturas';
