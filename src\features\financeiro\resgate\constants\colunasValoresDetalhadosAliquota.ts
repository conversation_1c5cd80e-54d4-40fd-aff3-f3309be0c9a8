import {
  IMontarDadosTabelaDetalheCalculoFactoryRetorno,
  TableColumn,
} from '@src/features/financeiro/resgate/exports';

export const COLUNAS_VALORES_DETALHADOS_ALIQUOTA: TableColumn<IMontarDadosTabelaDetalheCalculoFactoryRetorno>[] =
  [
    {
      name: 'Data de contribuição',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.data,
      minWidth: '140px',
      center: true,
    },
    {
      name: 'Valor nominal',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.saldoPrincipal,
      minWidth: '160px',
      center: true,
    },
    {
      name: 'Rendimento',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.rendimento,
      minWidth: '160px',
      center: true,
    },
    {
      name: 'Valor de saída bruto',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.valorSolicitado,
      minWidth: '160px',
      center: true,
    },
    {
      name: 'Carregam. saída',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.carregamentoSaida,
      center: true,
    },
    {
      name: 'Valor base IRRF',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.baseIrrf,
      minWidth: '160px',
      center: true,
    },
    {
      name: 'Alíquota IRRF (%)',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.aliquotaIrrf,
      center: true,
    },
    {
      name: 'Valor do IRRF',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.valorIrrf,
      minWidth: '160px',
      center: true,
    },
    {
      name: 'Taxa de saída',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.taxaSaida,
      center: true,
    },
    {
      name: 'Valor saída liquida',
      selector: (row: IMontarDadosTabelaDetalheCalculoFactoryRetorno) =>
        row.valorLiquido,
      minWidth: '160px',
      center: true,
    },
  ];
