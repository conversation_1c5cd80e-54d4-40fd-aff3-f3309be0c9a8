/**
 * Utilitários para testar o sistema de recuperação de erros
 * Use apenas em desenvolvimento
 */

export const ErrorRecoveryTest = {
  /**
   * Simula o erro de tema que você está enfrentando
   */
  simulateThemeError() {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Teste de erro só disponível em desenvolvimento');
      return;
    }
    
    console.log('🧪 Simulando erro de tema...');
    throw new Error("Cannot read properties of undefined (reading '600')");
  },
  
  /**
   * Simula erro genérico de propriedade
   */
  simulatePropertyError() {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Teste de erro só disponível em desenvolvimento');
      return;
    }
    
    console.log('🧪 Simulando erro de propriedade...');
    throw new Error("Cannot read properties of undefined (reading 'someProperty')");
  },
  
  /**
   * Força limpeza manual de erros
   */
  forceClearErrors() {
    console.log('🧹 Forçando limpeza de erros...');
    
    // Dispara evento de recuperação
    const event = new CustomEvent('theme-error-recovery');
    window.dispatchEvent(event);
    
    // Remove overlays
    const overlays = document.querySelectorAll(
      '[data-react-error-overlay], [data-webpack-overlay], .react-error-overlay, #webpack-dev-server-client-overlay'
    );
    
    overlays.forEach(overlay => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
        console.log('🧹 Overlay removido');
      }
    });
    
    console.log('✅ Limpeza manual concluída');
  },
  
  /**
   * Testa o sistema completo
   */
  testRecoverySystem() {
    console.log('🧪 Testando sistema de recuperação...');
    console.log('1. Simulando erro em 2 segundos...');
    
    setTimeout(() => {
      try {
        this.simulateThemeError();
      } catch (error) {
        console.log('2. Erro capturado, sistema deve tentar recuperação...');
      }
    }, 2000);
  }
};

// Expõe globalmente para uso no console
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).ErrorRecoveryTest = ErrorRecoveryTest;
  console.log('🧪 ErrorRecoveryTest disponível no console');
  console.log('💡 Use: ErrorRecoveryTest.testRecoverySystem()');
  console.log('💡 Use: ErrorRecoveryTest.forceClearErrors()');
}
