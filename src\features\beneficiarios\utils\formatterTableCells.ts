import {
  formatarDataHoraAmigavel,
  getTernaryResult,
  TFormatterTableCellParams,
  TIPO_PARENTESCO,
  TIPOS_INPUT_CELULA,
} from '../exports';

const VOID_STRING = '';

export const formatterTableCells = ({
  tipo,
  val,
}: TFormatterTableCellParams): string | number => {
  switch (tipo) {
    case TIPOS_INPUT_CELULA.DATA:
      return formatarDataHoraAmigavel(val as string, false);
    case TIPOS_INPUT_CELULA.SELECT:
      return TIPO_PARENTESCO[val as keyof typeof TIPO_PARENTESCO];
    case TIPOS_INPUT_CELULA.PORCENTAGEM:
      return getTernaryResult(val === VOID_STRING, VOID_STRING, `${val}%`);
    default:
      return val;
  }
};
