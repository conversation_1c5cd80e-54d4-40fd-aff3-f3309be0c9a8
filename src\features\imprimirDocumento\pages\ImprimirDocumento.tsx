import { GerarDocumento, Match, SwitchCase } from '@cvp/componentes-posvenda';
import { LoadingSpinner } from '@cvp/design-system-caixa';
import { tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/features/layoutPrevidencia/exports';
import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { useEnviarEmail } from '../exports';

const ImprimirDocumento: React.FC = () => {
  const navigate = useNavigate();
  const { impressao, parametrosScroll } = useContext(PrevidenciaContext);

  const { loading, result, enviarEmail } = useEnviarEmail({
    tipoEmail: impressao?.tipoEmail,
    parametrosEnvio: impressao.parametrosEnvio,
  });

  const voltar = () => {
    navigate(
      `${parametrosScroll.rota}?scroll=${parametrosScroll.valorScroll}`,
      {
        replace: true,
      },
    );
  };

  return (
    <SwitchCase fallback={undefined}>
      <Match when={!impressao?.base64?.length}>
        <LoadingSpinner size="medium">Carregando...</LoadingSpinner>
      </Match>

      <Match when={!!impressao?.base64?.length}>
        <GerarDocumento
          titulo="Previdência"
          tipoDocumento={tryGetValueOrDefault([impressao?.tipoDocumento], '')}
          base64File={tryGetValueOrDefault([impressao?.base64], [''])}
          enviandoEmail={loading}
          alerta={{
            mensagem: result?.mensagem,
            sucesso: result?.sucesso,
          }}
          onVoltar={voltar}
          onEnviarEmail={enviarEmail}
        />
      </Match>
    </SwitchCase>
  );
};

export default ImprimirDocumento;
