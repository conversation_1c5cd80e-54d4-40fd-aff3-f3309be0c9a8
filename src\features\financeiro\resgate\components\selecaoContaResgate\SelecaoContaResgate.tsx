import * as Resgate from '@src/features/financeiro/resgate/exports';

export const SelecaoContaResgate = ({
  onChange,
}: Resgate.ISelecaoContaResgateProps): React.ReactElement => {
  const { resgateFeatureData } = Resgate.useResgateContext();
  return (
    <Resgate.GridContaBancariaCustom>
      <Resgate.GridItem xs="1">
        <Resgate.Text
          variant="text-standard-600"
          fontColor="content-neutral-05"
        >
          Selecione a conta para crédito do resgate
        </Resgate.Text>
      </Resgate.GridItem>
      <Resgate.GridItem xs="1" lg="1/3">
        <div>
          <Resgate.Text
            variant="text-standard-600"
            fontColor="content-neutral-05"
          >
            Conta*
          </Resgate.Text>
          <Resgate.Select
            id="conta"
            onChange={onChange}
            options={Resgate.mapearSelectFormFactory({
              lista: resgateFeatureData?.listaContasExistentes,
              getText: conta => conta.text,
              getValue: conta => conta.value,
            })}
            placeholder="Selecione"
            size="standard"
            sizeWidth="standard"
            variant="box-classic"
          />
        </div>
      </Resgate.GridItem>
    </Resgate.GridContaBancariaCustom>
  );
};
