import { useContext } from 'react';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import {
  IConsultarDetalheCalculoPayload,
  IConsultarDetalheCalculoResponse,
  IUseConsultarDetalheCalculoReturn,
} from '@src/corporativo/types/financeiro/resgate/IConsultarDetalheCalculo';

export const useConsultarDetalheCalculo =
  (): IUseConsultarDetalheCalculoReturn => {
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const codigoCertificado = tryGetValueOrDefault(
      [certificadoAtivo?.certificadoNumero],
      '',
    );

    const {
      response: dadosConsultaDetalheCalculo,
      loading: isLoadingConsultaDetalheCalculo,
      invocarApiGatewayCvpComToken: consultarDetalheCalculo,
    } = useApiGatewayCvpInvoker<
      Partial<IConsultarDetalheCalculoPayload>,
      IConsultarDetalheCalculoResponse
    >(PECOS.ConsultarDetalheCalculo, {
      data: { codigoCertificado },
    });

    return {
      dadosConsultaDetalheCalculo: tryGetValueOrDefault(
        [dadosConsultaDetalheCalculo?.entidade],
        {} as IConsultarDetalheCalculoResponse,
      ),
      isLoadingConsultaDetalheCalculo,
      consultarDetalheCalculo,
    };
  };
