export interface IReservasDestinoRequest {
  codFundo: string;
  codObjetivo: number;
  codReserva: string;
  vlrSaldo: number;
}

export type TDefinirReservaEntidade = {
  msgErroExcessao: string;
  codMensagem: string;
  desMensagem: string;
  fundosContrib: TResponseFundosContrib[];
  vlrContribRegular: number;
};

export type TResponseFundosContrib = {
  codFundo: string;
  desFundo: string;
  pctDistribuicao: number;
  contribuicaoAtual: number;
  numCnpj: string;
  vlrSaldo: number;
  pctRentabUltimoAno: number;
  desPerfilFundo: string;
  codPerfilFundo: string;
  vlrMinimo: number;
};
