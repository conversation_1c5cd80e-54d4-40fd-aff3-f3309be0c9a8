import * as DadosParticipante from '../exports';

type TUseCampoDadosParticipante = (name: string) => {
  formik: DadosParticipante.FormikContextType<DadosParticipante.TFormDadosParticipanteSchema>;
  handleChangeCep: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  fieldProps: DadosParticipante.FieldInputProps<string>;
};

export const useCampoDadosParticipante: TUseCampoDadosParticipante = (
  name: string,
) => {
  const formik =
    DadosParticipante.useFormikContext<DadosParticipante.TFormDadosParticipanteSchema>();
  const dispatch = DadosParticipante.React.useContext(
    DadosParticipante.DadosParticipanteDispatchContext,
  );

  const { buscarEnderecoCep } = DadosParticipante.useBuscarEnderecoCep();
  const fieldProps = formik.getFieldProps<string>(name);

  const handleChangeCep = async (e: React.ChangeEvent<HTMLInputElement>) => {
    fieldProps.onChange(e);

    if (e.target.value.length === 9) {
      const response = await buscarEnderecoCep({
        cep: DadosParticipante.Utils.cep.unmask(e.target.value),
      });

      if (!response?.entidade) return;
      const { entidade } = response;
      dispatch({
        type: DadosParticipante.EDadosParticipanteActionKind.CONSULTAR_CEP,
        novoEndereco: entidade,
      });
      formik.setValues({
        ...formik.values,
        cep: e.target.value,
        bairro: entidade.bairro,
        cidade: entidade.cidade,
        endereco: entidade.endereco,
        uf: entidade.idEstado,
        complemento: DadosParticipante.Utils.tryGetValueOrDefault(
          [entidade.complemento],
          '',
        ),
        numero: DadosParticipante.Utils.tryGetValueOrDefault(
          [entidade.numero],
          '',
        ),
      });
    }
  };
  return {
    formik,
    handleChangeCep,
    fieldProps,
  };
};
