import {
  ALERTA_ERRO_EDICAO,
  ALERTA_PORCENTAGEM_WARN,
  ALERTA_SUCESSO_EDICAO,
  BeneficiariosDispatchContext,
  EBeneficiariosActionKind,
  FormikHelpers,
  TAdicionarBeneficiario,
  TAlterarBeneficiario,
  TFormBeneficiarios,
  useAlertaEdicao,
  useContext,
  useModificarBeneficiarios,
} from '../exports';

export const useAccordionBeneficiarios = () => {
  const { alterarAlerta } = useAlertaEdicao();
  const { invocarApiGatewayCvpComToken } = useModificarBeneficiarios();

  const dispatch = useContext(BeneficiariosDispatchContext);

  const submitBeneficiarios = async (
    values: TFormBeneficiarios,
    formikHelpers: FormikHelpers<TFormBeneficiarios>,
  ) => {
    try {
      const coberturasEditadas = Object.keys(values)
        .map(coberturaId => {
          const cobertura = values[coberturaId];
          if (!values[coberturaId].editando) return null;
          return {
            coberturaId,
            beneficiarios: cobertura.beneficiarios,
            planoId: cobertura.planoId,
            error:
              cobertura.beneficiarios.reduce(
                (acc, beneficiario) => acc + Number(beneficiario.porcentagem),
                0,
              ) !== 100,
          };
        })
        .filter(cobertura => cobertura !== null);
      const coberturasComErro = coberturasEditadas.filter(
        cobertura => cobertura?.error === true,
      );

      if (coberturasComErro.length) {
        coberturasComErro.forEach(cobertura => {
          alterarAlerta({
            identificador: `beneficiarios-cobertura-${cobertura.coberturaId}`,
            ...ALERTA_PORCENTAGEM_WARN,
            duration: 7000,
          });
        });
        return;
      }

      const coberturasASeremSalvas = coberturasEditadas.map(cobertura => {
        const adicionarBeneficiarios: TAdicionarBeneficiario[] = [];
        const alterarBeneficiarios: TAlterarBeneficiario[] = [];
        cobertura.beneficiarios.forEach(beneficiario => {
          if (beneficiario.estado === 'excluido') return;
          if (beneficiario.estado === 'novo') {
            adicionarBeneficiarios.push({
              nomeBeneficiario: beneficiario.nomeBeneficiario,
              porcentagem: String(beneficiario.porcentagem),
              dataNascimento: beneficiario.dataNascimento,
              numCpf: beneficiario.numCpf,
              sexo: 'M',
              idParentesco: beneficiario.idParentesco,
            });
            return;
          }
          alterarBeneficiarios.push({
            idBeneficiario: beneficiario.idBeneficiario,
            porcentagem: String(beneficiario.porcentagem),
            idParentesco: beneficiario.idParentesco,
          });
        });
        return {
          planoId: cobertura.planoId,
          coberturaId: cobertura.coberturaId,
          adicionarBeneficiarios,
          alterarBeneficiarios,
        };
      });

      const response = await Promise.all(
        coberturasASeremSalvas.map(cobertura =>
          invocarApiGatewayCvpComToken({
            idPlano: cobertura.planoId,
            idBeneficio: cobertura.coberturaId,
            adicionarBeneficiarios: cobertura.adicionarBeneficiarios,
            alterarBeneficiarios: cobertura.alterarBeneficiarios,
          })
            .then(r => ({ ...r, coberturaId: cobertura.coberturaId }))
            .catch(r => ({ ...r, coberturaId: cobertura.coberturaId })),
        ),
      );

      response.forEach(resposta => {
        if (resposta.sucessoGI && resposta.sucessoBFF) {
          formikHelpers.setFieldValue(
            `${resposta.coberturaId}.editando`,
            false,
          );
        }
      });

      if (
        response
          .map(resposta => resposta.sucessoBFF && resposta.sucessoGI)
          .some(val => !val)
      ) {
        alterarAlerta(ALERTA_ERRO_EDICAO);
        dispatch({
          type: EBeneficiariosActionKind.CONFIRMAR_EDICAO_BENEFICIARIOS,
          value: true,
        });
        return;
      }

      alterarAlerta(ALERTA_SUCESSO_EDICAO);
      dispatch({
        type: EBeneficiariosActionKind.CANCELAR_EDICAO_BENEFICIARIOS,
      });
    } finally {
      formikHelpers.setSubmitting(false);
    }
  };

  return { submitBeneficiarios };
};
