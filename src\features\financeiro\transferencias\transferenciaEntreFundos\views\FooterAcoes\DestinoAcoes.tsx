import React from 'react';
import { Button, Grid, GridItem, Text } from '@cvp/design-system-caixa';
import { useTransferenciaContext } from '@src/corporativo/hooks/useTransferenciaContext';
import { tryGetMonetaryValueOrDefault, getTernaryResult } from '@cvp/utils';
import * as CONSTANTES from '@src/features/financeiro/transferencias/constants';
import { DestinoAcoesButton } from '@src/features/financeiro/transferencias/transferenciaEntreFundos/styles';
import AddCircleOutline from '@mui/icons-material/AddCircleOutline';

export const FundoDestinoAcoes = (): React.ReactElement => {
  const {
    setVerMais,
    verMais,
    distribuicaoValores,
    setModalProsseguir,
    setErro,
  } = useTransferenciaContext();

  return (
    <>
      <Grid
        justify="space-between"
        alignitem="center"
        style={{ height: 'auto' }}
      >
        <GridItem>
          <Text variant="text-standard-400">
            {CONSTANTES.SESSOES.FOOTER_RESTANTE}{' '}
            <strong>
              {tryGetMonetaryValueOrDefault(
                distribuicaoValores.distribuicaoRestante,
                '0',
              )}
            </strong>
          </Text>
        </GridItem>
        <Grid style={{ flexDirection: 'column' }} margin="0">
          <GridItem>
            <Text variant="text-standard-400">
              {CONSTANTES.SESSOES.FOOTER_TOTAL}{' '}
              <strong>
                {tryGetMonetaryValueOrDefault(
                  distribuicaoValores.somaEntreFundosDestino,
                  '0',
                )}
              </strong>
            </Text>

            <DestinoAcoesButton
              onClick={() => setVerMais(!verMais)}
              variant="auxiliary"
              leftIcon={<AddCircleOutline height="16px" />}
              size="small"
            >
              <Text variant="text-standard-600">
                {getTernaryResult(
                  verMais,
                  CONSTANTES.SESSOES.FOOTER_VER_MENOS,
                  CONSTANTES.SESSOES.FOOTER_VER_MAIS,
                )}
              </Text>
            </DestinoAcoesButton>
          </GridItem>
        </Grid>
      </Grid>

      <Grid justify="flex-end" margin="3rem 0 0">
        <Button
          variant="secondary"
          onClick={() => {
            setModalProsseguir(true);
            setErro();
          }}
        >
          {CONSTANTES.SESSOES.FOOTER_BOTAO_PROSSEGUIR}
        </Button>
      </Grid>
    </>
  );
};
