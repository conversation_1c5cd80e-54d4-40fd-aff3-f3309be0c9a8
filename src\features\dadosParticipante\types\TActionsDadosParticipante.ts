import {
  EDadosParticipanteActionKind,
  IControlesLGPD,
  IDadosBuscarEnderecoCepResponse,
} from '../exports';

export type TActionsDadosParticipante =
  | IActionEditarDadosParticipante
  | IActionSalvarDadosParticipante
  | IActionAlterarConsentimentos
  | IActionCancelarEdicaoDadosParticipante
  | IActionConsultarCep
  | IActionAlterarAssinaturaValida;

interface IActionEditarDadosParticipante {
  type: EDadosParticipanteActionKind.EDITAR_DADOS_PARTICIPANTE;
}

interface IActionCancelarEdicaoDadosParticipante {
  type: EDadosParticipanteActionKind.CANCELAR_EDICAO_DADOS_PARTICIPANTE;
}

interface IActionSalvarDadosParticipante {
  type: EDadosParticipanteActionKind.SALVAR_DADOS_PARTICIPANTE;
  salvar?: boolean;
}

interface IActionAlterarConsentimentos {
  type: EDadosParticipanteActionKind.ALTERAR_CONSETIMENTOS;
  consentimentos: IControlesLGPD;
}

interface IActionAlterarAssinaturaValida {
  type: EDadosParticipanteActionKind.ALTERAR_ASSINATURA_VALIDA;
  assinaturaValida: boolean;
}

interface IActionConsultarCep {
  type: EDadosParticipanteActionKind.CONSULTAR_CEP;
  novoEndereco: IDadosBuscarEnderecoCepResponse;
}
