export const FORMA_DE_PAGAMENTO = [
  {
    text: 'Débito em conta',
    value: 'Débito em conta',
  },
];

export const LABEL_NOVA_CONTA = {
  text: 'Nova Conta',
  value: 'Nova Conta',
};
export const TEXT_NOVA_CONTA = 'Nova Conta';
export const TEXT_DEBITO_CONTA = 'Débito em conta';

export const TIPOS_OPERACAO = [
  {
    value: '001',
    text: '001 - Conta Corrente',
  },
  {
    value: '013',
    text: '013 - Conta Poupança',
  },
  {
    value: '023',
    text: '023 - Conta Caixa Fácil',
  },
  {
    value: '1288',
    text: '1288 - Poupança Caixa Fácil',
  },
  {
    value: '3701',
    text: '3701 - Conta Corrente',
  },
];

export const DIA_MINIMO_VENCIMENTO = 5;

export const DIA_MAXIMO_VENCIMENTO = 25;

export const CODIGOS_RETORNO = {
  X5: 'X5',
};

export const TEXTOS = {
  GERAR_BOLETO: 'Gera<PERSON> boleto',
  TIPO_DOCUMENTO: 'Boleto',
};
