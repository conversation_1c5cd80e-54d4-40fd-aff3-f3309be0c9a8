import { LoadingSpinner, Text } from '@cvp/design-system-caixa';

interface ICarregandoProps {
  children?: React.ReactNode;
}

export const Carregando: React.FC<ICarregandoProps> = ({
  children = 'Carregando...',
}) => {
  return (
    <div>
      <LoadingSpinner size="large">
        <Text variant="text-large-400" fontColor="brand-primary-09">
          {children}
        </Text>
      </LoadingSpinner>
    </div>
  );
};
