import {
  ApoliceIcon,
  CalendarioIcon,
  ICertificadoContentFactory,
  ICertificadoContentFactoryReturn,
  LABEL_DESCRIPTION,
  Utils,
} from '@src/features/layoutPrevidencia/exports';

export const CertificadoContentFactory = ({
  certificado,
  response,
  theme,
}: ICertificadoContentFactory): ICertificadoContentFactoryReturn[] => {
  return [
    {
      id: 1,
      icon: (
        <ApoliceIcon color={theme.color.palette.grayscale['70']} size="big" />
      ),
      label: LABEL_DESCRIPTION.NUMERO_CERTIFICADO,
      value: certificado?.certificadoNumero ?? '--',
      width: 236,
    },
    {
      id: 2,
      icon: (
        <ApoliceIcon color={theme.color.palette.grayscale['70']} size="big" />
      ),
      label: LABEL_DESCRIPTION.SALDO_TOTAL,
      value: Utils.tryGetMonetaryValueOrDefault(response?.saldoTotal),
      width: 204,
    },
    {
      id: 3,
      icon: (
        <CalendarioIcon
          color={theme.color.palette.grayscale['70']}
          size="big"
        />
      ),
      label: LABEL_DESCRIPTION.CONTRATACAO,
      value: Utils.formatarDataHoraAmigavel(certificado?.emissao),
    },
    {
      id: 4,
      icon: (
        <CalendarioIcon
          color={theme.color.palette.grayscale['70']}
          size="big"
        />
      ),
      label: LABEL_DESCRIPTION.VIGENCIA,
      value: Utils.formatarDataHoraAmigavel(certificado?.aposentadoria),
    },
  ];
};
