import { IMapearDadosSelecaoAliquotaFactoryRetorno } from '@src/features/financeiro/resgate/exports';

export interface IMontarDadosTabelaDetalheCalculoFactory {
  tipoAliquota: string;
  dadosSelecaoAliquota: IMapearDadosSelecaoAliquotaFactoryRetorno | undefined;
}

export interface IMontarDadosTabelaDetalheCalculoFactoryRetorno {
  data: string;
  valorPrincipal: string;
  rendimentos: string;
  saldoAporte: string;
  valorSolicitado: string;
  saldoPrincipal: string;
  rendimento: string;
  valorCorrecao: string;
  carregamentoSaida: string;
  baseIrrf: string;
  aliquotaIrrf: string;
  valorIrrf: string;
  taxaSaida: string;
  valorLiquido: string;
  numeroResgate: string;
  codigoConta: string;
  id: string;
  codigoEmpresa: string;
  codigoFundo: string;
  descricaoFundo: string;
}
