import styled from 'styled-components';

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
`;

export const ContribuicaoSection = styled.div`
  border: 1px solid ${({ theme }) => theme.color.border.neutral['03']};
  border-radius: 8px;
  padding: 1rem;
  background: ${({ theme }) => theme.color.background.neutral['01']};
`;

export const TitleSection = styled.div`
  margin-bottom: 1rem;
`;

export const ContribuicaoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
`;

export const InfoColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
  min-width: 200px;
`;

export const SwitchColumn = styled.div`
  display: flex;
  align-items: center;
  min-width: 120px;
`;

export const ExtraInfo = styled.div`
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.color.border.neutral['02']};
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;