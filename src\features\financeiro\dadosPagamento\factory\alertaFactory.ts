import * as DadosPagamento from '@src/features/financeiro/dadosPagamento/exports';

const obterVarianteAlertaPorRespostaDadosPagamento = <TResposta = unknown>(
  response: DadosPagamento.IHandleReponseResult<TResposta>,
  codigoRetorno?: string,
): DadosPagamento.IVariantAlert['variant'] => {
  const sucesso = DadosPagamento.checkIfAllItemsAreTrue([
    response?.sucessoBFF,
    response?.sucessoGI,
  ]);

  if (DadosPagamento.checkIfAllItemsAreTrue([sucesso, !!codigoRetorno])) {
    return DadosPagamento.getTernaryResult(
      codigoRetorno === DadosPagamento.CODIGOS_RETORNO.X5,
      DadosPagamento.ALERTA_CORES_VARIANTES.ATENCAO,
      DadosPagamento.ALERTA_CORES_VARIANTES.SUCESSO,
    );
  }

  return DadosPagamento.getTernaryResult(
    sucesso,
    DadosPagamento.ALERTA_CORES_VARIANTES.SUCESSO,
    DadosPagamento.ALERTA_CORES_VARIANTES.ERRO,
  );
};

/**
 * Responsável por construir um array de alertas com variantes,
 * baseadas nas respostas do fluxo de alteração dos métodos de pagamento.
 *
 * @template TResposta Tipo genérico da resposta esperada no fluxo.
 * @param {DadosPagamento.TConfigurarMensagemFactory<TResposta>} config - Configuração com dados e mensagens da resposta.
 * @returns {DadosPagamento.TAlertaDadosPagamento[]} Lista de alertas formatados para exibição.
 */

export const obterAlertaFactory = <TResposta = unknown>(
  config: DadosPagamento.TConfigurarMensagemFactory<TResposta>,
): DadosPagamento.TAlertaDadosPagamento[] => {
  if (!config?.response?.mensagens?.length) return [];

  if (config.exibirMultiplosAlertas) {
    return config.response.mensagens.map(item => ({
      mensagem: DadosPagamento.traduzirMensagem(
        DadosPagamento.tryGetValueOrDefault([item.descricao], ''),
      ),
      variant: obterVarianteAlertaPorRespostaDadosPagamento(
        config.response,
        config.codigoRetorno,
      ),
    }));
  }

  return [
    {
      variant: obterVarianteAlertaPorRespostaDadosPagamento(
        config.response,
        config.codigoRetorno,
      ),
      mensagem: DadosPagamento.traduzirMensagem(
        DadosPagamento.getTernaryResult(
          !!config.descricaoMensagemSistema,
          config.descricaoMensagemSistema!,
          DadosPagamento.tryGetValueOrDefault(
            [config.response.mensagens?.[0]?.descricao],
            '',
          ),
        ),
      ),
    },
  ];
};
