import * as SelectTypeRendaImports from '@src/features/consulta/consultarSimulacaoDeRenda/exports/index';

export const SelectTypeRenda: React.FC<
  SelectTypeRendaImports.ISelectTypeRenda
> = ({
  handleClearValuesData,
  handleClearUltimasSolicitacoes,
  handleSetIncomeRenda,
}) => {
  const { tiposDeRendaFormatado } =
    SelectTypeRendaImports.useObterTipoDeRenda();
  return (
    <SelectTypeRendaImports.GridItem xs="1 / 2">
      <SelectTypeRendaImports.Text
        variant="text-standard-600"
        fontColor="content-neutral-05"
      >
        {SelectTypeRendaImports.LABEL_SELECT_RENDA}
      </SelectTypeRendaImports.Text>
      <SelectTypeRendaImports.Select
        id="tipoderenda"
        onChange={value => {
          handleClearValuesData();
          handleClearUltimasSolicitacoes();
          handleSetIncomeRenda(value);
        }}
        options={tiposDeRendaFormatado}
        placeholder="Selecione"
        size="standard"
        textVariant="text-small-400"
        sizeWidth="large"
        variant="box-classic"
      />
    </SelectTypeRendaImports.GridItem>
  );
};
