import {
  fetchGatewayKey,
  IApiGatewayCvpContextConfiguration,
  IDadosAutenticacao,
} from '@cvp/componentes-posvenda';
import { ConfigApiGatewayConstants } from '@src/corporativo/constants/ConfigApiGatewayConstants';
import api from '@src/corporativo/infra/config/api/api';
import { PECO_MATRIZ } from '@src/corporativo/infra/config/api/endpoints';

interface IObterPayloadMatrizAcesso {
  (): Promise<IDadosAutenticacao>;
}
export const obterPayloadMatrizAcesso: IObterPayloadMatrizAcesso = async () => {
  const { SHArsaKey, UserName } = await fetchGatewayKey(
    api,
    PECO_MATRIZ.ConsultarMatrizAcessoPrevidencia,
    ConfigApiGatewayConstants as IApiGatewayCvpContextConfiguration,
  );

  return { SH<PERSON><PERSON><PERSON><PERSON>, UserName };
};
