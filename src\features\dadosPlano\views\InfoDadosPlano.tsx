import { DS } from '../exports';

export interface IInfoDadosPlanoProps {
  title: string;
  value: string;
}

const InfoDadosPlano = ({
  title,
  value,
}: IInfoDadosPlanoProps): React.JSX.Element => {
  return (
    <DS.Grid justify="space-between">
      <DS.GridItem xs="1/2">
        <DS.Text variant="text-standard-400" display="inline" marginLeft="30px">
          {title}
        </DS.Text>
      </DS.GridItem>
      <DS.GridItem xs="1/2">
        <DS.Text
          variant="text-standard-400"
          textAlign="right"
          marginRight="150px"
        >
          {value}
        </DS.Text>
      </DS.GridItem>
    </DS.Grid>
  );
};

export default InfoDadosPlano;
