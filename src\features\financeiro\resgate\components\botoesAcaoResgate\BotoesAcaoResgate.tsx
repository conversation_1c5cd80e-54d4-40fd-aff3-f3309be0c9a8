import * as Resgate from '@src/features/financeiro/resgate/exports';

export const BotoesAcaoResgate = ({
  onClickDetalhamento,
  onClickSolicitarResgate,
}: Resgate.IBotoesAcaoResgateProps): React.ReactElement => {
  const { values } =
    Resgate.useFormikContext<Resgate.IFormikValuesSimulacaoResgate>();

  return (
    <Resgate.GridItem xs="1">
      <Resgate.ContainerButtons>
        <Resgate.Button
          onClick={onClickDetalhamento}
          disabled={!values.aliquotaParaResgateSelecionada}
          size="standard"
          variant="secondary-outlined"
        >
          Valores detalhados
        </Resgate.Button>

        <Resgate.Button
          onClick={onClickSolicitarResgate}
          disabled={!values.aliquotaParaResgateSelecionada}
          size="standard"
          variant="secondary"
        >
          Solicitar resgate
        </Resgate.Button>
      </Resgate.ContainerButtons>
    </Resgate.GridItem>
  );
};
