import { Utils } from '../exports';

export const camposDadosParticipante = (): Array<
  Array<{
    fracao: string;
    label: string;
    campo: string;
    editavel?: boolean;
    disabled?: boolean;
    mask?: (val: string) => string;
  }>
> => [
  [
    {
      fracao: '3/12',
      label: 'Nome do cliente',
      campo: 'nome',
      editavel: false,
      mask: Utils.cep.mask,
    },
    {
      fracao: '3/12',
      label: 'Nome social',
      campo: 'nomeSocial',
      editavel: true,
    },
    {
      fracao: '4/12',
      label: 'Data de nascimento',
      campo: 'dataNascimento',
      editavel: false,
    },
  ],
  [
    {
      fracao: '3/12',
      label: 'CEP',
      campo: 'cep',
      editavel: true,
      mask: Utils.cep.mask,
    },
    {
      fracao: '3/12',
      label: 'Logradouro',
      campo: 'endereco',
      disabled: true,
      editavel: true,
    },
    {
      fracao: '2/12',
      label: 'Número',
      campo: 'numero',
      editavel: true,
    },
    {
      fracao: '4/12',
      label: 'Complemento',
      campo: 'complemento',
      editavel: true,
    },
    {
      fracao: '3/12',
      label: 'Bairro',
      campo: 'bairro',
      disabled: true,
      editavel: true,
    },
    {
      fracao: '3/12',
      label: 'Cidade',
      campo: 'cidade',
      disabled: true,
      editavel: true,
    },
    {
      fracao: '4/12',
      label: 'Estado',
      campo: 'uf',
      disabled: true,
      editavel: true,
    },
  ],
  [
    {
      fracao: '3/12',
      label: 'E-mail',
      campo: 'email',
      editavel: true,
    },
    {
      fracao: '3/12',
      label: 'Telefone Celular',
      campo: 'celular',
      editavel: true,
      mask: (val: string) =>
        Utils.telefoneUtils.mask(val, {
          type: 'Residencial',
        }),
    },
    {
      fracao: '4/12',
      label: 'Outro Telefone',
      campo: 'telefone',
      editavel: true,
      mask: (val: string) =>
        Utils.telefoneUtils.mask(val, {
          type: 'Comercial',
        }),
    },
  ],
];
