import {
  TConsultaFiltros,
  TDefinirPeriodo,
  VALORES_PERIODOS,
  formatarDataAdicionandoSeparador,
} from '@src/features/consulta/historicoSolicitacoes/exports';

const dataInicio = new Date();

dataInicio.setDate(
  dataInicio.getDate() - Number(VALORES_PERIODOS.ULTIMOS_12_MESES),
);

export const FILTRO_PERIODO_PERSONALIZADO = {
  inicio: formatarDataAdicionandoSeparador(dataInicio.toLocaleDateString()),
  fim: formatarDataAdicionandoSeparador(new Date().toLocaleDateString()),
};

export const obterLabelFiltroAplicado = (
  filtros: TConsultaFiltros,
  personalizado: boolean,
): string => {
  if (!filtros?.dataFinal || !filtros?.dataInicial) return '';
  const inicio = new Date(filtros.dataInicial);
  const fim = new Date(filtros.dataFinal);

  if (personalizado) {
    inicio.setDate(inicio.getDate() + 1);
    fim.setDate(fim.getDate() + 1);
  }

  return `${inicio.toLocaleDateString()} à ${fim.toLocaleDateString()}`;
};

export const definirPeriodo = (
  periodoValor: string,
  periodoPersonalizado: typeof FILTRO_PERIODO_PERSONALIZADO,
): TDefinirPeriodo => {
  if (!periodoValor) return {} as TDefinirPeriodo;

  if (periodoValor === VALORES_PERIODOS.PERIODO_PERSONALIZADO) {
    return {
      dataInicial: periodoPersonalizado.inicio,
      dataFinal: periodoPersonalizado.fim,
    };
  }

  const inicio = new Date();
  inicio.setDate(inicio.getDate() - Number(periodoValor));

  return {
    dataInicial: inicio.toISOString(),
    dataFinal: new Date().toISOString(),
  };
};
