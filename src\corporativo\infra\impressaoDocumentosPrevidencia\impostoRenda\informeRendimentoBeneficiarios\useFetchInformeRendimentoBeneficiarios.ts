import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IInformeRendimentosBeneficiariosEntity,
  IUseFetchInformeRendimentosBeneficiariosPayload,
  IUseFetchInformeRendimentosBeneficiariosReturn,
} from '@src/corporativo/types/impressaoDocumentosPrevidencia/IUseFetchInformeRendimentosBeneficiarios';

const useFetchInformeRendimentosBeneficiarios = (
  payload?: IUseFetchInformeRendimentosBeneficiariosPayload,
): IUseFetchInformeRendimentosBeneficiariosReturn => {
  const { invocarApiGatewayCvpComToken, loading } = useApiGatewayCvpInvoker<
    IUseFetchInformeRendimentosBeneficiariosPayload,
    IInformeRendimentosBeneficiariosEntity
  >(PECOS.ObterInforme, {
    data: payload,
    autoFetch: false,
  });

  return {
    isLoadingInformeRendimentosBeneficiarios: loading,
    fetchInformeRendimentosBeneficiarios: invocarApiGatewayCvpComToken,
  };
};

export default useFetchInformeRendimentosBeneficiarios;
