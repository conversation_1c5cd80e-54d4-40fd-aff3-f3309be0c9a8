export interface IResponseRecuperarContribuicoesCertificado {
  beneficioContribuicaoCertificado: [
    {
      beneficioId: string;
      categoriaContribuicao: string;
      dataPagamento: string;
      dataProximoPagamento: string;
      descricaoBeneficio: string;
      descricaoRenda: string;
      diaPagamento: string;
      fatorRenda: string;
      identificadorLegal: string;
      indRevisaoPermitida: string;
      nomeContribuicao: string;
      origemContribuicao: string;
      periodicidade: string;
      planoId: string;
      statusBeneficio: string;
      statusContribuicao: string;
      subStatusBeneficio: string;
      termoDesejado: string;
      tipoBeneficio: string;
      tipoCobertura: string;
      tipoContribuicao: string;
      tipoRenda: string;
      valorBeneficio: string;
      valorBeneficioEsperado: string;
      valorContribuicaoEsperado: string;
      valorPagamento: string;
    },
  ];
  contaId: string;
  cpfPessoaCertificado: string;
  descricaoProduto: string;
  empresaId: string;
  nomePessoaCertificado: string;
  produtoAgredado: string;
  produtoId: string;
  subCategoriaProduto: string;
  valorTotalSaldo: string;
}
