import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault } from '@cvp/utils';

import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import {
  IConsultarTiposPagamentoPayload,
  IConsultarTiposPagamentoResponse,
  IUseConsultarTiposPagamentoReturn,
} from '@src/corporativo/types/financeiro/resgate/IConsultarTiposPagamento';

export const useConsultarTiposPagamento =
  (): IUseConsultarTiposPagamentoReturn => {
    const {
      response: tiposPagamento,
      loading: isLoadingTiposPagamento,
      invocarApiGatewayCvpComToken: consultarTiposPagamento,
    } = useApiGatewayCvpInvoker<
      Partial<IConsultarTiposPagamentoPayload>,
      IConsultarTiposPagamentoResponse
    >(PECOS.ConsultarTiposPagamento, {});

    return {
      tiposPagamento: tryGetValueOrDefault(
        [tiposPagamento?.entidade],
        {} as IConsultarTiposPagamentoResponse,
      ),
      isLoadingTiposPagamento,
      consultarTiposPagamento,
    };
  };
