import {
  columnsTabelaFundosDistribuicaoFactory,
  customStyles,
  FUNDOS,
  IDadosFundosNovosAporte,
  Match,
  Table,
  tryGetValueOrDefault,
  useRenderTabelaFundos,
  Text,
  TIPO_FUNDO,
  useAporteContext,
  dadosTabelaFundosAporteFactory,
} from '@src/features/financeiro/aporte/exports';

interface ITabelaFundosDistribuicaoNovos {
  renderFundos: IDadosFundosNovosAporte;
}

const TabelaFundosDistribuicaoNovos: React.FC<
  ITabelaFundosDistribuicaoNovos
> = ({ renderFundos }) => {
  const { itensExtrato } = useAporteContext();

  const dadosTabelaFundos = dadosTabelaFundosAporteFactory({
    renderFundos,
    itensExtrato,
  });
  return (
    <Match when={renderFundos.dadosFundos?.length > 0}>
      <Text variant="text-large-600" fontColor="content-neutral-04">
        {FUNDOS.fundosNovos}
      </Text>
      <Table
        responsive
        striped
        highlightOnHover
        themeTable="cvp-04"
        customStyles={customStyles}
        data={tryGetValueOrDefault(
          [
            useRenderTabelaFundos({
              dadosTabelaFundos,
              tipoFundo: TIPO_FUNDO.novo,
            }),
          ],
          [],
        )}
        columns={columnsTabelaFundosDistribuicaoFactory}
        noDataComponent={FUNDOS.fundosNovosIndisponiveis}
      />
    </Match>
  );
};

export default TabelaFundosDistribuicaoNovos;
