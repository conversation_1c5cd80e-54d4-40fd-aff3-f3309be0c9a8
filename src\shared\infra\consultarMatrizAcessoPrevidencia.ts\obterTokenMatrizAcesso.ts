import {
  checkIfAllItemsAreTrue,
  getSessionItem,
  setSessionItem,
  tryGetValueOrDefault,
} from '@cvp/utils';
import { ChavesArmazenamento } from '@src/corporativo/constants/ChavesArmazenamento';
import { IApiResponse } from '@cvp/componentes-posvenda';
import { IClientePrevidencia } from '@src/corporativo/types/cliente/IClientePrevidencia';
import { IMatrizAcessoClienteResponse } from '@src/corporativo/types/shared/consultarMatrizAcessoPrevidencia.ts/IMatrizAcessoClienteResponse';
import { ConfigApiGatewayConstants } from '@src/corporativo/constants/ConfigApiGatewayConstants';
import api from '../../../corporativo/infra/config/api/axiosConfig';
import { obterPayloadMatrizAcesso } from './obterPayloadMatrizAcesso';
import { setPermissoesCertificado } from '../../../features/consultaCertificado/utils/setPermissoesCertificado';
import { PECO_MATRIZ } from '../../../corporativo/infra/config/api/endpoints';

interface IObterTokenMatrizAcesso {
  (codCliente: string | null): Promise<string | undefined>;
}

export const obterTokenMatrizAcesso: IObterTokenMatrizAcesso = async (
  codCliente: string | null,
) => {
  const matrizCached = getSessionItem<IMatrizAcessoClienteResponse>(
    ChavesArmazenamento.TOKEN_CLIENTE,
  );
  const certificadoAtual = getSessionItem<IClientePrevidencia>(
    ChavesArmazenamento.CLINTE_PREVIDENCIA,
  );

  const matrizCertificadoCached = tryGetValueOrDefault(
    [matrizCached?.matrizAcessoCliente?.lstWebSession],
    [],
  ).find(x => x.certificado === certificadoAtual?.numCertificado);

  if (
    checkIfAllItemsAreTrue([
      !!matrizCertificadoCached,
      !!matrizCertificadoCached?.xPrevToken,
    ])
  ) {
    setPermissoesCertificado(matrizCertificadoCached);
    return matrizCertificadoCached?.xPrevToken;
  }

  const { SHArsaKey, UserName } = await obterPayloadMatrizAcesso();
  const response = await api.post<IApiResponse<IMatrizAcessoClienteResponse>>(
    `${ConfigApiGatewayConstants.configure.operationPath}${PECO_MATRIZ.ConsultarMatrizAcessoPrevidencia}`,
    {
      SHArsaKey,
      UserName,
      codCliente: tryGetValueOrDefault([codCliente], certificadoAtual?.cpfCnpj),
    },
  );

  const matriz = response?.data?.dados?.entidade;

  setSessionItem(ChavesArmazenamento.TOKEN_CLIENTE, matriz);

  const matrizCertificado = tryGetValueOrDefault(
    [matriz?.matrizAcessoCliente?.lstWebSession],
    [],
  ).find(x => x.certificado === certificadoAtual?.numCertificado);

  setPermissoesCertificado(matrizCertificadoCached);

  return matrizCertificado?.xPrevToken;
};
