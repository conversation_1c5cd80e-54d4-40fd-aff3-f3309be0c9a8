import {
  FastField,
  FieldProps,
  formatterTableCells,
  InputCelulaBeneficiario,
  Match,
  SwitchCase,
  TCelulaTabelaBeneficiario,
  Text,
  TFormatterTableCellParams,
} from '../exports';

export const CelulaTabelaBeneficiario: React.FC<TCelulaTabelaBeneficiario> = ({
  name,
  tipo,
  error,
  required,
  estado,
  id,
  coberturaId,
  index,
}) => {
  const completeName = `${coberturaId}.beneficiarios[${index}].${name}`;
  return (
    <FastField name={completeName}>
      {({ field }: FieldProps<string>) => (
        <SwitchCase
          fallback={
            <Text variant="text-standard-400" marginBottom="0" marginTop="0">
              {formatterTableCells({
                val: field.value,
                tipo,
              } as unknown as TFormatterTableCellParams)}
            </Text>
          }
        >
          <Match when={id !== 'herdeiros-legais'}>
            <InputCelulaBeneficiario
              id={id}
              name={name}
              error={error}
              tipo={tipo}
              field={field}
              estado={estado}
              required={required}
              coberturaId={coberturaId}
            />
          </Match>
        </SwitchCase>
      )}
    </FastField>
  );
};
