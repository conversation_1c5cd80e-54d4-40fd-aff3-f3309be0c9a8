import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { getSessionItem, tryGetValueOrDefault } from '@cvp/utils';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import { IResponseTipoSolicitacao } from '@src/corporativo/types/consulta/historicoSolicitacao';
import { useContext } from 'react';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';

type TPayload = {
  cpfCnpj: string;
  numeroCertificado: string;
};

const DEFAULT_RETURN: IResponseTipoSolicitacao[] = [];

type TUseObterHistoricoSolicitacao = {
  loading: boolean;
  response: IResponseTipoSolicitacao[];
};

export const useObterFiltroTipoSolicitacao =
  (): TUseObterHistoricoSolicitacao => {
    const cpf = String(getSessionItem('cpfCnpj'));
    const { certificadoAtivo } = useContext(PrevidenciaContext);

    const { loading, response } = useApiGatewayCvpInvoker<
      TPayload,
      IResponseTipoSolicitacao[]
    >(PECOS.ObterFiltroTipoSolicitacoes, {
      autoFetch: true,
      cacheKey: 'tipos_solicitacao',
      cache: true,
      cacheTime: 60 * 60 * 1000,
      data: {
        cpfCnpj: cpf,
        numeroCertificado: certificadoAtivo.certificadoNumero,
      },
    });

    return {
      response: tryGetValueOrDefault([response?.entidade], DEFAULT_RETURN),
      loading,
    };
  };
