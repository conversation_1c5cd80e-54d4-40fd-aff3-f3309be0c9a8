import {
  CONSTANTES,
  Ds,
  Fac,
  Styles,
  Types,
  Views,
} from '@src/features/consulta/reajusteAnualDoPlano/exports';

export const PeriodoExibido: React.FC<Types.TPeriodoExibidoProps> = ({
  data,
  onClickVoltar,
}) => {
  return (
    <>
      <Ds.TabelaPrevidencia
        themeTable="default"
        striped
        columns={Fac.obterColunasHistoricoPorAno()}
        data={data}
        noDataComponent={CONSTANTES.TEXTOS.SEM_DADOS}
      />

      <Views.Alerta alertaDetalhes />

      <Styles.Footer>
        <Ds.Button onClick={onClickVoltar} variant="secondary-outlined">
          Voltar
        </Ds.Button>
      </Styles.Footer>
    </>
  );
};
