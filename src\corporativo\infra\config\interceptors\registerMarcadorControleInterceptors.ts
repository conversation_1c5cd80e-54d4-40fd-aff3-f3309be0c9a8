import { getSessionItem } from '@cvp/utils';
import { AxiosInstance } from 'axios';

export const registerMarcadorControleInterceptors = (
  api: AxiosInstance,
): void => {
  api.interceptors.request.use(async config => {
    const marcadorControle = String(getSessionItem<string>('marcadorControle'));

    if (
      config.url?.includes('PortalEconomiario/') &&
      !config.url?.includes('PECO_AutenticarUsuario')
    ) {
      if (config.data instanceof FormData) {
        config.data.append('marcadorControle', marcadorControle);
      }

      if (!(config.data instanceof FormData)) {
        return {
          ...config,
          data: { ...config.data, marcadorControle },
        };
      }
    }
    return config;
  });
};
