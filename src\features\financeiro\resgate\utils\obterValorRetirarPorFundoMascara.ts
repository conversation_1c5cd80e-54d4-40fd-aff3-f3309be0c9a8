import {
  tryGetValueOrDefault,
  valoresMonetarios,
  IFormikValuesSimulacaoResgate,
} from '@src/features/financeiro/resgate/exports';

/**
 * Obtém o valor de retirada de um fundo específico a partir dos valores do Formik
 * e aplica a máscara monetária no valor.
 *
 * @param {IFormikValuesSimulacaoResgate} formikValues - Os valores atuais do formulário gerenciados pelo Formik.
 * @param {string} codigoFundo - O código identificador do fundo para o qual o valor de retirada será buscado.
 * @returns {string} O valor de retirada formatado com máscara monetária. Caso não exista, retorna o valor mascarado de uma string vazia.
 */
export const obterValorRetirarPorFundoMascara = (
  formikValues: IFormikValuesSimulacaoResgate,
  codigoFundo: string,
): string => {
  const index: number = formikValues.fundosParaResgate.findIndex(
    fundoItem => fundoItem.codigoFundo === codigoFundo,
  );

  const valorResgate: string = tryGetValueOrDefault(
    [formikValues.fundosParaResgate[index]?.valorRetirar],
    '',
  );

  return valoresMonetarios.mask(valorResgate);
};
