import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Mapear os dados de seleção de alíquota para resgate
 *
 * Esta função é responsável por consultar os detalhes das alíquotas (progressiva e/ou regressiva),
 * baseando-se na alíquota atual do certificado e na possibilidade de editar a alíquota.
 * Consolida os dados das duas alíquotas em um único objeto estruturado para uso na simulação.
 *
 * @param {IListarFundosParaResgateAliquota} aliquota - Informações da alíquota do certificado
 * @param {IParametrosPadraoCalculoAliquota} parametrosPadraoCalculoAliquota - Parâmetros para cálculo das alíquotas
 * @returns {Promise<IMapearDadosSelecaoAliquotaFactoryRetorno>} Promise que resolve para um objeto contendo
 * todos os dados de cálculo, resumo e detalhamento das alíquotas progressiva e regressiva
 */
export const mapearDadosSelecaoAliquotaFactory = async (
  aliquota: Resgate.IListarFundosParaResgateAliquota,
  parametrosPadraoCalculoAliquota: Resgate.IParametrosPadraoCalculoAliquota,
): Promise<Resgate.IMapearDadosSelecaoAliquotaFactoryRetorno> => {
  let dadosAliquotaPorRegimeProgressivo: Resgate.IConsultarDetalhesDaAliquotaFactoryRetorno =
    Resgate.DADOS_ALIQUOTA_DEFAULT;

  let dadosAliquotaPorRegimeRegressivo: Resgate.IConsultarDetalhesDaAliquotaFactoryRetorno =
    Resgate.DADOS_ALIQUOTA_DEFAULT;

  const isTipoRegimeProgressivo: boolean = Resgate.checkIfSomeItemsAreTrue([
    aliquota.aliquotaAtual === Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
    aliquota.indicadorPermiteEditarAliquota,
  ]);

  const isTipoRegimeRegressivo: boolean = Resgate.checkIfSomeItemsAreTrue([
    aliquota.aliquotaAtual === Resgate.ALIQUOTA.TIPO_REGIME_REGRESSIVO,
    aliquota.indicadorPermiteEditarAliquota,
  ]);

  if (isTipoRegimeProgressivo) {
    dadosAliquotaPorRegimeProgressivo =
      await Resgate.consultarDetalhesDaAliquotaFactory({
        aliquotaAtual: Resgate.ALIQUOTA.TIPO_REGIME_PROGRESSIVO,
        ...parametrosPadraoCalculoAliquota,
      });
  }

  if (isTipoRegimeRegressivo) {
    dadosAliquotaPorRegimeRegressivo =
      await Resgate.consultarDetalhesDaAliquotaFactory({
        aliquotaAtual: Resgate.ALIQUOTA.TIPO_REGIME_REGRESSIVO,
        ...parametrosPadraoCalculoAliquota,
      });
  }

  return {
    calculoAliquotaProgressiva: dadosAliquotaPorRegimeProgressivo.calculo,
    calculoAliquotaRegressiva: dadosAliquotaPorRegimeRegressivo.calculo,
    resumoAliquotaProgressiva: dadosAliquotaPorRegimeProgressivo.resumo,
    resumoAliquotaRegressiva: dadosAliquotaPorRegimeRegressivo.resumo,
    detalhamentoAliquotaProgressiva:
      dadosAliquotaPorRegimeProgressivo.detalhado,
    detalhamentoAliquotaRegressiva: dadosAliquotaPorRegimeRegressivo.detalhado,
  };
};
