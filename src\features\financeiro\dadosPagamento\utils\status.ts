import { checkIfSomeItemsAreTrue } from '@cvp/utils';
import {
  CODIGO_STATUS_COBRANCA,
  STATUS_COBRANCA_CONVERTIDO,
} from '../constants/constants';

export const coverterDescStatus = (codStatusCobranca: string): string => {
  if (
    checkIfSomeItemsAreTrue([
      codStatusCobranca === CODIGO_STATUS_COBRANCA.PAGO,
      codStatusCobranca === CODIGO_STATUS_COBRANCA.PARCIALMENTE_PAGO,
    ])
  ) {
    return STATUS_COBRANCA_CONVERTIDO.PAGA;
  }

  if (codStatusCobranca === CODIGO_STATUS_COBRANCA.INADIMPLENTE) {
    return STATUS_COBRANCA_CONVERTIDO.NAO_PAGO;
  }

  if (
    checkIfSomeItemsAreTrue([
      codStatusCobranca === CODIGO_STATUS_COBRANCA.GERADA,
      codStatusCobranca === CODIGO_STATUS_COBRANCA.COBRADA,
      codStatusCobranca === CODIGO_STATUS_COBRANCA.PENDENTE,
    ])
  ) {
    return STATUS_COBRANCA_CONVERTIDO.PENDENTE;
  }

  if (
    checkIfSomeItemsAreTrue([
      codStatusCobranca === CODIGO_STATUS_COBRANCA.CANCELADA,
      codStatusCobranca === CODIGO_STATUS_COBRANCA.CANCELADA_INADIMPLENCIA,
    ])
  ) {
    return STATUS_COBRANCA_CONVERTIDO.CANCELADA;
  }

  return '';
};
