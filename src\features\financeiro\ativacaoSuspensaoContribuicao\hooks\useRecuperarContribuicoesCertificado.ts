import { useContext, useMemo, useState, useEffect } from 'react';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import { tryGetValueOrDefault, getSessionItem } from '@cvp/utils';
import { PECOS } from '@src/corporativo/infra/config/api/endpoints';
import { PrevidenciaContext } from '@src/corporativo/context/PrevidenciaContext';
import {
  IPayloadCoberturasContratadas,
  IResponseStatusCoberturasContratadas,
  EnumCategoria,
} from '../types/EnumsStatusAtivacaoSuspensao.types';
import { mockRecuperarContribuicoesCertificado } from '../mocks/mockRecuperarContribuicoesCertificado';

// Utilitários de desenvolvimento inline para evitar problemas de import
const isDevelopmentMode = (): boolean => {
  return process.env.NODE_ENV === 'development';
};

const shouldUseMockData = (): boolean => {
  return (
    isDevelopmentMode() &&
    (localStorage.getItem('CVP_USE_MOCK_DATA') === 'true' ||
      sessionStorage.getItem('CVP_USE_MOCK_DATA') === 'true')
  );
};

const getDevelopmentCacheConfig = () => {
  if (isDevelopmentMode()) {
    return {
      cacheTime: 10 * 60 * 1000, // 10 minutos em desenvolvimento
      enableCache: true,
      aggressiveCache: true,
    };
  }

  return {
    cacheTime: 2 * 60 * 1000, // 2 minutos em produção
    enableCache: true,
    aggressiveCache: false,
  };
};

// Função para habilitar/desabilitar dados mockados via console
const enableMockData = (enable: boolean = true) => {
  if (isDevelopmentMode()) {
    localStorage.setItem('CVP_USE_MOCK_DATA', enable.toString());
    console.log(
      `Mock data ${
        enable ? 'habilitado' : 'desabilitado'
      }. Recarregue a página para aplicar.`,
    );
  } else {
    console.warn(
      'Mock data só pode ser habilitado em ambiente de desenvolvimento',
    );
  }
};

// Expor função globalmente para facilitar uso no console do desenvolvedor
if (isDevelopmentMode() && typeof window !== 'undefined') {
  (window as any).CVP_enableMockData = enableMockData;
  console.log(
    '💡 Dica: Use CVP_enableMockData(true) no console para habilitar dados mockados',
  );
}

const useObterCoberturasContratadas = () => {
  const { certificadoAtivo } = useContext(PrevidenciaContext);
  const cpfCnpj = String(getSessionItem<string>('cpfCnpj'));

  // Estados para controle de mock data
  const [mockResponse, setMockResponse] =
    useState<IResponseStatusCoberturasContratadas | null>(null);
  const [isUsingMock, setIsUsingMock] = useState(false);

  // Configurações de cache otimizadas para desenvolvimento
  const cacheConfig = useMemo(() => getDevelopmentCacheConfig(), []);

  // Memoizar o payload para evitar recriações desnecessárias
  const payload: IPayloadCoberturasContratadas = useMemo(
    () => ({
      Cpf: cpfCnpj,
      NumeroCertificado: certificadoAtivo?.certificadoNumero || '',
      categoria: EnumCategoria.Coberturas,
      cpfCnpj,
    }),
    [cpfCnpj, certificadoAtivo?.certificadoNumero],
  );

  // Memoizar a chave de cache para evitar recriações
  const cacheKey = useMemo(
    () => `contribuicoes-${cpfCnpj}-${certificadoAtivo?.certificadoNumero}`,
    [cpfCnpj, certificadoAtivo?.certificadoNumero],
  );

  const { loading, response, invocarApiGatewayCvpComToken } =
    useApiGatewayCvpInvoker<
      IPayloadCoberturasContratadas,
      IResponseStatusCoberturasContratadas
    >(PECOS.RecuperarContribuicoesCertificado, {
      data: payload,
      autoFetch: false, // Desabilitar autoFetch para controle manual
      cache: cacheConfig.enableCache,
      cacheKey,
      cacheTime: cacheConfig.cacheTime,
    });

  // Verificar se deve usar dados mockados
  useEffect(() => {
    const useMock = shouldUseMockData();
    setIsUsingMock(useMock);

    if (useMock) {
      setMockResponse(mockRecuperarContribuicoesCertificado);
      if (isDevelopmentMode()) {
        console.log(
          '🎭 Usando dados mockados para ativação/suspensão de contribuição',
        );
      }
    } else {
      setMockResponse(null);
    }
  }, []);

  // Função customizada para invocar API que considera mock data
  const invocarApiCustomizada = useMemo(() => {
    return async (customPayload?: IPayloadCoberturasContratadas) => {
      if (isUsingMock && mockResponse) {
        // Simular delay de rede em desenvolvimento
        if (isDevelopmentMode()) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        return { entidade: mockResponse };
      }
      return invocarApiGatewayCvpComToken(customPayload);
    };
  }, [isUsingMock, mockResponse, invocarApiGatewayCvpComToken]);

  // Retornar dados mockados se estiver usando mock, senão dados da API
  const finalResponse = isUsingMock
    ? mockResponse
    : tryGetValueOrDefault([response?.entidade], null);
  const finalLoading = isUsingMock ? false : loading;

  return {
    loading: finalLoading,
    response: finalResponse,
    invocarApiGatewayCvpComToken: invocarApiCustomizada,
  };
};

export { useObterCoberturasContratadas as useRecuperarContribuicoesCertificado };
