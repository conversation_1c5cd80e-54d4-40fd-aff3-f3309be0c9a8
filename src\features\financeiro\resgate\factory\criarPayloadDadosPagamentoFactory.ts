import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Criar o payload de dados de pagamento para o resgate
 *
 * @param {string | undefined} numeroResgateConsolidado - Número do resgate consolidado
 * @param {IFormikValuesSimulacaoResgate} formikValues - Valores do formulário de simulação de resgate
 * @returns {TCriarPayloadDadosPagamentoFactoryReturn} Payload de dados de pagamento formatado
 * de acordo com o tipo de conta (nova ou existente)
 */
export const criarPayloadDadosPagamentoFactory = (
  numeroResgateConsolidado: string | undefined,
  formikValues: Resgate.IFormikValuesSimulacaoResgate,
): Resgate.TCriarPayloadDadosPagamentoFactoryReturn => {
  if (formikValues.isNovaConta) {
    return {
      codigoBanco: formikValues.novaConta.banco.value,
      dataProgramada: Resgate.converterDataParaString(new Date()),
      digitoAgencia: Resgate.PADROES_CONTA_BANCARIA.DIGITO_AGENCIA_PADRAO,
      digitoConta: formikValues.novaConta.digito,
      numeroAgencia: formikValues.novaConta.agencia,
      numeroConta: Resgate.formatarContaSeBancoCaixa({
        codigoBanco: formikValues.novaConta.banco.value,
        agencia: formikValues.novaConta.agencia,
        numeroConta: formikValues.novaConta.conta,
        operacao: formikValues.novaConta.operacao,
      }),
      numeroResgate: Number(numeroResgateConsolidado),
      tipoConta: Resgate.PADROES_CONTA_BANCARIA.SIGLA_CONTA_CORRENTE,
      tipoPagamento: Resgate.TIPOS_CANAL_PAGAMENTO.RESGATE_TRANSFERENCIA,
    };
  }

  if (!formikValues.isNovaConta) {
    return {
      codigoBanco: formikValues.contaExistente.codigoBanco,
      dataProgramada: Resgate.converterDataParaString(new Date()),
      digitoAgencia: Resgate.PADROES_CONTA_BANCARIA.DIGITO_AGENCIA_PADRAO,
      digitoConta: formikValues.contaExistente.digitoConta,
      numeroAgencia: formikValues.contaExistente.numeroAgencia,
      numeroConta: formikValues.contaExistente.codigoContaBancaria,
      numeroResgate: Number(numeroResgateConsolidado),
      tipoConta: Resgate.PADROES_CONTA_BANCARIA.SIGLA_CONTA_CORRENTE,
      tipoPagamento: Resgate.TIPOS_CANAL_PAGAMENTO.RESGATE_TRANSFERENCIA,
    };
  }

  return {};
};
