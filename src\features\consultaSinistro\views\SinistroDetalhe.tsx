import {
  DetalhesSinistro,
  IconExpandLessSharp,
  IconExpandMoreSharp,
  Match,
  React,
  S,
  Separator,
  SwitchCase,
  TDetalheSinistroProps,
  Text,
} from '../exports';

export const DetalheSinistro: React.FC<TDetalheSinistroProps> = ({
  codigoCertificado,
  numeroAvisoSinistro,
  andamentos,
}) => {
  const [open, setOpen] = React.useState(false);

  const handleOpen = () => {
    setOpen(state => !state);
  };

  return (
    <div style={{ marginTop: '24px' }}>
      <S.ButtonDetalhesStyled onClick={handleOpen}>
        <SwitchCase fallback={undefined}>
          <Match when={open}>
            <IconExpandLessSharp
              size="small"
              color="#005CA9"
              style={{ margin: '0 4px' }}
            />
          </Match>
          <Match when={!open}>
            <IconExpandMoreSharp
              size="small"
              color="#005CA9"
              style={{ margin: '0 4px' }}
            />
          </Match>
        </SwitchCase>
        <Text variant="text-standard-600" fontColor="content-highlight-01">
          Detalhes
        </Text>
      </S.ButtonDetalhesStyled>
      <SwitchCase fallback={null}>
        <Match when={open}>
          <Separator
            orientation="horizontal"
            overrideColor="#D0E0E3"
            size="4"
          />
          <div style={{ height: '32px' }} />
          <DetalhesSinistro
            codigoCertificado={codigoCertificado}
            numeroAvisoSinistro={numeroAvisoSinistro}
            andamentos={andamentos}
          />
        </Match>
      </SwitchCase>
    </div>
  );
};
