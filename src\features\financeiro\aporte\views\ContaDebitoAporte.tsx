import {
  AssinaturaAporte,
  BOTOES,
  Button,
  ButtonContainer,
  checkIfSomeItemsAreTrue,
  Grid,
  GridItem,
  LOADING,
  LoadingSpinner,
  Match,
  SelectContasBancarias,
  SwitchCase,
  useContaDebitoAporte,
} from '@src/features/financeiro/aporte/exports';

const ContaDebitoAporte: React.FC = () => {
  const {
    textoDadosBancarios,
    validarContaLoading,
    obterDadosBancariosLoading,
    contaBancariaSelecionada,
    handleVerificarConta,
    handleSelecionarContaBancaria,
    handleEtapaAnterior,
  } = useContaDebitoAporte();

  return (
    <SwitchCase>
      <Match when={obterDadosBancariosLoading}>
        <LoadingSpinner color="#005CA9" size="medium">
          {LOADING.DADOS_BANCARIOS}
        </LoadingSpinner>
      </Match>
      <Match when={!obterDadosBancariosLoading}>
        <SelectContasBancarias
          dadosBancarios={textoDadosBancarios}
          onChange={handleSelecionarContaBancaria}
        />

        <AssinaturaAporte />
        <Grid justify="flex-end">
          <GridItem>
            <Button
              onClick={handleEtapaAnterior}
              disabled={validarContaLoading}
              variant="secondary-outlined"
            >
              <ButtonContainer>{BOTOES.voltar}</ButtonContainer>
            </Button>
          </GridItem>
          <GridItem>
            <Button
              disabled={checkIfSomeItemsAreTrue([
                validarContaLoading,
                contaBancariaSelecionada.numeroConta === '',
              ])}
              onClick={handleVerificarConta}
              variant="secondary"
            >
              {BOTOES.confirmarAporte}
            </Button>
          </GridItem>
        </Grid>
      </Match>
    </SwitchCase>
  );
};

export default ContaDebitoAporte;
