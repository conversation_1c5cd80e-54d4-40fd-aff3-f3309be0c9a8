import * as CONFIG_ASSINATURA from '@src/corporativo/constants/assinatura/params';

export const getData = (cpfCnpj: string, numeroCertificado: string) => ({
  nomeMfe: AppConfig.REACT_APP_NOME_MFE,
  textoConfirmacao: CONFIG_ASSINATURA.MENSAGENS.CONFIRMACAO,
  icTokenSMS: true,
  origem: CONFIG_ASSINATURA.EOrigem.CLI,
  cpfCnpj,
  nsuProduto: numeroCertificado,
  metodosAssinatura: [
    CONFIG_ASSINATURA.EMetodoAssinatura.SENHA,
    CONFIG_ASSINATURA.EMetodoAssinatura.TOKEN,
  ],
  senhaRequest: {
    unidades: '',
    tiposConta: null,
    produtos: null,
    numeroConta: null,
  },
  tokenRequest: {
    telefoneOrigem: CONFIG_ASSINATURA.ETelfoneOrigem.SICLI,
    tempoExpiracaoToken: CONFIG_ASSINATURA.TOKEN_CONFIG.tempoExpiracao,
    tempoExpiracaoNovoToken:
      CONFIG_ASSINATURA.TOKEN_CONFIG.tempoExpiracaoNovoToken,
    transacaoGMS: '',
    subServico: '',
    parametros: [],
  },
});
