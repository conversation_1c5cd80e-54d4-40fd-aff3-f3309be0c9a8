import { TCoberturaBeneficiariosSchema } from '../exports';

interface IDadosBeneficiario {
  beneficiarioId: string;
  index: number;
  estado: string;
}

export const dadosBeneficiario = (
  beneficiarios: TCoberturaBeneficiariosSchema[],
): Array<IDadosBeneficiario> => {
  return beneficiarios.map((beneficiario, index) => ({
    beneficiarioId: beneficiario?.idBeneficiario ?? '',
    index,
    estado: beneficiario.estado,
  }));
};
