import { GridItem, Grid, Separator, Text } from '@cvp/design-system-caixa';
import {
  capitalize,
  formatarDataHoraAmigavel,
  getSessionItem,
  tryGetMonetaryValueOrDefault,
  tryGetValueOrDefault,
} from '@cvp/utils';

import React from 'react';
import IconApolice from '@src/corporativo/components/ApoliceIcon';
import IconCalendario from '@src/corporativo/components/CalendarioIcon';
import { useObterCoberturas } from '@src/corporativo/infra/obterCoberturas/useObterCoberturas';
import { ICertificadoPrevidenciaResponse } from '../exports';

type TICertificadoInfoProps = {
  certificado: ICertificadoPrevidenciaResponse;
};
const CertificadoInfo: React.FC<TICertificadoInfoProps> = ({ certificado }) => {
  const { response } = useObterCoberturas({
    NumeroCertificado: certificado?.certificadoNumero,
    Cpf: String(getSessionItem('cpfCnpj')),
  });

  return (
    <GridItem xs="1" style={{ margin: 0, padding: 0 }}>
      <Text variant="text-big-600" lineheight="farther">
        {capitalize(
          tryGetValueOrDefault(
            [
              `${certificado.produto.modalidade} | ${certificado.produto.descricaoProduto}`,
            ],
            '',
          ),
        )}
      </Text>
      <Grid style={{ alignItems: 'center', gap: '5px' }}>
        <GridItem
          style={{
            alignItems: 'center',
            display: 'flex',
            gap: '5px',
            margin: '0px 0px 0px 7px',
            width: 236,
          }}
        >
          <IconApolice color="#9EB2B8" size="big" />
          <Text variant="text-standard-400" marginBottom="0" marginTop="0">
            Numero Certificado: {certificado?.certificadoNumero}
          </Text>
        </GridItem>
        <Separator orientation="vertical" />
        <GridItem
          style={{
            alignItems: 'center',
            display: 'flex',
            gap: '5px',
            margin: 0,
            width: 204,
          }}
        >
          <IconApolice color="#9EB2B8" size="big" />
          <Text variant="text-standard-400" marginBottom="0" marginTop="0">
            Saldo Total: {tryGetMonetaryValueOrDefault(response?.saldoTotal)}
          </Text>
        </GridItem>
        <Separator orientation="vertical" />
        <GridItem
          style={{
            alignItems: 'center',
            display: 'flex',
            gap: '5px',
            margin: 0,
          }}
        >
          <IconCalendario color="#9EB2B8" size="big" />
          <Text variant="text-standard-400" marginBottom="0" marginTop="0">
            Contratação: {formatarDataHoraAmigavel(certificado?.emissao)}
          </Text>
        </GridItem>
        <Separator orientation="vertical" />
        <GridItem
          style={{
            alignItems: 'center',
            display: 'flex',
            gap: '5px',
            margin: 0,
          }}
        >
          <IconCalendario color="#9EB2B8" size="big" />
          <Text variant="text-standard-400" marginBottom="0" marginTop="0">
            Vigência: {formatarDataHoraAmigavel(certificado?.aposentadoria)}
          </Text>
        </GridItem>
      </Grid>
    </GridItem>
  );
};

export default CertificadoInfo;
