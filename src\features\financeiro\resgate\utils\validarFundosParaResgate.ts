import * as Resgate from '@src/features/financeiro/resgate/exports';

/**
 * Valida a lista de fundos para resgate, aplicando regras no campo `valorRetirar` de cada fundo selecionado.
 *
 * A validação é aplicada **apenas nos fundos cujo campo `selecionado` seja `true`**.
 *
 * As regras de validação incluem:
 * - Campo obrigatório (valor ausente ou zero)
 * - Valor inválido (não numérico)
 * - Valor inferior ao valor mínimo permitido
 *
 * Cada erro é associado a um caminho no formato `fundosParaResgate.{codigoFundo}.valorRetirar`,
 * compatível com a estrutura de erros flat usada pelo Formik.
 *
 * @param fundos - Lista de fundos que devem ser validados.
 * @param valorMinimo - Valor mínimo permitido para resgate, usado como base de comparação.
 * @returns Objeto com os erros encontrados no formato `{ [path]: mensagem }`, onde `path` segue o padrão flat do Formik.
 */
export const validarFundosParaResgate = (
  fundos: Resgate.IListarFundosParaResgateFundosDisponiveis[],
  valorMinimo: number,
): Resgate.TErrosValidacaoFundosResgate => {
  return fundos.reduce<Resgate.TErrosValidacaoFundosResgate>((acc, fundo) => {
    const valorRetirarNumerico = parseFloat(
      Resgate.tryGetValueOrDefault([fundo.valorRetirar], '0'),
    );
    const path = Resgate.PATHS_VALIDACAO_FORM_FUNDOS_RESGATE.valorRetirar(
      fundo.codigoFundo,
    );

    if (!fundo.selecionado) return acc;

    const adicionarErro = (
      mensagem: string,
    ): { [mensagem: string]: string } => ({
      ...acc,
      [path]: mensagem,
    });

    if (!valorRetirarNumerico) {
      return adicionarErro(
        Resgate.MENSAGENS_VALIDACAO_FORM_FUNDOS_RESGATE.OBRIGATORIO,
      );
    }

    if (Number.isNaN(valorRetirarNumerico)) {
      return adicionarErro(
        Resgate.MENSAGENS_VALIDACAO_FORM_FUNDOS_RESGATE.INVALIDO,
      );
    }

    if (valorRetirarNumerico < valorMinimo) {
      return adicionarErro(
        Resgate.MENSAGENS_VALIDACAO_FORM_FUNDOS_RESGATE.ABAIXO_MINIMO(
          valorMinimo,
        ),
      );
    }

    return acc;
  }, {});
};
