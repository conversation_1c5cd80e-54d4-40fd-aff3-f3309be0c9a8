import {
  ALIQUOTA,
  IMontarResumoAliquotaFactoryRetorno,
  IObterResumoAliquotaSelecionada,
  IObterResumoAliquotaSelecionadaRetorno,
} from '@src/features/financeiro/resgate/exports';

/**
 * Determina qual resumo de alíquota deve ser usado com base nas configurações do usuário e certificado
 *
 * Esta função analisa se o usuário pode editar a alíquota, qual a alíquota atual do certificado,
 * e qual foi a opção selecionada pelo usuário, retornando o resumo correspondente.
 *
 * @param {Object} params - Parâmetros para obtenção do resumo de alíquota
 * @param {boolean} params.indicadorPermiteEditarAliquota - Indica se o usuário pode alterar a alíquota
 * @param {string} params.aliquotaAtual - Alíquota atual do certificado * @param {IMontarResumoAliquotaFactoryRetorno[]} params.resumoAliquotaProgressiva - Resumo da alíquota progressiva
 * @param {IMontarResumoAliquotaFactoryRetorno[]} params.resumoAliquotaRegressiva - Resumo da alíquota regressiva
 * @param {IMontarResumoAliquotaFactoryRetorno[]} params.dadosResumoSelecionado - Dados do resumo da alíquota selecionada pelo usuário
 * @param {string} params.opcaoRegimeTributario - Opção de regime tributário selecionada pelo usuário
 * @returns {IObterResumoAliquotaSelecionadaRetorno} Objeto contendo o resumo adequado e o tipo de alíquota correspondente
 */
export const obterResumoAliquotaSelecionada = ({
  indicadorPermiteEditarAliquota,
  aliquotaAtual,
  resumoAliquotaProgressiva,
  resumoAliquotaRegressiva,
  dadosResumoSelecionado,
  opcaoRegimeTributario,
}: IObterResumoAliquotaSelecionada): IObterResumoAliquotaSelecionadaRetorno => {
  let resumo: IMontarResumoAliquotaFactoryRetorno[] | undefined;
  let tipoAliquota: string = '';

  if (aliquotaAtual === ALIQUOTA.TIPO_REGIME_PROGRESSIVO) {
    resumo = resumoAliquotaProgressiva;
    tipoAliquota = aliquotaAtual;
  }

  if (aliquotaAtual === ALIQUOTA.TIPO_REGIME_REGRESSIVO) {
    resumo = resumoAliquotaRegressiva;
    tipoAliquota = aliquotaAtual;
  }

  if (indicadorPermiteEditarAliquota) {
    resumo = dadosResumoSelecionado;
    tipoAliquota = opcaoRegimeTributario;
  }

  return {
    resumo,
    tipoAliquota,
  };
};
