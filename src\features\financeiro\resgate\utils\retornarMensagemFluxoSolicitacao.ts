import {
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
  IRetornarMensagemFluxoSolicitacao,
} from '@src/features/financeiro/resgate/exports';

/**
 * Processa mensagens de retorno de serviços e configura exibição de mensagens de erro quando necessário
 *
 * Esta função avalia o resultado de uma chamada de serviço, verifica se houve sucesso
 * e, em caso de erro, configura a mensagem de erro apropriada para exibição ao usuário.
 *
 * @template T - Tipo genérico dos dados do serviço
 * @param {Object} params - Parâmetros para processamento da mensagem
 * @param {T} params.dadosServico - Dados retornados pelo serviço
 * @param {Function} params.configurarMensagem - Função para configurar mensagem de erro
 * @param {Object} [params.erroCustom] - Objeto contendo mensagem e chave de erro customizadas
 * @returns {boolean} True se a operação foi bem-sucedida, False se houve erro e uma mensagem foi configurada
 */
export const retornarMensagemFluxoSolicitacao = <T = unknown>({
  dadosServico,
  configurarMensagem,
  erroCustom,
}: IRetornarMensagemFluxoSolicitacao<T>): boolean => {
  const listaMensagens = dadosServico?.mensagens;

  const mensagem: string = tryGetValueOrDefault(
    [listaMensagens?.[0]?.descricao],
    '',
  );

  const mensagemParaExibir: string = tryGetValueOrDefault(
    [erroCustom?.MENSAGEM, mensagem],
    '',
  );

  const chaveErroCustom: string = tryGetValueOrDefault([erroCustom?.CHAVE], '');

  const isSucesso: boolean = checkIfAllItemsAreTrue([
    !!dadosServico?.sucessoBFF,
    !!dadosServico?.sucessoGI,
  ]);

  const isExibirMensagemErro: boolean = checkIfAllItemsAreTrue([
    !isSucesso,
    !!listaMensagens?.length,
  ]);

  if (!isExibirMensagemErro) return true;

  configurarMensagem(chaveErroCustom, mensagemParaExibir);

  return false;
};
